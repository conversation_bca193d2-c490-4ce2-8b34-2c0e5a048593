[{"C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js": "4", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js": "5", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js": "6", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js": "7", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js": "8", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js": "9", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js": "10", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js": "11", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js": "12", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js": "13", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js": "14", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js": "15", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js": "16", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js": "17", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js": "18", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js": "19", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js": "20", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js": "21", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js": "22", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js": "23", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js": "24", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js": "25", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js": "26", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js": "27", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js": "28", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js": "29", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js": "30", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js": "31", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js": "32", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js": "33", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js": "34", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js": "35", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js": "36", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js": "37", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js": "38", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js": "39", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js": "40", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js": "41", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js": "42", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js": "43", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js": "44", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js": "45", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js": "46", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js": "47", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js": "48", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js": "49", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js": "50", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js": "51", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js": "52", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js": "53", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js": "54", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js": "55", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js": "56", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js": "57", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js": "58", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js": "59", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js": "60", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js": "61", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js": "62", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js": "63", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js": "64", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js": "65", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js": "66", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js": "67", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js": "68", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js": "69", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js": "70", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js": "71", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js": "72", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js": "73"}, {"size": 676, "mtime": 1754170368613, "results": "74", "hashOfConfig": "75"}, {"size": 11752, "mtime": 1754225529754, "results": "76", "hashOfConfig": "75"}, {"size": 656, "mtime": 1752859285013, "results": "77", "hashOfConfig": "75"}, {"size": 5622, "mtime": 1751990678109, "results": "78", "hashOfConfig": "75"}, {"size": 3723, "mtime": 1750871433201, "results": "79", "hashOfConfig": "75"}, {"size": 455, "mtime": 1750865324399, "results": "80", "hashOfConfig": "75"}, {"size": 6151, "mtime": 1750873153170, "results": "81", "hashOfConfig": "75"}, {"size": 1687, "mtime": 1753356431070, "results": "82", "hashOfConfig": "75"}, {"size": 694, "mtime": 1747594139572, "results": "83", "hashOfConfig": "75"}, {"size": 26035, "mtime": 1754228407427, "results": "84", "hashOfConfig": "75"}, {"size": 420, "mtime": 1747844523996, "results": "85", "hashOfConfig": "75"}, {"size": 15074, "mtime": 1754231438118, "results": "86", "hashOfConfig": "75"}, {"size": 2240, "mtime": 1750873583618, "results": "87", "hashOfConfig": "75"}, {"size": 491, "mtime": 1747844524003, "results": "88", "hashOfConfig": "75"}, {"size": 850, "mtime": 1750872193880, "results": "89", "hashOfConfig": "75"}, {"size": 4575, "mtime": 1750883025232, "results": "90", "hashOfConfig": "75"}, {"size": 3513, "mtime": 1754226913750, "results": "91", "hashOfConfig": "75"}, {"size": 422, "mtime": 1747844523996, "results": "92", "hashOfConfig": "75"}, {"size": 478, "mtime": 1751986578114, "results": "93", "hashOfConfig": "75"}, {"size": 55726, "mtime": 1754228454531, "results": "94", "hashOfConfig": "75"}, {"size": 1026, "mtime": 1750873436634, "results": "95", "hashOfConfig": "75"}, {"size": 7668, "mtime": 1754226432664, "results": "96", "hashOfConfig": "75"}, {"size": 15134, "mtime": 1752581112327, "results": "97", "hashOfConfig": "75"}, {"size": 4159, "mtime": 1750777638880, "results": "98", "hashOfConfig": "75"}, {"size": 19519, "mtime": 1753294211583, "results": "99", "hashOfConfig": "75"}, {"size": 153, "mtime": 1745582315206, "results": "100", "hashOfConfig": "75"}, {"size": 284, "mtime": 1750858318977, "results": "101", "hashOfConfig": "75"}, {"size": 2909, "mtime": 1750887085865, "results": "102", "hashOfConfig": "75"}, {"size": 11933, "mtime": 1752069953530, "results": "103", "hashOfConfig": "75"}, {"size": 1693, "mtime": 1750850985128, "results": "104", "hashOfConfig": "75"}, {"size": 25200, "mtime": 1754228296213, "results": "105", "hashOfConfig": "75"}, {"size": 11596, "mtime": 1750887959421, "results": "106", "hashOfConfig": "75"}, {"size": 6487, "mtime": 1754234361716, "results": "107", "hashOfConfig": "75"}, {"size": 12775, "mtime": 1754232445497, "results": "108", "hashOfConfig": "75"}, {"size": 5959, "mtime": 1754234404419, "results": "109", "hashOfConfig": "75"}, {"size": 22630, "mtime": 1754234211064, "results": "110", "hashOfConfig": "75"}, {"size": 8204, "mtime": 1750890990379, "results": "111", "hashOfConfig": "75"}, {"size": 5728, "mtime": 1754227046107, "results": "112", "hashOfConfig": "75"}, {"size": 3301, "mtime": 1752068170287, "results": "113", "hashOfConfig": "75"}, {"size": 11930, "mtime": 1754232528384, "results": "114", "hashOfConfig": "75"}, {"size": 22662, "mtime": 1754231601599, "results": "115", "hashOfConfig": "75"}, {"size": 44083, "mtime": 1754230261888, "results": "116", "hashOfConfig": "75"}, {"size": 22967, "mtime": 1754235230043, "results": "117", "hashOfConfig": "75"}, {"size": 10032, "mtime": 1754232575371, "results": "118", "hashOfConfig": "75"}, {"size": 9398, "mtime": 1754232600668, "results": "119", "hashOfConfig": "75"}, {"size": 14054, "mtime": 1754232656978, "results": "120", "hashOfConfig": "75"}, {"size": 665, "mtime": 1753136360410, "results": "121", "hashOfConfig": "75"}, {"size": 56, "mtime": 1750504542636, "results": "122", "hashOfConfig": "75"}, {"size": 751, "mtime": 1748635026074, "results": "123", "hashOfConfig": "75"}, {"size": 2964, "mtime": 1752068241528, "results": "124", "hashOfConfig": "75"}, {"size": 620, "mtime": 1747594139559, "results": "125", "hashOfConfig": "75"}, {"size": 1393, "mtime": 1750866339097, "results": "126", "hashOfConfig": "75"}, {"size": 3083, "mtime": 1751985930466, "results": "127", "hashOfConfig": "75"}, {"size": 4753, "mtime": 1751985930121, "results": "128", "hashOfConfig": "75"}, {"size": 10163, "mtime": 1754226336011, "results": "129", "hashOfConfig": "75"}, {"size": 4696, "mtime": 1751985929811, "results": "130", "hashOfConfig": "75"}, {"size": 25917, "mtime": 1754237448741, "results": "131", "hashOfConfig": "75"}, {"size": 7155, "mtime": 1751298764731, "results": "132", "hashOfConfig": "75"}, {"size": 5300, "mtime": 1754230784206, "results": "133", "hashOfConfig": "75"}, {"size": 897, "mtime": 1747061904678, "results": "134", "hashOfConfig": "75"}, {"size": 22585, "mtime": 1754226213157, "results": "135", "hashOfConfig": "75"}, {"size": 5376, "mtime": 1748517649309, "results": "136", "hashOfConfig": "75"}, {"size": 5485, "mtime": 1753034961627, "results": "137", "hashOfConfig": "75"}, {"size": 890, "mtime": 1749741250195, "results": "138", "hashOfConfig": "75"}, {"size": 4625, "mtime": 1753034977418, "results": "139", "hashOfConfig": "75"}, {"size": 2175, "mtime": 1753370053964, "results": "140", "hashOfConfig": "75"}, {"size": 13054, "mtime": 1754225741207, "results": "141", "hashOfConfig": "75"}, {"size": 23662, "mtime": 1754228144753, "results": "142", "hashOfConfig": "75"}, {"size": 23365, "mtime": 1753369624191, "results": "143", "hashOfConfig": "75"}, {"size": 10918, "mtime": 1754230033679, "results": "144", "hashOfConfig": "75"}, {"size": 56011, "mtime": 1754229884974, "results": "145", "hashOfConfig": "75"}, {"size": 1223, "mtime": 1751656822978, "results": "146", "hashOfConfig": "75"}, {"size": 2878, "mtime": 1751659811949, "results": "147", "hashOfConfig": "75"}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16w30kw", {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js", [], []]