{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\EditProfilePage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Container, TextField, Button, Typography, Paper, Grid, CircularProgress, Alert, Box, Avatar, IconButton, Chip, Divider, Stack, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from \"@mui/material\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { ArrowBack, PhotoCamera, Email, Phone, LocationOn, Work, Person, Check, Add, Lock, Visibility, VisibilityOff } from \"@mui/icons-material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditProfilePage = () => {\n  _s();\n  var _user$name, _user$email;\n  const {\n    t\n  } = useTranslation();\n  const {\n    email\n  } = useParams();\n  const navigate = useNavigate();\n  const [user, setUser] = useState(null);\n  const [form, setForm] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [preview, setPreview] = useState(\"\");\n  const [newSkill, setNewSkill] = useState(\"\");\n  const [skills, setSkills] = useState([]);\n  const [showSkillsDropdown, setShowSkillsDropdown] = useState(false);\n  const predefinedSkillKeys = [\"javascript\", \"python\", \"java\", \"cpp\", \"csharp\", \"php\", \"ruby\", \"go\", \"rust\", \"swift\", \"kotlin\", \"scala\", \"r\", \"matlab\", \"perl\", \"react\", \"vuejs\", \"angular\", \"htmlcss\", \"sass\", \"less\", \"bootstrap\", \"tailwind\", \"jquery\", \"webpack\", \"vite\", \"nodejs\", \"expressjs\", \"django\", \"flask\", \"springboot\", \"laravel\", \"rails\", \"aspnet\", \"fastapi\", \"mysql\", \"postgresql\", \"mongodb\", \"redis\", \"sqlite\", \"oracle\", \"sqlserver\", \"cassandra\", \"dynamodb\", \"firebase\", \"aws\", \"azure\", \"googlecloud\", \"docker\", \"kubernetes\", \"jenkins\", \"gitlabci\", \"githubactions\", \"terraform\", \"ansible\", \"reactnative\", \"flutter\", \"iosdev\", \"androiddev\", \"xamarin\", \"ionic\", \"dataanalysis\", \"machinelearning\", \"deeplearning\", \"ai\", \"tensorflow\", \"pytorch\", \"pandas\", \"numpy\", \"tableau\", \"powerbi\", \"uiuxdesign\", \"figma\", \"adobexd\", \"sketch\", \"photoshop\", \"illustrator\", \"indesign\", \"aftereffects\", \"blender\", \"digitalmarketing\", \"seo\", \"sem\", \"socialmedia\", \"contentmarketing\", \"emailmarketing\", \"googleanalytics\", \"facebookads\", \"projectmanagement\", \"agile\", \"scrum\", \"kanban\", \"jira\", \"trello\", \"asana\", \"mondaycom\", \"slack\", \"git\", \"linux\", \"windowsserver\", \"cybersecurity\", \"blockchain\", \"iot\", \"apidev\", \"microservices\", \"graphql\", \"restapi\", \"leadership\", \"communication\", \"problemsolving\", \"criticalthinking\", \"teammanagement\", \"publicspeaking\", \"negotiation\", \"english\", \"french\", \"spanish\", \"german\", \"arabic\", \"chinese\", \"japanese\", \"portuguese\", \"italian\", \"russian\", \"ecommerce\", \"fintech\", \"healthtech\", \"edtech\", \"gaming\", \"automotive\", \"realestate\", \"logistics\", \"retail\", \"contentwriting\", \"copywriting\", \"technicalwriting\", \"blogwriting\", \"socialcontent\", \"videoediting\", \"podcasting\"];\n  const predefinedSkills = predefinedSkillKeys.map(key => t(`skills.${key}`));\n  const [imageQuality, setImageQuality] = useState(null);\n  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);\n\n  // États pour le changement de mot de passe\n  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);\n  const [passwordForm, setPasswordForm] = useState({\n    currentPassword: \"\",\n    newPassword: \"\",\n    confirmPassword: \"\"\n  });\n  const [passwordError, setPasswordError] = useState(\"\");\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [changingPassword, setChangingPassword] = useState(false);\n  const [passwordStrength, setPasswordStrength] = useState(\"\");\n\n  // Fonction pour traduire le rôle\n  const translateRole = role => {\n    if (!role) return t('role.etudiant');\n    const roleKey = role.toLowerCase();\n    return t(`role.${roleKey}`);\n  };\n\n  // Fonction pour évaluer la force du mot de passe\n  const getPasswordStrength = password => {\n    if (!password) return \"\";\n    let strength = 0;\n\n    // Critères d'évaluation\n    const lengthCriteria = password.length >= 8;\n    const hasUpperCase = /[A-Z]/.test(password);\n    const hasLowerCase = /[a-z]/.test(password);\n    const hasDigit = /\\d/.test(password);\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n\n    // Calcul du score\n    strength += lengthCriteria ? 1 : 0;\n    strength += hasUpperCase ? 1 : 0;\n    strength += hasLowerCase ? 1 : 0;\n    strength += hasDigit ? 1 : 0;\n    strength += hasSpecialChar ? 1 : 0;\n\n    // Détermination de la force\n    if (strength <= 2) return t('profile.passwordStrengthWeak');\n    if (strength <= 4) return t('profile.passwordStrengthMedium');\n    return t('profile.passwordStrengthStrong');\n  };\n\n  // Fonction pour analyser la qualité de l'image (détection de flou)\n  const analyzeImageQuality = file => {\n    return new Promise(resolve => {\n      const img = new Image();\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      img.onload = () => {\n        // Redimensionner l'image pour l'analyse (pour des performances optimales)\n        const maxSize = 500;\n        let {\n          width,\n          height\n        } = img;\n        if (width > height) {\n          if (width > maxSize) {\n            height = height * maxSize / width;\n            width = maxSize;\n          }\n        } else {\n          if (height > maxSize) {\n            width = width * maxSize / height;\n            height = maxSize;\n          }\n        }\n        canvas.width = width;\n        canvas.height = height;\n        ctx.drawImage(img, 0, 0, width, height);\n\n        // Obtenir les données de l'image\n        const imageData = ctx.getImageData(0, 0, width, height);\n        const data = imageData.data;\n\n        // Calculer la variance des gradients (méthode de détection de flou)\n        let sum = 0;\n        let sumSquared = 0;\n        let count = 0;\n\n        // Calculer les gradients horizontaux et verticaux\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            const idx = (y * width + x) * 4;\n\n            // Convertir en niveaux de gris (variable non utilisée)\n            // const gray = 0.299 * data[idx] + 0.587 * data[idx + 1] + 0.114 * data[idx + 2];\n\n            // Gradient horizontal\n            const grayLeft = 0.299 * data[idx - 4] + 0.587 * data[idx - 3] + 0.114 * data[idx - 2];\n            const grayRight = 0.299 * data[idx + 4] + 0.587 * data[idx + 5] + 0.114 * data[idx + 6];\n            const gradX = grayRight - grayLeft;\n\n            // Gradient vertical\n            const grayTop = 0.299 * data[idx - width * 4] + 0.587 * data[idx - width * 4 + 1] + 0.114 * data[idx - width * 4 + 2];\n            const grayBottom = 0.299 * data[idx + width * 4] + 0.587 * data[idx + width * 4 + 1] + 0.114 * data[idx + width * 4 + 2];\n            const gradY = grayBottom - grayTop;\n\n            // Magnitude du gradient\n            const magnitude = Math.sqrt(gradX * gradX + gradY * gradY);\n            sum += magnitude;\n            sumSquared += magnitude * magnitude;\n            count++;\n          }\n        }\n\n        // Calculer la variance\n        const mean = sum / count;\n        const variance = sumSquared / count - mean * mean;\n\n        // Déterminer la qualité basée sur la variance\n        // Plus la variance est élevée, plus l'image est nette\n        let quality;\n        if (variance > 1000) {\n          quality = {\n            level: 'excellent',\n            score: variance,\n            isBlurry: false\n          };\n        } else if (variance > 500) {\n          quality = {\n            level: 'good',\n            score: variance,\n            isBlurry: false\n          };\n        } else if (variance > 200) {\n          quality = {\n            level: 'acceptable',\n            score: variance,\n            isBlurry: false\n          };\n        } else if (variance > 100) {\n          quality = {\n            level: 'poor',\n            score: variance,\n            isBlurry: true\n          };\n        } else {\n          quality = {\n            level: 'very_poor',\n            score: variance,\n            isBlurry: true\n          };\n        }\n        resolve(quality);\n      };\n      img.onerror = () => {\n        resolve({\n          level: 'error',\n          score: 0,\n          isBlurry: true\n        });\n      };\n      img.src = URL.createObjectURL(file);\n    });\n  };\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (!event.target.closest('.skills-dropdown-container')) {\n        setShowSkillsDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const fetchUser = async () => {\n      try {\n        setLoading(true);\n\n        // Vérifier d'abord si nous avons un utilisateur à éditer dans sessionStorage\n        // (cela serait défini lorsqu'on clique sur \"Edit\" dans la liste des utilisateurs)\n        const editingUserStr = sessionStorage.getItem(\"editingUser\");\n        if (editingUserStr) {\n          try {\n            const editingUser = JSON.parse(editingUserStr);\n            console.log(\"Found user to edit in sessionStorage:\", editingUser);\n            if (editingUser && editingUser.email) {\n              // Utiliser cet utilisateur comme point de départ\n              setUser(editingUser);\n              setForm(editingUser);\n\n              // Gérer les skills\n              if (editingUser.skills) {\n                if (typeof editingUser.skills === 'string') {\n                  try {\n                    const parsedSkills = JSON.parse(editingUser.skills);\n                    setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\n                  } catch (parseErr) {\n                    console.error(\"Error parsing skills:\", parseErr);\n                    setSkills([]);\n                  }\n                } else if (Array.isArray(editingUser.skills)) {\n                  setSkills(editingUser.skills);\n                } else {\n                  setSkills([]);\n                }\n              } else {\n                setSkills([]);\n              }\n\n              // Récupérer les données complètes depuis le serveur\n              try {\n                console.log(\"Fetching complete user data for email:\", editingUser.email);\n                const res = await axios.get(`http://localhost:8000/users/email/${editingUser.email}`);\n                console.log(\"Complete user data received:\", res.data);\n                if (res.data) {\n                  setUser(res.data);\n                  setForm(res.data);\n\n                  // Mettre à jour les skills avec les données du serveur\n                  if (res.data.skills) {\n                    if (typeof res.data.skills === 'string') {\n                      try {\n                        const parsedSkills = JSON.parse(res.data.skills);\n                        setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\n                      } catch (parseErr) {\n                        console.error(\"Error parsing skills from server:\", parseErr);\n                      }\n                    } else if (Array.isArray(res.data.skills)) {\n                      setSkills(res.data.skills);\n                    }\n                  }\n                }\n              } catch (serverErr) {\n                console.error(\"Error fetching complete user data:\", serverErr);\n                // Continuer avec les données de sessionStorage\n              }\n\n              // Nettoyer sessionStorage après utilisation\n              sessionStorage.removeItem(\"editingUser\");\n              setLoading(false);\n              return; // Sortir de la fonction car nous avons déjà les données\n            }\n          } catch (parseErr) {\n            console.error(\"Error parsing editing user from sessionStorage:\", parseErr);\n          }\n        }\n\n        // Si nous n'avons pas trouvé d'utilisateur à éditer dans sessionStorage,\n        // continuer avec la logique existante basée sur l'email de l'URL\n\n        // Si l'email n'est pas fourni dans l'URL, essayer de le récupérer depuis le localStorage\n        let userEmail = email;\n        if (!userEmail) {\n          console.log(\"Email not provided in URL, trying to get it from localStorage\");\n          const storedUser = localStorage.getItem(\"user\");\n          if (storedUser) {\n            try {\n              const userData = JSON.parse(storedUser);\n              if (userData && userData.email) {\n                userEmail = userData.email;\n                console.log(\"Using email from localStorage:\", userEmail);\n              }\n            } catch (err) {\n              console.error(\"Error parsing user data from localStorage:\", err);\n            }\n          }\n        }\n        if (!userEmail) {\n          throw new Error(\"No email available to fetch user data\");\n        }\n        try {\n          console.log(\"Fetching user data for email:\", userEmail);\n          const res = await axios.get(`http://localhost:8000/users/email/${userEmail}`);\n          console.log(\"User data received:\", res.data);\n          if (!res.data) {\n            throw new Error(\"No user data received from server\");\n          }\n          setUser(res.data);\n          setForm(res.data);\n\n          // Gérer les skills correctement\n          if (res.data.skills) {\n            // Si skills est une chaîne de caractères, essayer de la parser\n            if (typeof res.data.skills === 'string') {\n              try {\n                const parsedSkills = JSON.parse(res.data.skills);\n                setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\n              } catch (parseErr) {\n                console.error(\"Error parsing skills:\", parseErr);\n                setSkills([]);\n              }\n            } else if (Array.isArray(res.data.skills)) {\n              setSkills(res.data.skills);\n            } else {\n              setSkills([]);\n            }\n          } else {\n            setSkills([]);\n          }\n        } catch (fetchErr) {\n          console.error(\"Error fetching user data:\", fetchErr);\n\n          // Si l'utilisateur est dans le localStorage, l'utiliser comme solution de secours\n          const storedUser = localStorage.getItem(\"user\");\n          if (storedUser) {\n            try {\n              const userData = JSON.parse(storedUser);\n              console.log(\"Using user data from localStorage as fallback:\", userData);\n              setUser(userData);\n              setForm(userData);\n              if (userData.skills) {\n                setSkills(Array.isArray(userData.skills) ? userData.skills : []);\n              } else {\n                setSkills([]);\n              }\n            } catch (parseErr) {\n              console.error(\"Error parsing user data from localStorage:\", parseErr);\n              throw new Error(\"Failed to load user data\");\n            }\n          } else {\n            throw new Error(\"Failed to fetch user data and no local data available\");\n          }\n        }\n      } catch (err) {\n        console.error(\"Error loading user:\", err);\n        setError(`Error loading profile: ${err.message}`);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUser();\n  }, [email, navigate]);\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleFileChange = async e => {\n    const file = e.target.files[0];\n    if (!file) {\n      setSelectedFile(null);\n      setPreview(\"\");\n      setImageQuality(null);\n      return;\n    }\n\n    // Vérifier le type de fichier\n    if (!file.type.startsWith('image/')) {\n      toast.error(t('profile.invalidImageFile'));\n      e.target.value = '';\n      return;\n    }\n\n    // Vérifier la taille du fichier (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error(t('profile.fileSizeError'));\n      e.target.value = '';\n      return;\n    }\n    setPreview(URL.createObjectURL(file));\n    setIsAnalyzingImage(true);\n    setImageQuality(null);\n    try {\n      // Analyser la qualité de l'image\n      const quality = await analyzeImageQuality(file);\n      setImageQuality(quality);\n      if (quality.isBlurry) {\n        toast.warning(t('profile.blurryImageWarning', {\n          quality: quality.level\n        }));\n        // Ne pas définir le fichier automatiquement si l'image est floue\n        setSelectedFile(null);\n      } else {\n        toast.success(t('profile.goodQualityImage', {\n          quality: quality.level\n        }));\n        setSelectedFile(file);\n      }\n    } catch (error) {\n      console.error(\"Erreur lors de l'analyse de l'image:\", error);\n      toast.error(t('profile.imageAnalysisError'));\n      setImageQuality({\n        level: 'error',\n        score: 0,\n        isBlurry: true\n      });\n      setSelectedFile(null);\n    } finally {\n      setIsAnalyzingImage(false);\n    }\n  };\n  const handleAddSkill = (skillToAdd = null) => {\n    const skill = skillToAdd || newSkill.trim();\n    if (skill && !skills.includes(skill)) {\n      setSkills([...skills, skill]);\n      setNewSkill(\"\");\n      setShowSkillsDropdown(false);\n    }\n  };\n  const filteredSkills = predefinedSkills.filter(skill => skill.toLowerCase().includes(newSkill.toLowerCase()) && !skills.includes(skill));\n  const handleRemoveSkill = skillToRemove => {\n    setSkills(skills.filter(skill => skill !== skillToRemove));\n  };\n\n  // Fonction pour forcer l'utilisation d'une image floue\n  const handleForceUseBlurryImage = () => {\n    const fileInput = document.querySelector('input[type=\"file\"]');\n    if (fileInput && fileInput.files[0]) {\n      setSelectedFile(fileInput.files[0]);\n      toast.info(t('profile.blurryImageAccepted'));\n    }\n  };\n\n  // Fonctions pour le changement de mot de passe\n  const handlePasswordDialogOpen = () => {\n    setPasswordDialogOpen(true);\n    setPasswordForm({\n      currentPassword: \"\",\n      newPassword: \"\",\n      confirmPassword: \"\"\n    });\n    setPasswordError(\"\");\n  };\n  const handlePasswordDialogClose = () => {\n    setPasswordDialogOpen(false);\n    setPasswordError(\"\");\n  };\n  const handlePasswordChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setPasswordForm({\n      ...passwordForm,\n      [name]: value\n    });\n\n    // Évaluer la force du mot de passe si c'est le champ newPassword qui est modifié\n    if (name === \"newPassword\") {\n      setPasswordStrength(getPasswordStrength(value));\n    }\n  };\n  const handleChangePassword = async () => {\n    // Validation\n    if (!passwordForm.currentPassword) {\n      setPasswordError(t('profile.currentPasswordRequired'));\n      return;\n    }\n    if (!passwordForm.newPassword) {\n      setPasswordError(t('profile.newPasswordRequired'));\n      return;\n    }\n    if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n      setPasswordError(t('profile.passwordsDoNotMatch'));\n      return;\n    }\n    if (passwordForm.newPassword.length < 6) {\n      setPasswordError(t('profile.passwordTooShort'));\n      return;\n    }\n    setChangingPassword(true);\n    setPasswordError(\"\");\n    try {\n      // Appel API pour changer le mot de passe\n      await axios.post(`http://localhost:8000/auth/change-password`, {\n        email: user.email,\n        currentPassword: passwordForm.currentPassword,\n        newPassword: passwordForm.newPassword,\n        sendNotification: true\n      });\n      toast.success(t('profile.passwordChangedSuccess'));\n      toast.info(t('profile.passwordChangeEmailSent'));\n      handlePasswordDialogClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error changing password:\", error);\n      setPasswordError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || t('profile.passwordChangeError'));\n    } finally {\n      setChangingPassword(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError(\"\");\n\n    // Vérifier la qualité de l'image avant la soumission\n    if (selectedFile && imageQuality && imageQuality.isBlurry) {\n      const confirmUpload = window.confirm(t('profile.confirmBlurryUpload', {\n        quality: imageQuality.level\n      }));\n      if (!confirmUpload) {\n        setSubmitting(false);\n        return;\n      }\n    }\n    try {\n      // Utiliser l'email de l'utilisateur chargé plutôt que celui de l'URL\n      const userEmail = user.email;\n      let userId = user.id;\n\n      // Vérifier si l'ID est une chaîne de caractères et la convertir en nombre si nécessaire\n      if (typeof userId === 'string') {\n        userId = parseInt(userId, 10);\n        if (isNaN(userId)) {\n          console.error(\"Invalid user ID format:\", user.id);\n          // Essayer de récupérer l'ID depuis la réponse API\n          try {\n            const userResponse = await axios.get(`http://localhost:8000/users/email/${userEmail}`);\n            if (userResponse.data && userResponse.data.id) {\n              userId = userResponse.data.id;\n              console.log(\"Retrieved user ID from API:\", userId);\n            }\n          } catch (idErr) {\n            console.error(\"Failed to retrieve user ID from API:\", idErr);\n          }\n        }\n      }\n      if (!userEmail) {\n        throw new Error(\"No email available to update user data\");\n      }\n      console.log(\"Updating user data for email:\", userEmail, \"with ID:\", userId);\n\n      // Préparer les données utilisateur à mettre à jour\n      // S'assurer que skills est bien un tableau\n      let formattedSkills = skills;\n      if (!Array.isArray(formattedSkills)) {\n        if (typeof formattedSkills === 'string') {\n          try {\n            // Essayer de parser si c'est une chaîne JSON\n            formattedSkills = JSON.parse(formattedSkills);\n            if (!Array.isArray(formattedSkills)) {\n              formattedSkills = [formattedSkills]; // Convertir en tableau si ce n'est pas déjà un tableau\n            }\n          } catch (e) {\n            // Si ce n'est pas du JSON valide, le traiter comme une chaîne simple\n            formattedSkills = [formattedSkills];\n          }\n        } else if (formattedSkills) {\n          // Si c'est une valeur non-null/undefined mais pas un tableau ou une chaîne\n          formattedSkills = [String(formattedSkills)];\n        } else {\n          // Si c'est null ou undefined\n          formattedSkills = [];\n        }\n      }\n\n      // Filtrer les valeurs vides ou nulles\n      formattedSkills = formattedSkills.filter(skill => skill && skill.trim());\n      console.log(\"Formatted skills:\", formattedSkills);\n      const userData = {\n        name: form.name || null,\n        phone: form.phone || null,\n        location: form.location || null,\n        about: form.about || null,\n        skills: formattedSkills // Le backend s'attend à un tableau\n      };\n      console.log(\"User data to update:\", userData);\n      try {\n        // Mettre à jour les données utilisateur\n        const updateResponse = await axios.patch(`http://localhost:8000/users/email/${userEmail}`, userData);\n        console.log(\"Update response:\", updateResponse.data);\n        if (!updateResponse.data) {\n          throw new Error(\"No data received from server after update\");\n        }\n\n        // Mettre à jour l'utilisateur avec les données de la réponse\n        const updatedUser = updateResponse.data;\n        setUser(updatedUser);\n\n        // Mettre à jour les données utilisateur dans le localStorage et sessionStorage\n        // seulement si l'utilisateur connecté est celui qui est en train d'être modifié\n        const storedUser = localStorage.getItem(\"user\");\n        if (storedUser) {\n          try {\n            const storedUserData = JSON.parse(storedUser);\n\n            // Vérifier si l'utilisateur connecté est celui qui est en train d'être modifié\n            if (storedUserData.email === userEmail) {\n              // Utiliser les données de la réponse pour mettre à jour le localStorage\n              const updatedUserData = {\n                ...storedUserData,\n                name: updatedUser.name,\n                phone: updatedUser.phone,\n                location: updatedUser.location,\n                about: updatedUser.about,\n                skills: updatedUser.skills,\n                profilePic: updatedUser.profilePic\n              };\n              localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n              console.log(\"Updated user data in localStorage:\", updatedUserData);\n\n              // Mettre à jour également dans sessionStorage si présent\n              const sessionUser = sessionStorage.getItem(\"user\");\n              if (sessionUser) {\n                try {\n                  const sessionUserData = JSON.parse(sessionUser);\n                  if (sessionUserData.email === userEmail) {\n                    sessionStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                    console.log(\"Updated user data in sessionStorage\");\n                  }\n                } catch (err) {\n                  console.error(\"Error updating user data in sessionStorage:\", err);\n                }\n              }\n            } else {\n              console.log(\"User being edited is not the logged-in user, not updating localStorage\");\n            }\n          } catch (err) {\n            console.error(\"Error updating user data in localStorage:\", err);\n          }\n        }\n\n        // Télécharger la photo de profil si sélectionnée\n        if (selectedFile && userId) {\n          try {\n            console.log(\"Uploading profile picture for user ID:\", userId);\n            const formData = new FormData();\n            formData.append(\"photo\", selectedFile);\n\n            // Ajouter un log pour déboguer\n            console.log(\"Sending photo upload request to:\", `http://localhost:8000/users/id/${userId}/photo`);\n            console.log(\"With form data:\", selectedFile.name);\n            const photoResponse = await axios.patch(`http://localhost:8000/users/id/${userId}/photo`, formData, {\n              headers: {\n                \"Content-Type\": \"multipart/form-data\"\n              }\n            });\n            console.log(\"Photo upload response:\", photoResponse.data);\n            if (photoResponse.data && photoResponse.data.profilePic) {\n              // Mettre à jour l'utilisateur avec la nouvelle photo\n              setUser(prev => ({\n                ...prev,\n                profilePic: photoResponse.data.profilePic\n              }));\n\n              // Mettre à jour le localStorage avec la nouvelle photo si c'est l'utilisateur connecté\n              const storedUser = localStorage.getItem(\"user\");\n              if (storedUser) {\n                try {\n                  const storedUserData = JSON.parse(storedUser);\n                  if (storedUserData.email === userEmail) {\n                    storedUserData.profilePic = photoResponse.data.profilePic;\n                    localStorage.setItem(\"user\", JSON.stringify(storedUserData));\n                    console.log(\"Updated profile picture in localStorage\");\n\n                    // Mettre à jour également dans sessionStorage si présent\n                    const sessionUser = sessionStorage.getItem(\"user\");\n                    if (sessionUser) {\n                      try {\n                        const sessionUserData = JSON.parse(sessionUser);\n                        if (sessionUserData.email === userEmail) {\n                          sessionUserData.profilePic = photoResponse.data.profilePic;\n                          sessionStorage.setItem(\"user\", JSON.stringify(sessionUserData));\n                          console.log(\"Updated profile picture in sessionStorage\");\n                        }\n                      } catch (err) {\n                        console.error(\"Error updating profile picture in sessionStorage:\", err);\n                      }\n                    }\n                  }\n                } catch (err) {\n                  console.error(\"Error updating profile picture in localStorage:\", err);\n                }\n              }\n            }\n          } catch (photoErr) {\n            console.error(\"Error uploading profile picture:\", photoErr);\n            toast.error(t('profile.profileUpdatePartialError'));\n          }\n        }\n        toast.success(t('profile.profileUpdatedSuccess'));\n\n        // Rafraîchir la page principale et naviguer vers la page de profil\n        setTimeout(() => {\n          // Forcer un rafraîchissement des données utilisateur dans l'application principale\n          window.dispatchEvent(new CustomEvent('userProfileUpdated', {\n            detail: {\n              updatedUser: updatedUser || user\n            }\n          }));\n\n          // Option 1: Navigation normale avec rafraîchissement des données\n          if (updatedUser && updatedUser.id) {\n            navigate(`/ProfilePage/${updatedUser.id}`);\n            // Forcer un rafraîchissement de la page après navigation pour s'assurer que tout est à jour\n            setTimeout(() => {\n              window.location.reload();\n            }, 100);\n          } else if (userId) {\n            navigate(`/ProfilePage/${userId}`);\n            // Forcer un rafraîchissement de la page après navigation\n            setTimeout(() => {\n              window.location.reload();\n            }, 100);\n          } else {\n            // Si l'ID n'est pas disponible, essayer de récupérer l'utilisateur depuis le localStorage\n            const storedUser = localStorage.getItem(\"user\");\n            if (storedUser) {\n              try {\n                const userData = JSON.parse(storedUser);\n                if (userData && userData.id) {\n                  navigate(`/ProfilePage/${userData.id}`);\n                  // Forcer un rafraîchissement de la page après navigation\n                  setTimeout(() => {\n                    window.location.reload();\n                  }, 100);\n                  return;\n                }\n              } catch (err) {\n                console.error(\"Error parsing user data from localStorage:\", err);\n              }\n            }\n            // Si tout échoue, naviguer vers la page d'accueil et rafraîchir\n            navigate(\"/\");\n            window.location.reload();\n          }\n        }, 1500);\n      } catch (updateErr) {\n        var _updateErr$response, _updateErr$response$d;\n        console.error(\"Error updating user data:\", updateErr);\n        setError(`Update failed: ${((_updateErr$response = updateErr.response) === null || _updateErr$response === void 0 ? void 0 : (_updateErr$response$d = _updateErr$response.data) === null || _updateErr$response$d === void 0 ? void 0 : _updateErr$response$d.message) || updateErr.message}`);\n        toast.error(t('profile.profileUpdateError'));\n      }\n    } catch (err) {\n      console.error(\"Update error:\", err);\n      setError(`Update failed: ${err.message}`);\n      toast.error(t('profile.profileUpdateError'));\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '80vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 7\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      sx: {\n        mt: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 4,\n          borderRadius: 4,\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 600\n          },\n          children: t('profile.profileError')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3\n          },\n          children: error || t('profile.userNotFound')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          justifyContent: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => window.location.reload(),\n            children: t('common.tryAgain')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => navigate('/'),\n            children: t('common.goHome')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 832,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    sx: {\n      py: 6\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 4,\n      sx: {\n        p: 6,\n        borderRadius: 6,\n        background: 'linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          sx: {\n            fontWeight: 700,\n            background: 'linear-gradient(45deg, #1976d2 30%, #2196f3 90%)',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent'\n          },\n          children: t('profile.editProfile')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            // Naviguer vers la page de profil avec l'ID de l'utilisateur\n            if (user && user.id) {\n              navigate(`/ProfilePage/${user.id}`);\n            } else {\n              // Si l'ID n'est pas disponible, essayer de récupérer l'utilisateur depuis le localStorage\n              const storedUser = localStorage.getItem(\"user\");\n              if (storedUser) {\n                try {\n                  const userData = JSON.parse(storedUser);\n                  if (userData && userData.id) {\n                    navigate(`/ProfilePage/${userData.id}`);\n                    return;\n                  }\n                } catch (err) {\n                  console.error(\"Error parsing user data from localStorage:\", err);\n                }\n              }\n              // Si tout échoue, naviguer vers la page d'accueil\n              navigate(\"/\");\n            }\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 24\n          }, this),\n          variant: \"outlined\",\n          sx: {\n            borderRadius: 20,\n            px: 3\n          },\n          children: t('profile.backToProfile')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: preview || (user.profilePic ? user.profilePic.startsWith('/profile-pics/') ? `http://localhost:8000/uploads${user.profilePic}` : user.profilePic.startsWith('http') ? user.profilePic : `http://localhost:8000/uploads/profile-pics/${user.profilePic.split('/').pop()}` : null),\n            sx: {\n              width: 150,\n              height: 150,\n              fontSize: 60,\n              border: '4px solid #1976d2',\n              boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)'\n            },\n            children: ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.charAt(0).toUpperCase()) || \"U\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"primary\",\n            component: \"label\",\n            sx: {\n              position: 'absolute',\n              bottom: 0,\n              right: 0,\n              bgcolor: 'background.paper',\n              '&:hover': {\n                bgcolor: 'action.hover'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(PhotoCamera, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              hidden: true,\n              onChange: handleFileChange,\n              accept: \"image/*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          sx: {\n            mt: 1\n          },\n          children: t('profile.clickCameraToChange')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 11\n        }, this), isAnalyzingImage && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20,\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"primary\",\n            children: t('profile.analyzingImageQuality')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 13\n        }, this), imageQuality && !isAnalyzingImage && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: imageQuality.isBlurry ? \"warning\" : \"success\",\n            sx: {\n              display: 'inline-flex',\n              alignItems: 'center',\n              maxWidth: 400\n            },\n            children: imageQuality.isBlurry ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500\n                },\n                children: t('profile.blurryImageDetected')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5\n                },\n                children: [t('profile.quality'), \": \", imageQuality.level, \" (\", t('profile.score'), \": \", Math.round(imageQuality.score), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5,\n                  mb: 1\n                },\n                children: t('profile.recommendSharperImage')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"warning\",\n                onClick: handleForceUseBlurryImage,\n                sx: {\n                  mt: 1,\n                  fontSize: '0.75rem'\n                },\n                children: t('profile.useAnyway')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500\n                },\n                children: t('profile.goodImageQuality')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5\n                },\n                children: [t('profile.quality'), \": \", imageQuality.level, \" (\", t('profile.score'), \": \", Math.round(imageQuality.score), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 4\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                fontWeight: 600\n              },\n              children: [/*#__PURE__*/_jsxDEV(Person, {\n                color: \"primary\",\n                sx: {\n                  verticalAlign: 'middle',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 17\n              }, this), t('profile.personalInfo')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.fullName'),\n              name: \"name\",\n              fullWidth: true,\n              value: form.name || \"\",\n              onChange: handleChange,\n              margin: \"normal\",\n              slotProps: {\n                input: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Person, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this)\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.email'),\n              name: \"email\",\n              fullWidth: true,\n              value: form.email || \"\",\n              disabled: true,\n              margin: \"normal\",\n              slotProps: {\n                input: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Email, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 23\n                  }, this)\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.phone'),\n              name: \"phone\",\n              fullWidth: true,\n              value: form.phone || \"\",\n              onChange: handleChange,\n              margin: \"normal\",\n              slotProps: {\n                input: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(Phone, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 23\n                  }, this)\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.location'),\n              name: \"location\",\n              fullWidth: true,\n              value: form.location || \"\",\n              onChange: handleChange,\n              margin: \"normal\",\n              slotProps: {\n                input: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(LocationOn, {\n                      color: \"action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1091,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 23\n                  }, this)\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"primary\",\n                startIcon: /*#__PURE__*/_jsxDEV(Lock, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 30\n                }, this),\n                onClick: handlePasswordDialogOpen,\n                fullWidth: true,\n                sx: {\n                  borderRadius: 2,\n                  py: 1,\n                  textTransform: 'none',\n                  fontWeight: 500\n                },\n                children: t('profile.changePassword')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1099,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1098,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                fontWeight: 600\n              },\n              children: [/*#__PURE__*/_jsxDEV(Work, {\n                color: \"primary\",\n                sx: {\n                  verticalAlign: 'middle',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1120,\n                columnNumber: 17\n              }, this), t('profile.professionalDetails')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.role'),\n              name: \"role\",\n              fullWidth: true,\n              value: translateRole(form.role || \"Etudiant\"),\n              disabled: true,\n              margin: \"normal\",\n              helperText: t('profile.contactAdminRole'),\n              InputProps: {\n                sx: {\n                  textTransform: 'capitalize'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: t('profile.aboutMe'),\n              name: \"about\",\n              fullWidth: true,\n              multiline: true,\n              rows: 4,\n              value: form.about || \"\",\n              onChange: handleChange,\n              margin: \"normal\",\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                gutterBottom: true,\n                children: t('profile.skillsExpertise')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 1,\n                  mb: 2\n                },\n                children: skills.map((skill, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: skill,\n                  onDelete: () => handleRemoveSkill(skill),\n                  color: \"primary\",\n                  variant: \"outlined\",\n                  deleteIcon: /*#__PURE__*/_jsxDEV(Check, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1163,\n                    columnNumber: 35\n                  }, this),\n                  sx: {\n                    borderRadius: 1\n                  }\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'relative'\n                },\n                className: \"skills-dropdown-container\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TextField, {\n                    label: t('profile.addSkill'),\n                    value: newSkill,\n                    onChange: e => {\n                      setNewSkill(e.target.value);\n                      setShowSkillsDropdown(e.target.value.length > 0);\n                    },\n                    onFocus: () => setShowSkillsDropdown(newSkill.length > 0),\n                    size: \"small\",\n                    fullWidth: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    onClick: () => handleAddSkill(),\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1184,\n                      columnNumber: 34\n                    }, this),\n                    sx: {\n                      whiteSpace: 'nowrap'\n                    },\n                    children: t('common.add')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1169,\n                  columnNumber: 19\n                }, this), showSkillsDropdown && filteredSkills.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    position: 'absolute',\n                    top: '100%',\n                    left: 0,\n                    right: 0,\n                    zIndex: 1000,\n                    maxHeight: 200,\n                    overflowY: 'auto',\n                    mt: 1\n                  },\n                  children: filteredSkills.slice(0, 15).map(skill => /*#__PURE__*/_jsxDEV(Box, {\n                    onClick: () => handleAddSkill(skill),\n                    sx: {\n                      p: 1,\n                      cursor: 'pointer',\n                      '&:hover': {\n                        bgcolor: 'action.hover'\n                      }\n                    },\n                    children: skill\n                  }, skill, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end'\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                size: \"large\",\n                disabled: submitting,\n                sx: {\n                  px: 6,\n                  py: 1.5,\n                  borderRadius: 20,\n                  fontSize: '1rem',\n                  '&:hover': {\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)'\n                  }\n                },\n                children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 24,\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1246,\n                    columnNumber: 23\n                  }, this), t('common.saving')]\n                }, void 0, true) : t('common.saveChanges')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1017,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 855,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: passwordDialogOpen,\n      onClose: handlePasswordDialogClose,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          fontWeight: 600\n        },\n        children: t('profile.changePassword')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [passwordError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: passwordError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n          sx: {\n            mb: 2\n          },\n          children: t('profile.changePasswordDescription')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: t('profile.currentPassword'),\n          name: \"currentPassword\",\n          type: showCurrentPassword ? \"text\" : \"password\",\n          value: passwordForm.currentPassword,\n          onChange: handlePasswordChange,\n          fullWidth: true,\n          margin: \"normal\",\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowCurrentPassword(!showCurrentPassword),\n                edge: \"end\",\n                children: showCurrentPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 44\n                }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 64\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1292,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: t('profile.newPassword'),\n          name: \"newPassword\",\n          type: showNewPassword ? \"text\" : \"password\",\n          value: passwordForm.newPassword,\n          onChange: handlePasswordChange,\n          fullWidth: true,\n          margin: \"normal\",\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowNewPassword(!showNewPassword),\n                edge: \"end\",\n                children: showNewPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 40\n                }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 60\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1303,\n          columnNumber: 11\n        }, this), passwordForm.newPassword && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 1,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: [t('profile.passwordStrength'), \":\", /*#__PURE__*/_jsxDEV(Box, {\n              component: \"span\",\n              sx: {\n                ml: 1,\n                fontWeight: 'bold',\n                color: passwordStrength === t('profile.passwordStrengthStrong') ? 'success.main' : passwordStrength === t('profile.passwordStrengthMedium') ? 'warning.main' : 'error.main'\n              },\n              children: passwordStrength.toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%',\n              height: 4,\n              bgcolor: 'grey.200',\n              borderRadius: 1,\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: '100%',\n                width: passwordStrength === t('profile.passwordStrengthStrong') ? '100%' : passwordStrength === t('profile.passwordStrengthMedium') ? '60%' : '30%',\n                bgcolor: passwordStrength === t('profile.passwordStrengthStrong') ? 'success.main' : passwordStrength === t('profile.passwordStrengthMedium') ? 'warning.main' : 'error.main',\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1347,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1346,\n            columnNumber: 15\n          }, this), passwordStrength !== t('profile.passwordStrengthStrong') && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2,\n              p: 2,\n              bgcolor: 'grey.50',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              color: \"primary\",\n              children: [t('profile.strongPasswordTips'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1368,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: passwordForm.newPassword.length >= 8 ? 'success.main' : 'text.secondary',\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [passwordForm.newPassword.length >= 8 ? '✓' : '○', \" \", t('profile.atLeast8Chars')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1372,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: /[A-Z]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary',\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/[A-Z]/.test(passwordForm.newPassword) ? '✓' : '○', \" \", t('profile.oneUppercase')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: /[a-z]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary',\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/[a-z]/.test(passwordForm.newPassword) ? '✓' : '○', \" \", t('profile.oneLowercase')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1389,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: /\\d/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary',\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/\\d/.test(passwordForm.newPassword) ? '✓' : '○', \" \", t('profile.oneDigit')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: /[!@#$%^&*(),.?\":{}|<>]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary',\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/[!@#$%^&*(),.?\":{}|<>]/.test(passwordForm.newPassword) ? '✓' : '○', \" \", t('profile.oneSpecialChar')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1404,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1371,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1367,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: t('profile.confirmNewPassword'),\n          name: \"confirmPassword\",\n          type: showConfirmPassword ? \"text\" : \"password\",\n          value: passwordForm.confirmPassword,\n          onChange: handlePasswordChange,\n          fullWidth: true,\n          margin: \"normal\",\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                edge: \"end\",\n                children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 44\n                }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 64\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1429,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1428,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1418,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          px: 3,\n          pb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePasswordDialogClose,\n          variant: \"outlined\",\n          disabled: changingPassword,\n          children: t('common.cancel')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleChangePassword,\n          variant: \"contained\",\n          disabled: changingPassword,\n          startIcon: changingPassword ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1453,\n            columnNumber: 43\n          }, this) : /*#__PURE__*/_jsxDEV(Lock, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1453,\n            columnNumber: 76\n          }, this),\n          children: changingPassword ? t('common.updating') : t('profile.updatePassword')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1441,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 854,\n    columnNumber: 5\n  }, this);\n};\n_s(EditProfilePage, \"yKaHLH1k11I4SvPk1Wr23QsA47g=\", false, function () {\n  return [useTranslation, useParams, useNavigate];\n});\n_c = EditProfilePage;\nexport default EditProfilePage;\nvar _c;\n$RefreshReg$(_c, \"EditProfilePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Container", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "Grid", "CircularProgress", "<PERSON><PERSON>", "Box", "Avatar", "IconButton", "Chip", "Divider", "<PERSON><PERSON>", "InputAdornment", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useParams", "useNavigate", "ArrowBack", "PhotoCamera", "Email", "Phone", "LocationOn", "Work", "Person", "Check", "Add", "Lock", "Visibility", "VisibilityOff", "useTranslation", "axios", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditProfilePage", "_s", "_user$name", "_user$email", "t", "email", "navigate", "user", "setUser", "form", "setForm", "loading", "setLoading", "submitting", "setSubmitting", "error", "setError", "selectedFile", "setSelectedFile", "preview", "setPreview", "newSkill", "setNewSkill", "skills", "setSkills", "showSkillsDropdown", "setShowSkillsDropdown", "predefinedSkillKeys", "predefinedSkills", "map", "key", "imageQuality", "setImageQuality", "isAnalyzingImage", "setIsAnalyzingImage", "passwordDialogOpen", "setPasswordDialogOpen", "passwordForm", "setPasswordForm", "currentPassword", "newPassword", "confirmPassword", "passwordError", "setPasswordError", "showCurrentPassword", "setShowCurrentPassword", "showNewPassword", "setShowNewPassword", "showConfirmPassword", "setShowConfirmPassword", "changingPassword", "setChangingPassword", "passwordStrength", "setPasswordStrength", "translateRole", "role", "<PERSON><PERSON><PERSON>", "toLowerCase", "getPasswordStrength", "password", "strength", "lengthCriteria", "length", "hasUpperCase", "test", "hasLowerCase", "has<PERSON><PERSON>t", "hasSpecialChar", "analyzeImageQuality", "file", "Promise", "resolve", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "onload", "maxSize", "width", "height", "drawImage", "imageData", "getImageData", "data", "sum", "sumSquared", "count", "y", "x", "idx", "grayLeft", "grayRight", "gradX", "grayTop", "grayBottom", "gradY", "magnitude", "Math", "sqrt", "mean", "variance", "quality", "level", "score", "isBlurry", "onerror", "src", "URL", "createObjectURL", "handleClickOutside", "event", "target", "closest", "addEventListener", "removeEventListener", "fetchUser", "editingUserStr", "sessionStorage", "getItem", "editingUser", "JSON", "parse", "console", "log", "parsedSkills", "Array", "isArray", "parseErr", "res", "get", "serverErr", "removeItem", "userEmail", "storedUser", "localStorage", "userData", "err", "Error", "fetchErr", "message", "handleChange", "e", "name", "value", "handleFileChange", "files", "type", "startsWith", "size", "warning", "success", "handleAddSkill", "skillToAdd", "skill", "trim", "includes", "filteredSkills", "filter", "handleRemoveSkill", "skillToRemove", "handleForceUseBlurryImage", "fileInput", "querySelector", "info", "handlePasswordDialogOpen", "handlePasswordDialogClose", "handlePasswordChange", "handleChangePassword", "post", "sendNotification", "_error$response", "_error$response$data", "response", "handleSubmit", "preventDefault", "confirmUpload", "window", "confirm", "userId", "id", "parseInt", "isNaN", "userResponse", "idErr", "formattedSkills", "String", "phone", "location", "about", "updateResponse", "patch", "updatedUser", "storedUserData", "updatedUserData", "profilePic", "setItem", "stringify", "sessionUser", "sessionUserData", "formData", "FormData", "append", "photoResponse", "headers", "prev", "photoErr", "setTimeout", "dispatchEvent", "CustomEvent", "detail", "reload", "updateErr", "_updateErr$response", "_updateErr$response$d", "sx", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "mt", "elevation", "p", "borderRadius", "textAlign", "variant", "gutterBottom", "fontWeight", "severity", "mb", "direction", "spacing", "onClick", "py", "background", "WebkitBackgroundClip", "WebkitTextFillColor", "startIcon", "px", "flexDirection", "position", "split", "pop", "fontSize", "border", "boxShadow", "char<PERSON>t", "toUpperCase", "color", "component", "bottom", "right", "bgcolor", "hidden", "onChange", "accept", "mr", "round", "onSubmit", "container", "item", "xs", "md", "verticalAlign", "label", "fullWidth", "margin", "slotProps", "input", "startAdornment", "disabled", "textTransform", "helperText", "InputProps", "multiline", "rows", "flexWrap", "gap", "onDelete", "deleteIcon", "className", "onFocus", "whiteSpace", "top", "left", "zIndex", "maxHeight", "overflowY", "slice", "cursor", "my", "transform", "open", "onClose", "endAdornment", "edge", "ml", "overflow", "transition", "sm", "pb", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/EditProfilePage.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>er,\r\n  TextField,\r\n  Button,\r\n  Typography,\r\n  Paper,\r\n  Grid,\r\n  CircularProgress,\r\n  Alert,\r\n  Box,\r\n  Avatar,\r\n  IconButton,\r\n  Chip,\r\n  Divider,\r\n  Stack,\r\n  InputAdornment,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  DialogContentText,\r\n} from \"@mui/material\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport {\r\n  ArrowBack,\r\n  PhotoCamera,\r\n  Email,\r\n  Phone,\r\n  LocationOn,\r\n  Work,\r\n  Person,\r\n  Check,\r\n  Add,\r\n  Lock,\r\n  Visibility,\r\n  VisibilityOff,\r\n} from \"@mui/icons-material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst EditProfilePage = () => {\r\n  const { t } = useTranslation();\r\n  const { email } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  const [user, setUser] = useState(null);\r\n  const [form, setForm] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [preview, setPreview] = useState(\"\");\r\n  const [newSkill, setNewSkill] = useState(\"\");\r\n  const [skills, setSkills] = useState([]);\r\n  const [showSkillsDropdown, setShowSkillsDropdown] = useState(false);\r\n  \r\n  const predefinedSkillKeys = [\r\n    \"javascript\", \"python\", \"java\", \"cpp\", \"csharp\", \"php\", \"ruby\", \"go\", \"rust\", \"swift\", \"kotlin\", \"scala\", \"r\", \"matlab\", \"perl\",\r\n    \"react\", \"vuejs\", \"angular\", \"htmlcss\", \"sass\", \"less\", \"bootstrap\", \"tailwind\", \"jquery\", \"webpack\", \"vite\",\r\n    \"nodejs\", \"expressjs\", \"django\", \"flask\", \"springboot\", \"laravel\", \"rails\", \"aspnet\", \"fastapi\",\r\n    \"mysql\", \"postgresql\", \"mongodb\", \"redis\", \"sqlite\", \"oracle\", \"sqlserver\", \"cassandra\", \"dynamodb\", \"firebase\",\r\n    \"aws\", \"azure\", \"googlecloud\", \"docker\", \"kubernetes\", \"jenkins\", \"gitlabci\", \"githubactions\", \"terraform\", \"ansible\",\r\n    \"reactnative\", \"flutter\", \"iosdev\", \"androiddev\", \"xamarin\", \"ionic\",\r\n    \"dataanalysis\", \"machinelearning\", \"deeplearning\", \"ai\", \"tensorflow\", \"pytorch\", \"pandas\", \"numpy\", \"tableau\", \"powerbi\",\r\n    \"uiuxdesign\", \"figma\", \"adobexd\", \"sketch\", \"photoshop\", \"illustrator\", \"indesign\", \"aftereffects\", \"blender\",\r\n    \"digitalmarketing\", \"seo\", \"sem\", \"socialmedia\", \"contentmarketing\", \"emailmarketing\", \"googleanalytics\", \"facebookads\",\r\n    \"projectmanagement\", \"agile\", \"scrum\", \"kanban\", \"jira\", \"trello\", \"asana\", \"mondaycom\", \"slack\",\r\n    \"git\", \"linux\", \"windowsserver\", \"cybersecurity\", \"blockchain\", \"iot\", \"apidev\", \"microservices\", \"graphql\", \"restapi\",\r\n    \"leadership\", \"communication\", \"problemsolving\", \"criticalthinking\", \"teammanagement\", \"publicspeaking\", \"negotiation\",\r\n    \"english\", \"french\", \"spanish\", \"german\", \"arabic\", \"chinese\", \"japanese\", \"portuguese\", \"italian\", \"russian\",\r\n    \"ecommerce\", \"fintech\", \"healthtech\", \"edtech\", \"gaming\", \"automotive\", \"realestate\", \"logistics\", \"retail\",\r\n    \"contentwriting\", \"copywriting\", \"technicalwriting\", \"blogwriting\", \"socialcontent\", \"videoediting\", \"podcasting\"\r\n  ];\r\n  \r\n  const predefinedSkills = predefinedSkillKeys.map(key => t(`skills.${key}`));\r\n  const [imageQuality, setImageQuality] = useState(null);\r\n  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);\r\n\r\n  // États pour le changement de mot de passe\r\n  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);\r\n  const [passwordForm, setPasswordForm] = useState({\r\n    currentPassword: \"\",\r\n    newPassword: \"\",\r\n    confirmPassword: \"\"\r\n  });\r\n  const [passwordError, setPasswordError] = useState(\"\");\r\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false);\r\n  const [showNewPassword, setShowNewPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [changingPassword, setChangingPassword] = useState(false);\r\n  const [passwordStrength, setPasswordStrength] = useState(\"\");\r\n\r\n  // Fonction pour traduire le rôle\r\n  const translateRole = (role) => {\r\n    if (!role) return t('role.etudiant');\r\n    const roleKey = role.toLowerCase();\r\n    return t(`role.${roleKey}`);\r\n  };\r\n\r\n  // Fonction pour évaluer la force du mot de passe\r\n  const getPasswordStrength = (password) => {\r\n    if (!password) return \"\";\r\n\r\n    let strength = 0;\r\n\r\n    // Critères d'évaluation\r\n    const lengthCriteria = password.length >= 8;\r\n    const hasUpperCase = /[A-Z]/.test(password);\r\n    const hasLowerCase = /[a-z]/.test(password);\r\n    const hasDigit = /\\d/.test(password);\r\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\r\n\r\n    // Calcul du score\r\n    strength += lengthCriteria ? 1 : 0;\r\n    strength += hasUpperCase ? 1 : 0;\r\n    strength += hasLowerCase ? 1 : 0;\r\n    strength += hasDigit ? 1 : 0;\r\n    strength += hasSpecialChar ? 1 : 0;\r\n\r\n    // Détermination de la force\r\n    if (strength <= 2) return t('profile.passwordStrengthWeak');\r\n    if (strength <= 4) return t('profile.passwordStrengthMedium');\r\n    return t('profile.passwordStrengthStrong');\r\n  };\r\n\r\n  // Fonction pour analyser la qualité de l'image (détection de flou)\r\n  const analyzeImageQuality = (file) => {\r\n    return new Promise((resolve) => {\r\n      const img = new Image();\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n\r\n      img.onload = () => {\r\n        // Redimensionner l'image pour l'analyse (pour des performances optimales)\r\n        const maxSize = 500;\r\n        let { width, height } = img;\r\n\r\n        if (width > height) {\r\n          if (width > maxSize) {\r\n            height = (height * maxSize) / width;\r\n            width = maxSize;\r\n          }\r\n        } else {\r\n          if (height > maxSize) {\r\n            width = (width * maxSize) / height;\r\n            height = maxSize;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Obtenir les données de l'image\r\n        const imageData = ctx.getImageData(0, 0, width, height);\r\n        const data = imageData.data;\r\n\r\n        // Calculer la variance des gradients (méthode de détection de flou)\r\n        let sum = 0;\r\n        let sumSquared = 0;\r\n        let count = 0;\r\n\r\n        // Calculer les gradients horizontaux et verticaux\r\n        for (let y = 1; y < height - 1; y++) {\r\n          for (let x = 1; x < width - 1; x++) {\r\n            const idx = (y * width + x) * 4;\r\n\r\n            // Convertir en niveaux de gris (variable non utilisée)\r\n            // const gray = 0.299 * data[idx] + 0.587 * data[idx + 1] + 0.114 * data[idx + 2];\r\n\r\n            // Gradient horizontal\r\n            const grayLeft = 0.299 * data[idx - 4] + 0.587 * data[idx - 3] + 0.114 * data[idx - 2];\r\n            const grayRight = 0.299 * data[idx + 4] + 0.587 * data[idx + 5] + 0.114 * data[idx + 6];\r\n            const gradX = grayRight - grayLeft;\r\n\r\n            // Gradient vertical\r\n            const grayTop = 0.299 * data[idx - width * 4] + 0.587 * data[idx - width * 4 + 1] + 0.114 * data[idx - width * 4 + 2];\r\n            const grayBottom = 0.299 * data[idx + width * 4] + 0.587 * data[idx + width * 4 + 1] + 0.114 * data[idx + width * 4 + 2];\r\n            const gradY = grayBottom - grayTop;\r\n\r\n            // Magnitude du gradient\r\n            const magnitude = Math.sqrt(gradX * gradX + gradY * gradY);\r\n\r\n            sum += magnitude;\r\n            sumSquared += magnitude * magnitude;\r\n            count++;\r\n          }\r\n        }\r\n\r\n        // Calculer la variance\r\n        const mean = sum / count;\r\n        const variance = (sumSquared / count) - (mean * mean);\r\n\r\n        // Déterminer la qualité basée sur la variance\r\n        // Plus la variance est élevée, plus l'image est nette\r\n        let quality;\r\n        if (variance > 1000) {\r\n          quality = { level: 'excellent', score: variance, isBlurry: false };\r\n        } else if (variance > 500) {\r\n          quality = { level: 'good', score: variance, isBlurry: false };\r\n        } else if (variance > 200) {\r\n          quality = { level: 'acceptable', score: variance, isBlurry: false };\r\n        } else if (variance > 100) {\r\n          quality = { level: 'poor', score: variance, isBlurry: true };\r\n        } else {\r\n          quality = { level: 'very_poor', score: variance, isBlurry: true };\r\n        }\r\n\r\n        resolve(quality);\r\n      };\r\n\r\n      img.onerror = () => {\r\n        resolve({ level: 'error', score: 0, isBlurry: true });\r\n      };\r\n\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (!event.target.closest('.skills-dropdown-container')) {\r\n        setShowSkillsDropdown(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchUser = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        // Vérifier d'abord si nous avons un utilisateur à éditer dans sessionStorage\r\n        // (cela serait défini lorsqu'on clique sur \"Edit\" dans la liste des utilisateurs)\r\n        const editingUserStr = sessionStorage.getItem(\"editingUser\");\r\n        if (editingUserStr) {\r\n          try {\r\n            const editingUser = JSON.parse(editingUserStr);\r\n            console.log(\"Found user to edit in sessionStorage:\", editingUser);\r\n\r\n            if (editingUser && editingUser.email) {\r\n              // Utiliser cet utilisateur comme point de départ\r\n              setUser(editingUser);\r\n              setForm(editingUser);\r\n\r\n              // Gérer les skills\r\n              if (editingUser.skills) {\r\n                if (typeof editingUser.skills === 'string') {\r\n                  try {\r\n                    const parsedSkills = JSON.parse(editingUser.skills);\r\n                    setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\r\n                  } catch (parseErr) {\r\n                    console.error(\"Error parsing skills:\", parseErr);\r\n                    setSkills([]);\r\n                  }\r\n                } else if (Array.isArray(editingUser.skills)) {\r\n                  setSkills(editingUser.skills);\r\n                } else {\r\n                  setSkills([]);\r\n                }\r\n              } else {\r\n                setSkills([]);\r\n              }\r\n\r\n              // Récupérer les données complètes depuis le serveur\r\n              try {\r\n                console.log(\"Fetching complete user data for email:\", editingUser.email);\r\n                const res = await axios.get(`http://localhost:8000/users/email/${editingUser.email}`);\r\n                console.log(\"Complete user data received:\", res.data);\r\n\r\n                if (res.data) {\r\n                  setUser(res.data);\r\n                  setForm(res.data);\r\n\r\n                  // Mettre à jour les skills avec les données du serveur\r\n                  if (res.data.skills) {\r\n                    if (typeof res.data.skills === 'string') {\r\n                      try {\r\n                        const parsedSkills = JSON.parse(res.data.skills);\r\n                        setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\r\n                      } catch (parseErr) {\r\n                        console.error(\"Error parsing skills from server:\", parseErr);\r\n                      }\r\n                    } else if (Array.isArray(res.data.skills)) {\r\n                      setSkills(res.data.skills);\r\n                    }\r\n                  }\r\n                }\r\n              } catch (serverErr) {\r\n                console.error(\"Error fetching complete user data:\", serverErr);\r\n                // Continuer avec les données de sessionStorage\r\n              }\r\n\r\n              // Nettoyer sessionStorage après utilisation\r\n              sessionStorage.removeItem(\"editingUser\");\r\n              setLoading(false);\r\n              return; // Sortir de la fonction car nous avons déjà les données\r\n            }\r\n          } catch (parseErr) {\r\n            console.error(\"Error parsing editing user from sessionStorage:\", parseErr);\r\n          }\r\n        }\r\n\r\n        // Si nous n'avons pas trouvé d'utilisateur à éditer dans sessionStorage,\r\n        // continuer avec la logique existante basée sur l'email de l'URL\r\n\r\n        // Si l'email n'est pas fourni dans l'URL, essayer de le récupérer depuis le localStorage\r\n        let userEmail = email;\r\n        if (!userEmail) {\r\n          console.log(\"Email not provided in URL, trying to get it from localStorage\");\r\n          const storedUser = localStorage.getItem(\"user\");\r\n          if (storedUser) {\r\n            try {\r\n              const userData = JSON.parse(storedUser);\r\n              if (userData && userData.email) {\r\n                userEmail = userData.email;\r\n                console.log(\"Using email from localStorage:\", userEmail);\r\n              }\r\n            } catch (err) {\r\n              console.error(\"Error parsing user data from localStorage:\", err);\r\n            }\r\n          }\r\n        }\r\n\r\n        if (!userEmail) {\r\n          throw new Error(\"No email available to fetch user data\");\r\n        }\r\n\r\n        try {\r\n          console.log(\"Fetching user data for email:\", userEmail);\r\n          const res = await axios.get(`http://localhost:8000/users/email/${userEmail}`);\r\n          console.log(\"User data received:\", res.data);\r\n\r\n          if (!res.data) {\r\n            throw new Error(\"No user data received from server\");\r\n          }\r\n\r\n          setUser(res.data);\r\n          setForm(res.data);\r\n\r\n          // Gérer les skills correctement\r\n          if (res.data.skills) {\r\n            // Si skills est une chaîne de caractères, essayer de la parser\r\n            if (typeof res.data.skills === 'string') {\r\n              try {\r\n                const parsedSkills = JSON.parse(res.data.skills);\r\n                setSkills(Array.isArray(parsedSkills) ? parsedSkills : []);\r\n              } catch (parseErr) {\r\n                console.error(\"Error parsing skills:\", parseErr);\r\n                setSkills([]);\r\n              }\r\n            } else if (Array.isArray(res.data.skills)) {\r\n              setSkills(res.data.skills);\r\n            } else {\r\n              setSkills([]);\r\n            }\r\n          } else {\r\n            setSkills([]);\r\n          }\r\n        } catch (fetchErr) {\r\n          console.error(\"Error fetching user data:\", fetchErr);\r\n\r\n          // Si l'utilisateur est dans le localStorage, l'utiliser comme solution de secours\r\n          const storedUser = localStorage.getItem(\"user\");\r\n          if (storedUser) {\r\n            try {\r\n              const userData = JSON.parse(storedUser);\r\n              console.log(\"Using user data from localStorage as fallback:\", userData);\r\n              setUser(userData);\r\n              setForm(userData);\r\n\r\n              if (userData.skills) {\r\n                setSkills(Array.isArray(userData.skills) ? userData.skills : []);\r\n              } else {\r\n                setSkills([]);\r\n              }\r\n            } catch (parseErr) {\r\n              console.error(\"Error parsing user data from localStorage:\", parseErr);\r\n              throw new Error(\"Failed to load user data\");\r\n            }\r\n          } else {\r\n            throw new Error(\"Failed to fetch user data and no local data available\");\r\n          }\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error loading user:\", err);\r\n        setError(`Error loading profile: ${err.message}`);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUser();\r\n  }, [email, navigate]);\r\n\r\n  const handleChange = (e) => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleFileChange = async (e) => {\r\n    const file = e.target.files[0];\r\n\r\n    if (!file) {\r\n      setSelectedFile(null);\r\n      setPreview(\"\");\r\n      setImageQuality(null);\r\n      return;\r\n    }\r\n\r\n    // Vérifier le type de fichier\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error(t('profile.invalidImageFile'));\r\n      e.target.value = '';\r\n      return;\r\n    }\r\n\r\n    // Vérifier la taille du fichier (max 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast.error(t('profile.fileSizeError'));\r\n      e.target.value = '';\r\n      return;\r\n    }\r\n\r\n    setPreview(URL.createObjectURL(file));\r\n    setIsAnalyzingImage(true);\r\n    setImageQuality(null);\r\n\r\n    try {\r\n      // Analyser la qualité de l'image\r\n      const quality = await analyzeImageQuality(file);\r\n      setImageQuality(quality);\r\n\r\n      if (quality.isBlurry) {\r\n        toast.warning(t('profile.blurryImageWarning', { quality: quality.level }));\r\n        // Ne pas définir le fichier automatiquement si l'image est floue\r\n        setSelectedFile(null);\r\n      } else {\r\n        toast.success(t('profile.goodQualityImage', { quality: quality.level }));\r\n        setSelectedFile(file);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'analyse de l'image:\", error);\r\n      toast.error(t('profile.imageAnalysisError'));\r\n      setImageQuality({ level: 'error', score: 0, isBlurry: true });\r\n      setSelectedFile(null);\r\n    } finally {\r\n      setIsAnalyzingImage(false);\r\n    }\r\n  };\r\n\r\n  const handleAddSkill = (skillToAdd = null) => {\r\n    const skill = skillToAdd || newSkill.trim();\r\n    if (skill && !skills.includes(skill)) {\r\n      setSkills([...skills, skill]);\r\n      setNewSkill(\"\");\r\n      setShowSkillsDropdown(false);\r\n    }\r\n  };\r\n  \r\n  const filteredSkills = predefinedSkills.filter(skill => \r\n    skill.toLowerCase().includes(newSkill.toLowerCase()) && !skills.includes(skill)\r\n  );\r\n\r\n  const handleRemoveSkill = (skillToRemove) => {\r\n    setSkills(skills.filter(skill => skill !== skillToRemove));\r\n  };\r\n\r\n  // Fonction pour forcer l'utilisation d'une image floue\r\n  const handleForceUseBlurryImage = () => {\r\n    const fileInput = document.querySelector('input[type=\"file\"]');\r\n    if (fileInput && fileInput.files[0]) {\r\n      setSelectedFile(fileInput.files[0]);\r\n      toast.info(t('profile.blurryImageAccepted'));\r\n    }\r\n  };\r\n\r\n  // Fonctions pour le changement de mot de passe\r\n  const handlePasswordDialogOpen = () => {\r\n    setPasswordDialogOpen(true);\r\n    setPasswordForm({\r\n      currentPassword: \"\",\r\n      newPassword: \"\",\r\n      confirmPassword: \"\"\r\n    });\r\n    setPasswordError(\"\");\r\n  };\r\n\r\n  const handlePasswordDialogClose = () => {\r\n    setPasswordDialogOpen(false);\r\n    setPasswordError(\"\");\r\n  };\r\n\r\n  const handlePasswordChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setPasswordForm({\r\n      ...passwordForm,\r\n      [name]: value\r\n    });\r\n\r\n    // Évaluer la force du mot de passe si c'est le champ newPassword qui est modifié\r\n    if (name === \"newPassword\") {\r\n      setPasswordStrength(getPasswordStrength(value));\r\n    }\r\n  };\r\n\r\n  const handleChangePassword = async () => {\r\n    // Validation\r\n    if (!passwordForm.currentPassword) {\r\n      setPasswordError(t('profile.currentPasswordRequired'));\r\n      return;\r\n    }\r\n\r\n    if (!passwordForm.newPassword) {\r\n      setPasswordError(t('profile.newPasswordRequired'));\r\n      return;\r\n    }\r\n\r\n    if (passwordForm.newPassword !== passwordForm.confirmPassword) {\r\n      setPasswordError(t('profile.passwordsDoNotMatch'));\r\n      return;\r\n    }\r\n\r\n    if (passwordForm.newPassword.length < 6) {\r\n      setPasswordError(t('profile.passwordTooShort'));\r\n      return;\r\n    }\r\n\r\n    setChangingPassword(true);\r\n    setPasswordError(\"\");\r\n\r\n    try {\r\n      // Appel API pour changer le mot de passe\r\n      await axios.post(`http://localhost:8000/auth/change-password`, {\r\n        email: user.email,\r\n        currentPassword: passwordForm.currentPassword,\r\n        newPassword: passwordForm.newPassword,\r\n        sendNotification: true\r\n      });\r\n\r\n      toast.success(t('profile.passwordChangedSuccess'));\r\n      toast.info(t('profile.passwordChangeEmailSent'));\r\n      handlePasswordDialogClose();\r\n    } catch (error) {\r\n      console.error(\"Error changing password:\", error);\r\n      setPasswordError(error.response?.data?.message || t('profile.passwordChangeError'));\r\n    } finally {\r\n      setChangingPassword(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setSubmitting(true);\r\n    setError(\"\");\r\n\r\n    // Vérifier la qualité de l'image avant la soumission\r\n    if (selectedFile && imageQuality && imageQuality.isBlurry) {\r\n      const confirmUpload = window.confirm(\r\n        t('profile.confirmBlurryUpload', { quality: imageQuality.level })\r\n      );\r\n\r\n      if (!confirmUpload) {\r\n        setSubmitting(false);\r\n        return;\r\n      }\r\n    }\r\n\r\n    try {\r\n      // Utiliser l'email de l'utilisateur chargé plutôt que celui de l'URL\r\n      const userEmail = user.email;\r\n      let userId = user.id;\r\n\r\n      // Vérifier si l'ID est une chaîne de caractères et la convertir en nombre si nécessaire\r\n      if (typeof userId === 'string') {\r\n        userId = parseInt(userId, 10);\r\n        if (isNaN(userId)) {\r\n          console.error(\"Invalid user ID format:\", user.id);\r\n          // Essayer de récupérer l'ID depuis la réponse API\r\n          try {\r\n            const userResponse = await axios.get(`http://localhost:8000/users/email/${userEmail}`);\r\n            if (userResponse.data && userResponse.data.id) {\r\n              userId = userResponse.data.id;\r\n              console.log(\"Retrieved user ID from API:\", userId);\r\n            }\r\n          } catch (idErr) {\r\n            console.error(\"Failed to retrieve user ID from API:\", idErr);\r\n          }\r\n        }\r\n      }\r\n\r\n      if (!userEmail) {\r\n        throw new Error(\"No email available to update user data\");\r\n      }\r\n\r\n      console.log(\"Updating user data for email:\", userEmail, \"with ID:\", userId);\r\n\r\n      // Préparer les données utilisateur à mettre à jour\r\n      // S'assurer que skills est bien un tableau\r\n      let formattedSkills = skills;\r\n      if (!Array.isArray(formattedSkills)) {\r\n        if (typeof formattedSkills === 'string') {\r\n          try {\r\n            // Essayer de parser si c'est une chaîne JSON\r\n            formattedSkills = JSON.parse(formattedSkills);\r\n            if (!Array.isArray(formattedSkills)) {\r\n              formattedSkills = [formattedSkills]; // Convertir en tableau si ce n'est pas déjà un tableau\r\n            }\r\n          } catch (e) {\r\n            // Si ce n'est pas du JSON valide, le traiter comme une chaîne simple\r\n            formattedSkills = [formattedSkills];\r\n          }\r\n        } else if (formattedSkills) {\r\n          // Si c'est une valeur non-null/undefined mais pas un tableau ou une chaîne\r\n          formattedSkills = [String(formattedSkills)];\r\n        } else {\r\n          // Si c'est null ou undefined\r\n          formattedSkills = [];\r\n        }\r\n      }\r\n\r\n      // Filtrer les valeurs vides ou nulles\r\n      formattedSkills = formattedSkills.filter(skill => skill && skill.trim());\r\n\r\n      console.log(\"Formatted skills:\", formattedSkills);\r\n\r\n      const userData = {\r\n        name: form.name || null,\r\n        phone: form.phone || null,\r\n        location: form.location || null,\r\n        about: form.about || null,\r\n        skills: formattedSkills, // Le backend s'attend à un tableau\r\n      };\r\n\r\n      console.log(\"User data to update:\", userData);\r\n\r\n      try {\r\n        // Mettre à jour les données utilisateur\r\n        const updateResponse = await axios.patch(`http://localhost:8000/users/email/${userEmail}`, userData);\r\n        console.log(\"Update response:\", updateResponse.data);\r\n\r\n        if (!updateResponse.data) {\r\n          throw new Error(\"No data received from server after update\");\r\n        }\r\n\r\n        // Mettre à jour l'utilisateur avec les données de la réponse\r\n        const updatedUser = updateResponse.data;\r\n        setUser(updatedUser);\r\n\r\n        // Mettre à jour les données utilisateur dans le localStorage et sessionStorage\r\n        // seulement si l'utilisateur connecté est celui qui est en train d'être modifié\r\n        const storedUser = localStorage.getItem(\"user\");\r\n        if (storedUser) {\r\n          try {\r\n            const storedUserData = JSON.parse(storedUser);\r\n\r\n            // Vérifier si l'utilisateur connecté est celui qui est en train d'être modifié\r\n            if (storedUserData.email === userEmail) {\r\n              // Utiliser les données de la réponse pour mettre à jour le localStorage\r\n              const updatedUserData = {\r\n                ...storedUserData,\r\n                name: updatedUser.name,\r\n                phone: updatedUser.phone,\r\n                location: updatedUser.location,\r\n                about: updatedUser.about,\r\n                skills: updatedUser.skills,\r\n                profilePic: updatedUser.profilePic\r\n              };\r\n              localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\r\n              console.log(\"Updated user data in localStorage:\", updatedUserData);\r\n\r\n              // Mettre à jour également dans sessionStorage si présent\r\n              const sessionUser = sessionStorage.getItem(\"user\");\r\n              if (sessionUser) {\r\n                try {\r\n                  const sessionUserData = JSON.parse(sessionUser);\r\n                  if (sessionUserData.email === userEmail) {\r\n                    sessionStorage.setItem(\"user\", JSON.stringify(updatedUserData));\r\n                    console.log(\"Updated user data in sessionStorage\");\r\n                  }\r\n                } catch (err) {\r\n                  console.error(\"Error updating user data in sessionStorage:\", err);\r\n                }\r\n              }\r\n            } else {\r\n              console.log(\"User being edited is not the logged-in user, not updating localStorage\");\r\n            }\r\n          } catch (err) {\r\n            console.error(\"Error updating user data in localStorage:\", err);\r\n          }\r\n        }\r\n\r\n        // Télécharger la photo de profil si sélectionnée\r\n        if (selectedFile && userId) {\r\n          try {\r\n            console.log(\"Uploading profile picture for user ID:\", userId);\r\n            const formData = new FormData();\r\n            formData.append(\"photo\", selectedFile);\r\n\r\n            // Ajouter un log pour déboguer\r\n            console.log(\"Sending photo upload request to:\", `http://localhost:8000/users/id/${userId}/photo`);\r\n            console.log(\"With form data:\", selectedFile.name);\r\n\r\n            const photoResponse = await axios.patch(\r\n              `http://localhost:8000/users/id/${userId}/photo`,\r\n              formData,\r\n              { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n            );\r\n\r\n            console.log(\"Photo upload response:\", photoResponse.data);\r\n\r\n            if (photoResponse.data && photoResponse.data.profilePic) {\r\n              // Mettre à jour l'utilisateur avec la nouvelle photo\r\n              setUser(prev => ({ ...prev, profilePic: photoResponse.data.profilePic }));\r\n\r\n              // Mettre à jour le localStorage avec la nouvelle photo si c'est l'utilisateur connecté\r\n              const storedUser = localStorage.getItem(\"user\");\r\n              if (storedUser) {\r\n                try {\r\n                  const storedUserData = JSON.parse(storedUser);\r\n                  if (storedUserData.email === userEmail) {\r\n                    storedUserData.profilePic = photoResponse.data.profilePic;\r\n                    localStorage.setItem(\"user\", JSON.stringify(storedUserData));\r\n                    console.log(\"Updated profile picture in localStorage\");\r\n\r\n                    // Mettre à jour également dans sessionStorage si présent\r\n                    const sessionUser = sessionStorage.getItem(\"user\");\r\n                    if (sessionUser) {\r\n                      try {\r\n                        const sessionUserData = JSON.parse(sessionUser);\r\n                        if (sessionUserData.email === userEmail) {\r\n                          sessionUserData.profilePic = photoResponse.data.profilePic;\r\n                          sessionStorage.setItem(\"user\", JSON.stringify(sessionUserData));\r\n                          console.log(\"Updated profile picture in sessionStorage\");\r\n                        }\r\n                      } catch (err) {\r\n                        console.error(\"Error updating profile picture in sessionStorage:\", err);\r\n                      }\r\n                    }\r\n                  }\r\n                } catch (err) {\r\n                  console.error(\"Error updating profile picture in localStorage:\", err);\r\n                }\r\n              }\r\n            }\r\n          } catch (photoErr) {\r\n            console.error(\"Error uploading profile picture:\", photoErr);\r\n            toast.error(t('profile.profileUpdatePartialError'));\r\n          }\r\n        }\r\n\r\n        toast.success(t('profile.profileUpdatedSuccess'));\r\n\r\n        // Rafraîchir la page principale et naviguer vers la page de profil\r\n        setTimeout(() => {\r\n          // Forcer un rafraîchissement des données utilisateur dans l'application principale\r\n          window.dispatchEvent(new CustomEvent('userProfileUpdated', {\r\n            detail: { updatedUser: updatedUser || user }\r\n          }));\r\n\r\n          // Option 1: Navigation normale avec rafraîchissement des données\r\n          if (updatedUser && updatedUser.id) {\r\n            navigate(`/ProfilePage/${updatedUser.id}`);\r\n            // Forcer un rafraîchissement de la page après navigation pour s'assurer que tout est à jour\r\n            setTimeout(() => {\r\n              window.location.reload();\r\n            }, 100);\r\n          } else if (userId) {\r\n            navigate(`/ProfilePage/${userId}`);\r\n            // Forcer un rafraîchissement de la page après navigation\r\n            setTimeout(() => {\r\n              window.location.reload();\r\n            }, 100);\r\n          } else {\r\n            // Si l'ID n'est pas disponible, essayer de récupérer l'utilisateur depuis le localStorage\r\n            const storedUser = localStorage.getItem(\"user\");\r\n            if (storedUser) {\r\n              try {\r\n                const userData = JSON.parse(storedUser);\r\n                if (userData && userData.id) {\r\n                  navigate(`/ProfilePage/${userData.id}`);\r\n                  // Forcer un rafraîchissement de la page après navigation\r\n                  setTimeout(() => {\r\n                    window.location.reload();\r\n                  }, 100);\r\n                  return;\r\n                }\r\n              } catch (err) {\r\n                console.error(\"Error parsing user data from localStorage:\", err);\r\n              }\r\n            }\r\n            // Si tout échoue, naviguer vers la page d'accueil et rafraîchir\r\n            navigate(\"/\");\r\n            window.location.reload();\r\n          }\r\n        }, 1500);\r\n      } catch (updateErr) {\r\n        console.error(\"Error updating user data:\", updateErr);\r\n        setError(`Update failed: ${updateErr.response?.data?.message || updateErr.message}`);\r\n        toast.error(t('profile.profileUpdateError'));\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Update error:\", err);\r\n      setError(`Update failed: ${err.message}`);\r\n      toast.error(t('profile.profileUpdateError'));\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Container sx={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        minHeight: '80vh'\r\n      }}>\r\n        <CircularProgress size={60} />\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  if (!user) {\r\n    return (\r\n      <Container maxWidth=\"sm\" sx={{ mt: 8 }}>\r\n        <Paper elevation={3} sx={{ p: 4, borderRadius: 4, textAlign: 'center' }}>\r\n          <Typography variant=\"h5\" gutterBottom sx={{ fontWeight: 600 }}>\r\n            {t('profile.profileError')}\r\n          </Typography>\r\n          <Alert severity=\"error\" sx={{ mb: 3 }}>\r\n            {error || t('profile.userNotFound')}\r\n          </Alert>\r\n          <Stack direction=\"row\" spacing={2} justifyContent=\"center\">\r\n            <Button variant=\"contained\" onClick={() => window.location.reload()}>\r\n              {t('common.tryAgain')}\r\n            </Button>\r\n            <Button variant=\"outlined\" onClick={() => navigate('/')}>\r\n              {t('common.goHome')}\r\n            </Button>\r\n          </Stack>\r\n        </Paper>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"md\" sx={{ py: 6 }}>\r\n      <Paper elevation={4} sx={{\r\n        p: 6,\r\n        borderRadius: 6,\r\n        background: 'linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%)'\r\n      }}>\r\n        {/* Header Section */}\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          mb: 4\r\n        }}>\r\n          <Typography variant=\"h4\" sx={{\r\n            fontWeight: 700,\r\n            background: 'linear-gradient(45deg, #1976d2 30%, #2196f3 90%)',\r\n            WebkitBackgroundClip: 'text',\r\n            WebkitTextFillColor: 'transparent'\r\n          }}>\r\n            {t('profile.editProfile')}\r\n          </Typography>\r\n          <Button\r\n            onClick={() => {\r\n              // Naviguer vers la page de profil avec l'ID de l'utilisateur\r\n              if (user && user.id) {\r\n                navigate(`/ProfilePage/${user.id}`);\r\n              } else {\r\n                // Si l'ID n'est pas disponible, essayer de récupérer l'utilisateur depuis le localStorage\r\n                const storedUser = localStorage.getItem(\"user\");\r\n                if (storedUser) {\r\n                  try {\r\n                    const userData = JSON.parse(storedUser);\r\n                    if (userData && userData.id) {\r\n                      navigate(`/ProfilePage/${userData.id}`);\r\n                      return;\r\n                    }\r\n                  } catch (err) {\r\n                    console.error(\"Error parsing user data from localStorage:\", err);\r\n                  }\r\n                }\r\n                // Si tout échoue, naviguer vers la page d'accueil\r\n                navigate(\"/\");\r\n              }\r\n            }}\r\n            startIcon={<ArrowBack />}\r\n            variant=\"outlined\"\r\n            sx={{ borderRadius: 20, px: 3 }}\r\n          >\r\n            {t('profile.backToProfile')}\r\n          </Button>\r\n        </Box>\r\n\r\n        {/* Profile Picture Section */}\r\n        <Box sx={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center',\r\n          mb: 4\r\n        }}>\r\n          <Box sx={{ position: 'relative' }}>\r\n            <Avatar\r\n              src={preview || (user.profilePic ?\r\n                (user.profilePic.startsWith('/profile-pics/') ?\r\n                  `http://localhost:8000/uploads${user.profilePic}` :\r\n                  (user.profilePic.startsWith('http') ?\r\n                    user.profilePic :\r\n                    `http://localhost:8000/uploads/profile-pics/${user.profilePic.split('/').pop()}`\r\n                  )\r\n                ) :\r\n                null\r\n              )}\r\n              sx={{\r\n                width: 150,\r\n                height: 150,\r\n                fontSize: 60,\r\n                border: '4px solid #1976d2',\r\n                boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)'\r\n              }}\r\n            >\r\n              {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || \"U\"}\r\n            </Avatar>\r\n            <IconButton\r\n              color=\"primary\"\r\n              component=\"label\"\r\n              sx={{\r\n                position: 'absolute',\r\n                bottom: 0,\r\n                right: 0,\r\n                bgcolor: 'background.paper',\r\n                '&:hover': { bgcolor: 'action.hover' }\r\n              }}\r\n            >\r\n              <PhotoCamera />\r\n              <input type=\"file\" hidden onChange={handleFileChange} accept=\"image/*\" />\r\n            </IconButton>\r\n          </Box>\r\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\r\n            {t('profile.clickCameraToChange')}\r\n          </Typography>\r\n\r\n          {/* Indicateur d'analyse de l'image */}\r\n          {isAnalyzingImage && (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\r\n              <CircularProgress size={20} sx={{ mr: 1 }} />\r\n              <Typography variant=\"body2\" color=\"primary\">\r\n                {t('profile.analyzingImageQuality')}\r\n              </Typography>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Résultat de l'analyse de l'image */}\r\n          {imageQuality && !isAnalyzingImage && (\r\n            <Box sx={{ mt: 2, textAlign: 'center' }}>\r\n              <Alert\r\n                severity={imageQuality.isBlurry ? \"warning\" : \"success\"}\r\n                sx={{\r\n                  display: 'inline-flex',\r\n                  alignItems: 'center',\r\n                  maxWidth: 400\r\n                }}\r\n              >\r\n                {imageQuality.isBlurry ? (\r\n                  <Box sx={{ textAlign: 'center' }}>\r\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\r\n                      {t('profile.blurryImageDetected')}\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" sx={{ display: 'block', mt: 0.5 }}>\r\n                      {t('profile.quality')}: {imageQuality.level} ({t('profile.score')}: {Math.round(imageQuality.score)})\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" sx={{ display: 'block', mt: 0.5, mb: 1 }}>\r\n                      {t('profile.recommendSharperImage')}\r\n                    </Typography>\r\n                    <Button\r\n                      size=\"small\"\r\n                      variant=\"outlined\"\r\n                      color=\"warning\"\r\n                      onClick={handleForceUseBlurryImage}\r\n                      sx={{ mt: 1, fontSize: '0.75rem' }}\r\n                    >\r\n                      {t('profile.useAnyway')}\r\n                    </Button>\r\n                  </Box>\r\n                ) : (\r\n                  <>\r\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\r\n                      {t('profile.goodImageQuality')}\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" sx={{ display: 'block', mt: 0.5 }}>\r\n                      {t('profile.quality')}: {imageQuality.level} ({t('profile.score')}: {Math.round(imageQuality.score)})\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Alert>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        {error && (\r\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          <Grid container spacing={4}>\r\n            {/* Personal Info */}\r\n            <Grid item xs={12} md={6}>\r\n              <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\r\n                <Person color=\"primary\" sx={{ verticalAlign: 'middle', mr: 1 }} />\r\n                {t('profile.personalInfo')}\r\n              </Typography>\r\n\r\n              <TextField\r\n                label={t('profile.fullName')}\r\n                name=\"name\"\r\n                fullWidth\r\n                value={form.name || \"\"}\r\n                onChange={handleChange}\r\n                margin=\"normal\"\r\n                slotProps={{\r\n                  input: {\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Person color=\"action\" />\r\n                      </InputAdornment>\r\n                    )\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <TextField\r\n                label={t('profile.email')}\r\n                name=\"email\"\r\n                fullWidth\r\n                value={form.email || \"\"}\r\n                disabled\r\n                margin=\"normal\"\r\n                slotProps={{\r\n                  input: {\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Email color=\"action\" />\r\n                      </InputAdornment>\r\n                    )\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <TextField\r\n                label={t('profile.phone')}\r\n                name=\"phone\"\r\n                fullWidth\r\n                value={form.phone || \"\"}\r\n                onChange={handleChange}\r\n                margin=\"normal\"\r\n                slotProps={{\r\n                  input: {\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <Phone color=\"action\" />\r\n                      </InputAdornment>\r\n                    )\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <TextField\r\n                label={t('profile.location')}\r\n                name=\"location\"\r\n                fullWidth\r\n                value={form.location || \"\"}\r\n                onChange={handleChange}\r\n                margin=\"normal\"\r\n                slotProps={{\r\n                  input: {\r\n                    startAdornment: (\r\n                      <InputAdornment position=\"start\">\r\n                        <LocationOn color=\"action\" />\r\n                      </InputAdornment>\r\n                    )\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ mt: 3 }}>\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"primary\"\r\n                  startIcon={<Lock />}\r\n                  onClick={handlePasswordDialogOpen}\r\n                  fullWidth\r\n                  sx={{\r\n                    borderRadius: 2,\r\n                    py: 1,\r\n                    textTransform: 'none',\r\n                    fontWeight: 500\r\n                  }}\r\n                >\r\n                  {t('profile.changePassword')}\r\n                </Button>\r\n              </Box>\r\n            </Grid>\r\n\r\n            {/* About & Skills */}\r\n            <Grid item xs={12} md={6}>\r\n              <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\r\n                <Work color=\"primary\" sx={{ verticalAlign: 'middle', mr: 1 }} />\r\n                {t('profile.professionalDetails')}\r\n              </Typography>\r\n\r\n              <TextField\r\n                label={t('profile.role')}\r\n                name=\"role\"\r\n                fullWidth\r\n                value={translateRole(form.role || \"Etudiant\")}\r\n                disabled\r\n                margin=\"normal\"\r\n                helperText={t('profile.contactAdminRole')}\r\n                InputProps={{\r\n                  sx: {\r\n                    textTransform: 'capitalize'\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <TextField\r\n                label={t('profile.aboutMe')}\r\n                name=\"about\"\r\n                fullWidth\r\n                multiline\r\n                rows={4}\r\n                value={form.about || \"\"}\r\n                onChange={handleChange}\r\n                margin=\"normal\"\r\n                sx={{ mt: 2 }}\r\n              />\r\n\r\n              <Box sx={{ mt: 3 }}>\r\n                <Typography variant=\"subtitle1\" gutterBottom>\r\n                  {t('profile.skillsExpertise')}\r\n                </Typography>\r\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>\r\n                  {skills.map((skill, idx) => (\r\n                    <Chip\r\n                      key={idx}\r\n                      label={skill}\r\n                      onDelete={() => handleRemoveSkill(skill)}\r\n                      color=\"primary\"\r\n                      variant=\"outlined\"\r\n                      deleteIcon={<Check fontSize=\"small\" />}\r\n                      sx={{ borderRadius: 1 }}\r\n                    />\r\n                  ))}\r\n                </Box>\r\n                <Box sx={{ position: 'relative' }} className=\"skills-dropdown-container\">\r\n                  <Box sx={{ display: 'flex', gap: 1 }}>\r\n                    <TextField\r\n                      label={t('profile.addSkill')}\r\n                      value={newSkill}\r\n                      onChange={(e) => {\r\n                        setNewSkill(e.target.value);\r\n                        setShowSkillsDropdown(e.target.value.length > 0);\r\n                      }}\r\n                      onFocus={() => setShowSkillsDropdown(newSkill.length > 0)}\r\n                      size=\"small\"\r\n                      fullWidth\r\n                    />\r\n                    <Button\r\n                      onClick={() => handleAddSkill()}\r\n                      variant=\"contained\"\r\n                      startIcon={<Add />}\r\n                      sx={{ whiteSpace: 'nowrap' }}\r\n                    >\r\n                      {t('common.add')}\r\n                    </Button>\r\n                  </Box>\r\n                  {showSkillsDropdown && filteredSkills.length > 0 && (\r\n                    <Paper\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        top: '100%',\r\n                        left: 0,\r\n                        right: 0,\r\n                        zIndex: 1000,\r\n                        maxHeight: 200,\r\n                        overflowY: 'auto',\r\n                        mt: 1\r\n                      }}\r\n                    >\r\n                      {filteredSkills.slice(0, 15).map((skill) => (\r\n                        <Box\r\n                          key={skill}\r\n                          onClick={() => handleAddSkill(skill)}\r\n                          sx={{\r\n                            p: 1,\r\n                            cursor: 'pointer',\r\n                            '&:hover': {\r\n                              bgcolor: 'action.hover'\r\n                            }\r\n                          }}\r\n                        >\r\n                          {skill}\r\n                        </Box>\r\n                      ))}\r\n                    </Paper>\r\n                  )}\r\n                </Box>\r\n              </Box>\r\n            </Grid>\r\n\r\n            {/* Submit Button */}\r\n            <Grid item xs={12}>\r\n              <Divider sx={{ my: 3 }} />\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"contained\"\r\n                  size=\"large\"\r\n                  disabled={submitting}\r\n                  sx={{\r\n                    px: 6,\r\n                    py: 1.5,\r\n                    borderRadius: 20,\r\n                    fontSize: '1rem',\r\n                    '&:hover': {\r\n                      transform: 'translateY(-2px)',\r\n                      boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)'\r\n                    }\r\n                  }}\r\n                >\r\n                  {submitting ? (\r\n                    <>\r\n                      <CircularProgress size={24} sx={{ mr: 1 }} />\r\n                      {t('common.saving')}\r\n                    </>\r\n                  ) : (\r\n                    t('common.saveChanges')\r\n                  )}\r\n                </Button>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </form>\r\n      </Paper>\r\n\r\n      {/* Dialog pour changer le mot de passe */}\r\n      <Dialog\r\n        open={passwordDialogOpen}\r\n        onClose={handlePasswordDialogClose}\r\n        maxWidth=\"sm\"\r\n        fullWidth\r\n      >\r\n        <DialogTitle sx={{ fontWeight: 600 }}>\r\n          {t('profile.changePassword')}\r\n        </DialogTitle>\r\n\r\n        <DialogContent>\r\n          {passwordError && (\r\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n              {passwordError}\r\n            </Alert>\r\n          )}\r\n\r\n          <DialogContentText sx={{ mb: 2 }}>\r\n            {t('profile.changePasswordDescription')}\r\n          </DialogContentText>\r\n\r\n          <TextField\r\n            label={t('profile.currentPassword')}\r\n            name=\"currentPassword\"\r\n            type={showCurrentPassword ? \"text\" : \"password\"}\r\n            value={passwordForm.currentPassword}\r\n            onChange={handlePasswordChange}\r\n            fullWidth\r\n            margin=\"normal\"\r\n            InputProps={{\r\n              endAdornment: (\r\n                <InputAdornment position=\"end\">\r\n                  <IconButton\r\n                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}\r\n                    edge=\"end\"\r\n                  >\r\n                    {showCurrentPassword ? <VisibilityOff /> : <Visibility />}\r\n                  </IconButton>\r\n                </InputAdornment>\r\n              )\r\n            }}\r\n          />\r\n\r\n          <TextField\r\n            label={t('profile.newPassword')}\r\n            name=\"newPassword\"\r\n            type={showNewPassword ? \"text\" : \"password\"}\r\n            value={passwordForm.newPassword}\r\n            onChange={handlePasswordChange}\r\n            fullWidth\r\n            margin=\"normal\"\r\n            InputProps={{\r\n              endAdornment: (\r\n                <InputAdornment position=\"end\">\r\n                  <IconButton\r\n                    onClick={() => setShowNewPassword(!showNewPassword)}\r\n                    edge=\"end\"\r\n                  >\r\n                    {showNewPassword ? <VisibilityOff /> : <Visibility />}\r\n                  </IconButton>\r\n                </InputAdornment>\r\n              )\r\n            }}\r\n          />\r\n\r\n          {/* Indicateur de force du mot de passe */}\r\n          {passwordForm.newPassword && (\r\n            <Box sx={{ mt: 1, mb: 2 }}>\r\n              <Typography variant=\"body2\" gutterBottom>\r\n                {t('profile.passwordStrength')}:\r\n                <Box component=\"span\"\r\n                  sx={{\r\n                    ml: 1,\r\n                    fontWeight: 'bold',\r\n                    color: passwordStrength === t('profile.passwordStrengthStrong')\r\n                      ? 'success.main'\r\n                      : passwordStrength === t('profile.passwordStrengthMedium')\r\n                        ? 'warning.main'\r\n                        : 'error.main'\r\n                  }}\r\n                >\r\n                  {passwordStrength.toUpperCase()}\r\n                </Box>\r\n              </Typography>\r\n\r\n              {/* Barre de progression */}\r\n              <Box sx={{ width: '100%', height: 4, bgcolor: 'grey.200', borderRadius: 1, overflow: 'hidden' }}>\r\n                <Box\r\n                  sx={{\r\n                    height: '100%',\r\n                    width: passwordStrength === t('profile.passwordStrengthStrong')\r\n                      ? '100%'\r\n                      : passwordStrength === t('profile.passwordStrengthMedium')\r\n                        ? '60%'\r\n                        : '30%',\r\n                    bgcolor: passwordStrength === t('profile.passwordStrengthStrong')\r\n                      ? 'success.main'\r\n                      : passwordStrength === t('profile.passwordStrengthMedium')\r\n                        ? 'warning.main'\r\n                        : 'error.main',\r\n                    transition: 'width 0.3s ease'\r\n                  }}\r\n                />\r\n              </Box>\r\n\r\n              {/* Conseils pour un mot de passe fort */}\r\n              {passwordStrength !== t('profile.passwordStrengthStrong') && (\r\n                <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                  <Typography variant=\"subtitle2\" gutterBottom color=\"primary\">\r\n                    {t('profile.strongPasswordTips')}:\r\n                  </Typography>\r\n                  <Grid container spacing={1}>\r\n                    <Grid item xs={12} sm={6}>\r\n                      <Typography variant=\"body2\"\r\n                        color={passwordForm.newPassword.length >= 8 ? 'success.main' : 'text.secondary'}\r\n                        sx={{ display: 'flex', alignItems: 'center' }}\r\n                      >\r\n                        {passwordForm.newPassword.length >= 8 ? '✓' : '○'} {t('profile.atLeast8Chars')}\r\n                      </Typography>\r\n                    </Grid>\r\n                    <Grid item xs={12} sm={6}>\r\n                      <Typography variant=\"body2\"\r\n                        color={/[A-Z]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary'}\r\n                        sx={{ display: 'flex', alignItems: 'center' }}\r\n                      >\r\n                        {/[A-Z]/.test(passwordForm.newPassword) ? '✓' : '○'} {t('profile.oneUppercase')}\r\n                      </Typography>\r\n                    </Grid>\r\n                    <Grid item xs={12} sm={6}>\r\n                      <Typography variant=\"body2\"\r\n                        color={/[a-z]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary'}\r\n                        sx={{ display: 'flex', alignItems: 'center' }}\r\n                      >\r\n                        {/[a-z]/.test(passwordForm.newPassword) ? '✓' : '○'} {t('profile.oneLowercase')}\r\n                      </Typography>\r\n                    </Grid>\r\n                    <Grid item xs={12} sm={6}>\r\n                      <Typography variant=\"body2\"\r\n                        color={/\\d/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary'}\r\n                        sx={{ display: 'flex', alignItems: 'center' }}\r\n                      >\r\n                        {/\\d/.test(passwordForm.newPassword) ? '✓' : '○'} {t('profile.oneDigit')}\r\n                      </Typography>\r\n                    </Grid>\r\n                    <Grid item xs={12}>\r\n                      <Typography variant=\"body2\"\r\n                        color={/[!@#$%^&*(),.?\":{}|<>]/.test(passwordForm.newPassword) ? 'success.main' : 'text.secondary'}\r\n                        sx={{ display: 'flex', alignItems: 'center' }}\r\n                      >\r\n                        {/[!@#$%^&*(),.?\":{}|<>]/.test(passwordForm.newPassword) ? '✓' : '○'} {t('profile.oneSpecialChar')}\r\n                      </Typography>\r\n                    </Grid>\r\n                  </Grid>\r\n                </Box>\r\n              )}\r\n            </Box>\r\n          )}\r\n\r\n          <TextField\r\n            label={t('profile.confirmNewPassword')}\r\n            name=\"confirmPassword\"\r\n            type={showConfirmPassword ? \"text\" : \"password\"}\r\n            value={passwordForm.confirmPassword}\r\n            onChange={handlePasswordChange}\r\n            fullWidth\r\n            margin=\"normal\"\r\n            InputProps={{\r\n              endAdornment: (\r\n                <InputAdornment position=\"end\">\r\n                  <IconButton\r\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                    edge=\"end\"\r\n                  >\r\n                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\r\n                  </IconButton>\r\n                </InputAdornment>\r\n              )\r\n            }}\r\n          />\r\n        </DialogContent>\r\n\r\n        <DialogActions sx={{ px: 3, pb: 3 }}>\r\n          <Button\r\n            onClick={handlePasswordDialogClose}\r\n            variant=\"outlined\"\r\n            disabled={changingPassword}\r\n          >\r\n            {t('common.cancel')}\r\n          </Button>\r\n          <Button\r\n            onClick={handleChangePassword}\r\n            variant=\"contained\"\r\n            disabled={changingPassword}\r\n            startIcon={changingPassword ? <CircularProgress size={20} /> : <Lock />}\r\n          >\r\n            {changingPassword ? t('common.updating') : t('profile.updatePassword')}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default EditProfilePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,SAAS,EACTC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,aAAa,QACR,qBAAqB;AAC5B,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA;EAC5B,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAM,CAAC,GAAG1B,SAAS,CAAC,CAAC;EAC7B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmD,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiE,MAAM,EAAEC,SAAS,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMqE,mBAAmB,GAAG,CAC1B,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAC/H,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAC5G,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAC/F,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAC/G,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EACrH,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EACpE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EACzH,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAC7G,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,aAAa,EACvH,mBAAmB,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAChG,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EACtH,YAAY,EAAE,eAAe,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EACtH,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAC7G,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAC3G,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,CAClH;EAED,MAAMC,gBAAgB,GAAGD,mBAAmB,CAACE,GAAG,CAACC,GAAG,IAAI1B,CAAC,CAAC,UAAU0B,GAAG,EAAE,CAAC,CAAC;EAC3E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAC;IAC/CiF,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAMgG,aAAa,GAAIC,IAAI,IAAK;IAC9B,IAAI,CAACA,IAAI,EAAE,OAAOnD,CAAC,CAAC,eAAe,CAAC;IACpC,MAAMoD,OAAO,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;IAClC,OAAOrD,CAAC,CAAC,QAAQoD,OAAO,EAAE,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAIC,QAAQ,GAAG,CAAC;;IAEhB;IACA,MAAMC,cAAc,GAAGF,QAAQ,CAACG,MAAM,IAAI,CAAC;IAC3C,MAAMC,YAAY,GAAG,OAAO,CAACC,IAAI,CAACL,QAAQ,CAAC;IAC3C,MAAMM,YAAY,GAAG,OAAO,CAACD,IAAI,CAACL,QAAQ,CAAC;IAC3C,MAAMO,QAAQ,GAAG,IAAI,CAACF,IAAI,CAACL,QAAQ,CAAC;IACpC,MAAMQ,cAAc,GAAG,wBAAwB,CAACH,IAAI,CAACL,QAAQ,CAAC;;IAE9D;IACAC,QAAQ,IAAIC,cAAc,GAAG,CAAC,GAAG,CAAC;IAClCD,QAAQ,IAAIG,YAAY,GAAG,CAAC,GAAG,CAAC;IAChCH,QAAQ,IAAIK,YAAY,GAAG,CAAC,GAAG,CAAC;IAChCL,QAAQ,IAAIM,QAAQ,GAAG,CAAC,GAAG,CAAC;IAC5BN,QAAQ,IAAIO,cAAc,GAAG,CAAC,GAAG,CAAC;;IAElC;IACA,IAAIP,QAAQ,IAAI,CAAC,EAAE,OAAOxD,CAAC,CAAC,8BAA8B,CAAC;IAC3D,IAAIwD,QAAQ,IAAI,CAAC,EAAE,OAAOxD,CAAC,CAAC,gCAAgC,CAAC;IAC7D,OAAOA,CAAC,CAAC,gCAAgC,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMgE,mBAAmB,GAAIC,IAAI,IAAK;IACpC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEnCN,GAAG,CAACO,MAAM,GAAG,MAAM;QACjB;QACA,MAAMC,OAAO,GAAG,GAAG;QACnB,IAAI;UAAEC,KAAK;UAAEC;QAAO,CAAC,GAAGV,GAAG;QAE3B,IAAIS,KAAK,GAAGC,MAAM,EAAE;UAClB,IAAID,KAAK,GAAGD,OAAO,EAAE;YACnBE,MAAM,GAAIA,MAAM,GAAGF,OAAO,GAAIC,KAAK;YACnCA,KAAK,GAAGD,OAAO;UACjB;QACF,CAAC,MAAM;UACL,IAAIE,MAAM,GAAGF,OAAO,EAAE;YACpBC,KAAK,GAAIA,KAAK,GAAGD,OAAO,GAAIE,MAAM;YAClCA,MAAM,GAAGF,OAAO;UAClB;QACF;QAEAN,MAAM,CAACO,KAAK,GAAGA,KAAK;QACpBP,MAAM,CAACQ,MAAM,GAAGA,MAAM;QACtBL,GAAG,CAACM,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,EAAES,KAAK,EAAEC,MAAM,CAAC;;QAEvC;QACA,MAAME,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEJ,KAAK,EAAEC,MAAM,CAAC;QACvD,MAAMI,IAAI,GAAGF,SAAS,CAACE,IAAI;;QAE3B;QACA,IAAIC,GAAG,GAAG,CAAC;QACX,IAAIC,UAAU,GAAG,CAAC;QAClB,IAAIC,KAAK,GAAG,CAAC;;QAEb;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,GAAG,CAAC,EAAEQ,CAAC,EAAE,EAAE;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;YAClC,MAAMC,GAAG,GAAG,CAACF,CAAC,GAAGT,KAAK,GAAGU,CAAC,IAAI,CAAC;;YAE/B;YACA;;YAEA;YACA,MAAME,QAAQ,GAAG,KAAK,GAAGP,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGN,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGN,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC;YACtF,MAAME,SAAS,GAAG,KAAK,GAAGR,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGN,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGN,IAAI,CAACM,GAAG,GAAG,CAAC,CAAC;YACvF,MAAMG,KAAK,GAAGD,SAAS,GAAGD,QAAQ;;YAElC;YACA,MAAMG,OAAO,GAAG,KAAK,GAAGV,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGK,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGK,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;YACrH,MAAMgB,UAAU,GAAG,KAAK,GAAGX,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGK,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGK,IAAI,CAACM,GAAG,GAAGX,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;YACxH,MAAMiB,KAAK,GAAGD,UAAU,GAAGD,OAAO;;YAElC;YACA,MAAMG,SAAS,GAAGC,IAAI,CAACC,IAAI,CAACN,KAAK,GAAGA,KAAK,GAAGG,KAAK,GAAGA,KAAK,CAAC;YAE1DX,GAAG,IAAIY,SAAS;YAChBX,UAAU,IAAIW,SAAS,GAAGA,SAAS;YACnCV,KAAK,EAAE;UACT;QACF;;QAEA;QACA,MAAMa,IAAI,GAAGf,GAAG,GAAGE,KAAK;QACxB,MAAMc,QAAQ,GAAIf,UAAU,GAAGC,KAAK,GAAKa,IAAI,GAAGA,IAAK;;QAErD;QACA;QACA,IAAIE,OAAO;QACX,IAAID,QAAQ,GAAG,IAAI,EAAE;UACnBC,OAAO,GAAG;YAAEC,KAAK,EAAE,WAAW;YAAEC,KAAK,EAAEH,QAAQ;YAAEI,QAAQ,EAAE;UAAM,CAAC;QACpE,CAAC,MAAM,IAAIJ,QAAQ,GAAG,GAAG,EAAE;UACzBC,OAAO,GAAG;YAAEC,KAAK,EAAE,MAAM;YAAEC,KAAK,EAAEH,QAAQ;YAAEI,QAAQ,EAAE;UAAM,CAAC;QAC/D,CAAC,MAAM,IAAIJ,QAAQ,GAAG,GAAG,EAAE;UACzBC,OAAO,GAAG;YAAEC,KAAK,EAAE,YAAY;YAAEC,KAAK,EAAEH,QAAQ;YAAEI,QAAQ,EAAE;UAAM,CAAC;QACrE,CAAC,MAAM,IAAIJ,QAAQ,GAAG,GAAG,EAAE;UACzBC,OAAO,GAAG;YAAEC,KAAK,EAAE,MAAM;YAAEC,KAAK,EAAEH,QAAQ;YAAEI,QAAQ,EAAE;UAAK,CAAC;QAC9D,CAAC,MAAM;UACLH,OAAO,GAAG;YAAEC,KAAK,EAAE,WAAW;YAAEC,KAAK,EAAEH,QAAQ;YAAEI,QAAQ,EAAE;UAAK,CAAC;QACnE;QAEApC,OAAO,CAACiC,OAAO,CAAC;MAClB,CAAC;MAEDhC,GAAG,CAACoC,OAAO,GAAG,MAAM;QAClBrC,OAAO,CAAC;UAAEkC,KAAK,EAAE,OAAO;UAAEC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAK,CAAC,CAAC;MACvD,CAAC;MAEDnC,GAAG,CAACqC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAAC1C,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC;EAEDhH,SAAS,CAAC,MAAM;IACd,MAAM2J,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAAE;QACvDzF,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAEDiD,QAAQ,CAACyC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXrC,QAAQ,CAAC0C,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN3J,SAAS,CAAC,MAAM;IACd,MAAMiK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF1G,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA;QACA,MAAM2G,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;QAC5D,IAAIF,cAAc,EAAE;UAClB,IAAI;YACF,MAAMG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;YAC9CM,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEJ,WAAW,CAAC;YAEjE,IAAIA,WAAW,IAAIA,WAAW,CAACrH,KAAK,EAAE;cACpC;cACAG,OAAO,CAACkH,WAAW,CAAC;cACpBhH,OAAO,CAACgH,WAAW,CAAC;;cAEpB;cACA,IAAIA,WAAW,CAACnG,MAAM,EAAE;gBACtB,IAAI,OAAOmG,WAAW,CAACnG,MAAM,KAAK,QAAQ,EAAE;kBAC1C,IAAI;oBACF,MAAMwG,YAAY,GAAGJ,IAAI,CAACC,KAAK,CAACF,WAAW,CAACnG,MAAM,CAAC;oBACnDC,SAAS,CAACwG,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;kBAC5D,CAAC,CAAC,OAAOG,QAAQ,EAAE;oBACjBL,OAAO,CAAC9G,KAAK,CAAC,uBAAuB,EAAEmH,QAAQ,CAAC;oBAChD1G,SAAS,CAAC,EAAE,CAAC;kBACf;gBACF,CAAC,MAAM,IAAIwG,KAAK,CAACC,OAAO,CAACP,WAAW,CAACnG,MAAM,CAAC,EAAE;kBAC5CC,SAAS,CAACkG,WAAW,CAACnG,MAAM,CAAC;gBAC/B,CAAC,MAAM;kBACLC,SAAS,CAAC,EAAE,CAAC;gBACf;cACF,CAAC,MAAM;gBACLA,SAAS,CAAC,EAAE,CAAC;cACf;;cAEA;cACA,IAAI;gBACFqG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEJ,WAAW,CAACrH,KAAK,CAAC;gBACxE,MAAM8H,GAAG,GAAG,MAAMzI,KAAK,CAAC0I,GAAG,CAAC,qCAAqCV,WAAW,CAACrH,KAAK,EAAE,CAAC;gBACrFwH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,GAAG,CAAC7C,IAAI,CAAC;gBAErD,IAAI6C,GAAG,CAAC7C,IAAI,EAAE;kBACZ9E,OAAO,CAAC2H,GAAG,CAAC7C,IAAI,CAAC;kBACjB5E,OAAO,CAACyH,GAAG,CAAC7C,IAAI,CAAC;;kBAEjB;kBACA,IAAI6C,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,EAAE;oBACnB,IAAI,OAAO4G,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,KAAK,QAAQ,EAAE;sBACvC,IAAI;wBACF,MAAMwG,YAAY,GAAGJ,IAAI,CAACC,KAAK,CAACO,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC;wBAChDC,SAAS,CAACwG,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;sBAC5D,CAAC,CAAC,OAAOG,QAAQ,EAAE;wBACjBL,OAAO,CAAC9G,KAAK,CAAC,mCAAmC,EAAEmH,QAAQ,CAAC;sBAC9D;oBACF,CAAC,MAAM,IAAIF,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC,EAAE;sBACzCC,SAAS,CAAC2G,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC;oBAC5B;kBACF;gBACF;cACF,CAAC,CAAC,OAAO8G,SAAS,EAAE;gBAClBR,OAAO,CAAC9G,KAAK,CAAC,oCAAoC,EAAEsH,SAAS,CAAC;gBAC9D;cACF;;cAEA;cACAb,cAAc,CAACc,UAAU,CAAC,aAAa,CAAC;cACxC1H,UAAU,CAAC,KAAK,CAAC;cACjB,OAAO,CAAC;YACV;UACF,CAAC,CAAC,OAAOsH,QAAQ,EAAE;YACjBL,OAAO,CAAC9G,KAAK,CAAC,iDAAiD,EAAEmH,QAAQ,CAAC;UAC5E;QACF;;QAEA;QACA;;QAEA;QACA,IAAIK,SAAS,GAAGlI,KAAK;QACrB,IAAI,CAACkI,SAAS,EAAE;UACdV,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;UAC5E,MAAMU,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;UAC/C,IAAIe,UAAU,EAAE;YACd,IAAI;cACF,MAAME,QAAQ,GAAGf,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;cACvC,IAAIE,QAAQ,IAAIA,QAAQ,CAACrI,KAAK,EAAE;gBAC9BkI,SAAS,GAAGG,QAAQ,CAACrI,KAAK;gBAC1BwH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAES,SAAS,CAAC;cAC1D;YACF,CAAC,CAAC,OAAOI,GAAG,EAAE;cACZd,OAAO,CAAC9G,KAAK,CAAC,4CAA4C,EAAE4H,GAAG,CAAC;YAClE;UACF;QACF;QAEA,IAAI,CAACJ,SAAS,EAAE;UACd,MAAM,IAAIK,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEA,IAAI;UACFf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,SAAS,CAAC;UACvD,MAAMJ,GAAG,GAAG,MAAMzI,KAAK,CAAC0I,GAAG,CAAC,qCAAqCG,SAAS,EAAE,CAAC;UAC7EV,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEK,GAAG,CAAC7C,IAAI,CAAC;UAE5C,IAAI,CAAC6C,GAAG,CAAC7C,IAAI,EAAE;YACb,MAAM,IAAIsD,KAAK,CAAC,mCAAmC,CAAC;UACtD;UAEApI,OAAO,CAAC2H,GAAG,CAAC7C,IAAI,CAAC;UACjB5E,OAAO,CAACyH,GAAG,CAAC7C,IAAI,CAAC;;UAEjB;UACA,IAAI6C,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,EAAE;YACnB;YACA,IAAI,OAAO4G,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,KAAK,QAAQ,EAAE;cACvC,IAAI;gBACF,MAAMwG,YAAY,GAAGJ,IAAI,CAACC,KAAK,CAACO,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC;gBAChDC,SAAS,CAACwG,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC;cAC5D,CAAC,CAAC,OAAOG,QAAQ,EAAE;gBACjBL,OAAO,CAAC9G,KAAK,CAAC,uBAAuB,EAAEmH,QAAQ,CAAC;gBAChD1G,SAAS,CAAC,EAAE,CAAC;cACf;YACF,CAAC,MAAM,IAAIwG,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC,EAAE;cACzCC,SAAS,CAAC2G,GAAG,CAAC7C,IAAI,CAAC/D,MAAM,CAAC;YAC5B,CAAC,MAAM;cACLC,SAAS,CAAC,EAAE,CAAC;YACf;UACF,CAAC,MAAM;YACLA,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,CAAC,OAAOqH,QAAQ,EAAE;UACjBhB,OAAO,CAAC9G,KAAK,CAAC,2BAA2B,EAAE8H,QAAQ,CAAC;;UAEpD;UACA,MAAML,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;UAC/C,IAAIe,UAAU,EAAE;YACd,IAAI;cACF,MAAME,QAAQ,GAAGf,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;cACvCX,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEY,QAAQ,CAAC;cACvElI,OAAO,CAACkI,QAAQ,CAAC;cACjBhI,OAAO,CAACgI,QAAQ,CAAC;cAEjB,IAAIA,QAAQ,CAACnH,MAAM,EAAE;gBACnBC,SAAS,CAACwG,KAAK,CAACC,OAAO,CAACS,QAAQ,CAACnH,MAAM,CAAC,GAAGmH,QAAQ,CAACnH,MAAM,GAAG,EAAE,CAAC;cAClE,CAAC,MAAM;gBACLC,SAAS,CAAC,EAAE,CAAC;cACf;YACF,CAAC,CAAC,OAAO0G,QAAQ,EAAE;cACjBL,OAAO,CAAC9G,KAAK,CAAC,4CAA4C,EAAEmH,QAAQ,CAAC;cACrE,MAAM,IAAIU,KAAK,CAAC,0BAA0B,CAAC;YAC7C;UACF,CAAC,MAAM;YACL,MAAM,IAAIA,KAAK,CAAC,uDAAuD,CAAC;UAC1E;QACF;MACF,CAAC,CAAC,OAAOD,GAAG,EAAE;QACZd,OAAO,CAAC9G,KAAK,CAAC,qBAAqB,EAAE4H,GAAG,CAAC;QACzC3H,QAAQ,CAAC,0BAA0B2H,GAAG,CAACG,OAAO,EAAE,CAAC;MACnD,CAAC,SAAS;QACRlI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED0G,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjH,KAAK,EAAEC,QAAQ,CAAC,CAAC;EAErB,MAAMyI,YAAY,GAAIC,CAAC,IAAK;IAC1BtI,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAACuI,CAAC,CAAC9B,MAAM,CAAC+B,IAAI,GAAGD,CAAC,CAAC9B,MAAM,CAACgC;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAOH,CAAC,IAAK;IACpC,MAAM3E,IAAI,GAAG2E,CAAC,CAAC9B,MAAM,CAACkC,KAAK,CAAC,CAAC,CAAC;IAE9B,IAAI,CAAC/E,IAAI,EAAE;MACTnD,eAAe,CAAC,IAAI,CAAC;MACrBE,UAAU,CAAC,EAAE,CAAC;MACdY,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;;IAEA;IACA,IAAI,CAACqC,IAAI,CAACgF,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC3J,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,0BAA0B,CAAC,CAAC;MAC1C4I,CAAC,CAAC9B,MAAM,CAACgC,KAAK,GAAG,EAAE;MACnB;IACF;;IAEA;IACA,IAAI7E,IAAI,CAACkF,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B5J,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACvC4I,CAAC,CAAC9B,MAAM,CAACgC,KAAK,GAAG,EAAE;MACnB;IACF;IAEA9H,UAAU,CAAC0F,GAAG,CAACC,eAAe,CAAC1C,IAAI,CAAC,CAAC;IACrCnC,mBAAmB,CAAC,IAAI,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMwE,OAAO,GAAG,MAAMpC,mBAAmB,CAACC,IAAI,CAAC;MAC/CrC,eAAe,CAACwE,OAAO,CAAC;MAExB,IAAIA,OAAO,CAACG,QAAQ,EAAE;QACpBhH,KAAK,CAAC6J,OAAO,CAACpJ,CAAC,CAAC,4BAA4B,EAAE;UAAEoG,OAAO,EAAEA,OAAO,CAACC;QAAM,CAAC,CAAC,CAAC;QAC1E;QACAvF,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLvB,KAAK,CAAC8J,OAAO,CAACrJ,CAAC,CAAC,0BAA0B,EAAE;UAAEoG,OAAO,EAAEA,OAAO,CAACC;QAAM,CAAC,CAAC,CAAC;QACxEvF,eAAe,CAACmD,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACd8G,OAAO,CAAC9G,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DpB,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,4BAA4B,CAAC,CAAC;MAC5C4B,eAAe,CAAC;QAAEyE,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC7DzF,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,SAAS;MACRgB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwH,cAAc,GAAGA,CAACC,UAAU,GAAG,IAAI,KAAK;IAC5C,MAAMC,KAAK,GAAGD,UAAU,IAAItI,QAAQ,CAACwI,IAAI,CAAC,CAAC;IAC3C,IAAID,KAAK,IAAI,CAACrI,MAAM,CAACuI,QAAQ,CAACF,KAAK,CAAC,EAAE;MACpCpI,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEqI,KAAK,CAAC,CAAC;MAC7BtI,WAAW,CAAC,EAAE,CAAC;MACfI,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqI,cAAc,GAAGnI,gBAAgB,CAACoI,MAAM,CAACJ,KAAK,IAClDA,KAAK,CAACnG,WAAW,CAAC,CAAC,CAACqG,QAAQ,CAACzI,QAAQ,CAACoC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAClC,MAAM,CAACuI,QAAQ,CAACF,KAAK,CAChF,CAAC;EAED,MAAMK,iBAAiB,GAAIC,aAAa,IAAK;IAC3C1I,SAAS,CAACD,MAAM,CAACyI,MAAM,CAACJ,KAAK,IAAIA,KAAK,KAAKM,aAAa,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMC,SAAS,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,oBAAoB,CAAC;IAC9D,IAAID,SAAS,IAAIA,SAAS,CAAChB,KAAK,CAAC,CAAC,CAAC,EAAE;MACnClI,eAAe,CAACkJ,SAAS,CAAChB,KAAK,CAAC,CAAC,CAAC,CAAC;MACnCzJ,KAAK,CAAC2K,IAAI,CAAClK,CAAC,CAAC,6BAA6B,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMmK,wBAAwB,GAAGA,CAAA,KAAM;IACrCnI,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,eAAe,CAAC;MACdC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFE,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAM6H,yBAAyB,GAAGA,CAAA,KAAM;IACtCpI,qBAAqB,CAAC,KAAK,CAAC;IAC5BO,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAM8H,oBAAoB,GAAIzB,CAAC,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAAC9B,MAAM;IAChC5E,eAAe,CAAC;MACd,GAAGD,YAAY;MACf,CAAC4G,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAID,IAAI,KAAK,aAAa,EAAE;MAC1B5F,mBAAmB,CAACK,mBAAmB,CAACwF,KAAK,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMwB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAACrI,YAAY,CAACE,eAAe,EAAE;MACjCI,gBAAgB,CAACvC,CAAC,CAAC,iCAAiC,CAAC,CAAC;MACtD;IACF;IAEA,IAAI,CAACiC,YAAY,CAACG,WAAW,EAAE;MAC7BG,gBAAgB,CAACvC,CAAC,CAAC,6BAA6B,CAAC,CAAC;MAClD;IACF;IAEA,IAAIiC,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC7DE,gBAAgB,CAACvC,CAAC,CAAC,6BAA6B,CAAC,CAAC;MAClD;IACF;IAEA,IAAIiC,YAAY,CAACG,WAAW,CAACsB,MAAM,GAAG,CAAC,EAAE;MACvCnB,gBAAgB,CAACvC,CAAC,CAAC,0BAA0B,CAAC,CAAC;MAC/C;IACF;IAEA+C,mBAAmB,CAAC,IAAI,CAAC;IACzBR,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF;MACA,MAAMjD,KAAK,CAACiL,IAAI,CAAC,4CAA4C,EAAE;QAC7DtK,KAAK,EAAEE,IAAI,CAACF,KAAK;QACjBkC,eAAe,EAAEF,YAAY,CAACE,eAAe;QAC7CC,WAAW,EAAEH,YAAY,CAACG,WAAW;QACrCoI,gBAAgB,EAAE;MACpB,CAAC,CAAC;MAEFjL,KAAK,CAAC8J,OAAO,CAACrJ,CAAC,CAAC,gCAAgC,CAAC,CAAC;MAClDT,KAAK,CAAC2K,IAAI,CAAClK,CAAC,CAAC,iCAAiC,CAAC,CAAC;MAChDoK,yBAAyB,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOzJ,KAAK,EAAE;MAAA,IAAA8J,eAAA,EAAAC,oBAAA;MACdjD,OAAO,CAAC9G,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD4B,gBAAgB,CAAC,EAAAkI,eAAA,GAAA9J,KAAK,CAACgK,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvF,IAAI,cAAAwF,oBAAA,uBAApBA,oBAAA,CAAsBhC,OAAO,KAAI1I,CAAC,CAAC,6BAA6B,CAAC,CAAC;IACrF,CAAC,SAAS;MACR+C,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6H,YAAY,GAAG,MAAOhC,CAAC,IAAK;IAChCA,CAAC,CAACiC,cAAc,CAAC,CAAC;IAClBnK,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIC,YAAY,IAAIc,YAAY,IAAIA,YAAY,CAAC4E,QAAQ,EAAE;MACzD,MAAMuE,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClChL,CAAC,CAAC,6BAA6B,EAAE;QAAEoG,OAAO,EAAEzE,YAAY,CAAC0E;MAAM,CAAC,CAClE,CAAC;MAED,IAAI,CAACyE,aAAa,EAAE;QAClBpK,aAAa,CAAC,KAAK,CAAC;QACpB;MACF;IACF;IAEA,IAAI;MACF;MACA,MAAMyH,SAAS,GAAGhI,IAAI,CAACF,KAAK;MAC5B,IAAIgL,MAAM,GAAG9K,IAAI,CAAC+K,EAAE;;MAEpB;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGE,QAAQ,CAACF,MAAM,EAAE,EAAE,CAAC;QAC7B,IAAIG,KAAK,CAACH,MAAM,CAAC,EAAE;UACjBxD,OAAO,CAAC9G,KAAK,CAAC,yBAAyB,EAAER,IAAI,CAAC+K,EAAE,CAAC;UACjD;UACA,IAAI;YACF,MAAMG,YAAY,GAAG,MAAM/L,KAAK,CAAC0I,GAAG,CAAC,qCAAqCG,SAAS,EAAE,CAAC;YACtF,IAAIkD,YAAY,CAACnG,IAAI,IAAImG,YAAY,CAACnG,IAAI,CAACgG,EAAE,EAAE;cAC7CD,MAAM,GAAGI,YAAY,CAACnG,IAAI,CAACgG,EAAE;cAC7BzD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuD,MAAM,CAAC;YACpD;UACF,CAAC,CAAC,OAAOK,KAAK,EAAE;YACd7D,OAAO,CAAC9G,KAAK,CAAC,sCAAsC,EAAE2K,KAAK,CAAC;UAC9D;QACF;MACF;MAEA,IAAI,CAACnD,SAAS,EAAE;QACd,MAAM,IAAIK,KAAK,CAAC,wCAAwC,CAAC;MAC3D;MAEAf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,SAAS,EAAE,UAAU,EAAE8C,MAAM,CAAC;;MAE3E;MACA;MACA,IAAIM,eAAe,GAAGpK,MAAM;MAC5B,IAAI,CAACyG,KAAK,CAACC,OAAO,CAAC0D,eAAe,CAAC,EAAE;QACnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;UACvC,IAAI;YACF;YACAA,eAAe,GAAGhE,IAAI,CAACC,KAAK,CAAC+D,eAAe,CAAC;YAC7C,IAAI,CAAC3D,KAAK,CAACC,OAAO,CAAC0D,eAAe,CAAC,EAAE;cACnCA,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC;YACvC;UACF,CAAC,CAAC,OAAO3C,CAAC,EAAE;YACV;YACA2C,eAAe,GAAG,CAACA,eAAe,CAAC;UACrC;QACF,CAAC,MAAM,IAAIA,eAAe,EAAE;UAC1B;UACAA,eAAe,GAAG,CAACC,MAAM,CAACD,eAAe,CAAC,CAAC;QAC7C,CAAC,MAAM;UACL;UACAA,eAAe,GAAG,EAAE;QACtB;MACF;;MAEA;MACAA,eAAe,GAAGA,eAAe,CAAC3B,MAAM,CAACJ,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAExEhC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6D,eAAe,CAAC;MAEjD,MAAMjD,QAAQ,GAAG;QACfO,IAAI,EAAExI,IAAI,CAACwI,IAAI,IAAI,IAAI;QACvB4C,KAAK,EAAEpL,IAAI,CAACoL,KAAK,IAAI,IAAI;QACzBC,QAAQ,EAAErL,IAAI,CAACqL,QAAQ,IAAI,IAAI;QAC/BC,KAAK,EAAEtL,IAAI,CAACsL,KAAK,IAAI,IAAI;QACzBxK,MAAM,EAAEoK,eAAe,CAAE;MAC3B,CAAC;MAED9D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,QAAQ,CAAC;MAE7C,IAAI;QACF;QACA,MAAMsD,cAAc,GAAG,MAAMtM,KAAK,CAACuM,KAAK,CAAC,qCAAqC1D,SAAS,EAAE,EAAEG,QAAQ,CAAC;QACpGb,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkE,cAAc,CAAC1G,IAAI,CAAC;QAEpD,IAAI,CAAC0G,cAAc,CAAC1G,IAAI,EAAE;UACxB,MAAM,IAAIsD,KAAK,CAAC,2CAA2C,CAAC;QAC9D;;QAEA;QACA,MAAMsD,WAAW,GAAGF,cAAc,CAAC1G,IAAI;QACvC9E,OAAO,CAAC0L,WAAW,CAAC;;QAEpB;QACA;QACA,MAAM1D,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIe,UAAU,EAAE;UACd,IAAI;YACF,MAAM2D,cAAc,GAAGxE,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;;YAE7C;YACA,IAAI2D,cAAc,CAAC9L,KAAK,KAAKkI,SAAS,EAAE;cACtC;cACA,MAAM6D,eAAe,GAAG;gBACtB,GAAGD,cAAc;gBACjBlD,IAAI,EAAEiD,WAAW,CAACjD,IAAI;gBACtB4C,KAAK,EAAEK,WAAW,CAACL,KAAK;gBACxBC,QAAQ,EAAEI,WAAW,CAACJ,QAAQ;gBAC9BC,KAAK,EAAEG,WAAW,CAACH,KAAK;gBACxBxK,MAAM,EAAE2K,WAAW,CAAC3K,MAAM;gBAC1B8K,UAAU,EAAEH,WAAW,CAACG;cAC1B,CAAC;cACD5D,YAAY,CAAC6D,OAAO,CAAC,MAAM,EAAE3E,IAAI,CAAC4E,SAAS,CAACH,eAAe,CAAC,CAAC;cAC7DvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEsE,eAAe,CAAC;;cAElE;cACA,MAAMI,WAAW,GAAGhF,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;cAClD,IAAI+E,WAAW,EAAE;gBACf,IAAI;kBACF,MAAMC,eAAe,GAAG9E,IAAI,CAACC,KAAK,CAAC4E,WAAW,CAAC;kBAC/C,IAAIC,eAAe,CAACpM,KAAK,KAAKkI,SAAS,EAAE;oBACvCf,cAAc,CAAC8E,OAAO,CAAC,MAAM,EAAE3E,IAAI,CAAC4E,SAAS,CAACH,eAAe,CAAC,CAAC;oBAC/DvE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;kBACpD;gBACF,CAAC,CAAC,OAAOa,GAAG,EAAE;kBACZd,OAAO,CAAC9G,KAAK,CAAC,6CAA6C,EAAE4H,GAAG,CAAC;gBACnE;cACF;YACF,CAAC,MAAM;cACLd,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;YACvF;UACF,CAAC,CAAC,OAAOa,GAAG,EAAE;YACZd,OAAO,CAAC9G,KAAK,CAAC,2CAA2C,EAAE4H,GAAG,CAAC;UACjE;QACF;;QAEA;QACA,IAAI1H,YAAY,IAAIoK,MAAM,EAAE;UAC1B,IAAI;YACFxD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuD,MAAM,CAAC;YAC7D,MAAMqB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;YAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE3L,YAAY,CAAC;;YAEtC;YACA4G,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,kCAAkCuD,MAAM,QAAQ,CAAC;YACjGxD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE7G,YAAY,CAACgI,IAAI,CAAC;YAEjD,MAAM4D,aAAa,GAAG,MAAMnN,KAAK,CAACuM,KAAK,CACrC,kCAAkCZ,MAAM,QAAQ,EAChDqB,QAAQ,EACR;cAAEI,OAAO,EAAE;gBAAE,cAAc,EAAE;cAAsB;YAAE,CACvD,CAAC;YAEDjF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+E,aAAa,CAACvH,IAAI,CAAC;YAEzD,IAAIuH,aAAa,CAACvH,IAAI,IAAIuH,aAAa,CAACvH,IAAI,CAAC+G,UAAU,EAAE;cACvD;cACA7L,OAAO,CAACuM,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEV,UAAU,EAAEQ,aAAa,CAACvH,IAAI,CAAC+G;cAAW,CAAC,CAAC,CAAC;;cAEzE;cACA,MAAM7D,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;cAC/C,IAAIe,UAAU,EAAE;gBACd,IAAI;kBACF,MAAM2D,cAAc,GAAGxE,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;kBAC7C,IAAI2D,cAAc,CAAC9L,KAAK,KAAKkI,SAAS,EAAE;oBACtC4D,cAAc,CAACE,UAAU,GAAGQ,aAAa,CAACvH,IAAI,CAAC+G,UAAU;oBACzD5D,YAAY,CAAC6D,OAAO,CAAC,MAAM,EAAE3E,IAAI,CAAC4E,SAAS,CAACJ,cAAc,CAAC,CAAC;oBAC5DtE,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;oBAEtD;oBACA,MAAM0E,WAAW,GAAGhF,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;oBAClD,IAAI+E,WAAW,EAAE;sBACf,IAAI;wBACF,MAAMC,eAAe,GAAG9E,IAAI,CAACC,KAAK,CAAC4E,WAAW,CAAC;wBAC/C,IAAIC,eAAe,CAACpM,KAAK,KAAKkI,SAAS,EAAE;0BACvCkE,eAAe,CAACJ,UAAU,GAAGQ,aAAa,CAACvH,IAAI,CAAC+G,UAAU;0BAC1D7E,cAAc,CAAC8E,OAAO,CAAC,MAAM,EAAE3E,IAAI,CAAC4E,SAAS,CAACE,eAAe,CAAC,CAAC;0BAC/D5E,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;wBAC1D;sBACF,CAAC,CAAC,OAAOa,GAAG,EAAE;wBACZd,OAAO,CAAC9G,KAAK,CAAC,mDAAmD,EAAE4H,GAAG,CAAC;sBACzE;oBACF;kBACF;gBACF,CAAC,CAAC,OAAOA,GAAG,EAAE;kBACZd,OAAO,CAAC9G,KAAK,CAAC,iDAAiD,EAAE4H,GAAG,CAAC;gBACvE;cACF;YACF;UACF,CAAC,CAAC,OAAOqE,QAAQ,EAAE;YACjBnF,OAAO,CAAC9G,KAAK,CAAC,kCAAkC,EAAEiM,QAAQ,CAAC;YAC3DrN,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,mCAAmC,CAAC,CAAC;UACrD;QACF;QAEAT,KAAK,CAAC8J,OAAO,CAACrJ,CAAC,CAAC,+BAA+B,CAAC,CAAC;;QAEjD;QACA6M,UAAU,CAAC,MAAM;UACf;UACA9B,MAAM,CAAC+B,aAAa,CAAC,IAAIC,WAAW,CAAC,oBAAoB,EAAE;YACzDC,MAAM,EAAE;cAAElB,WAAW,EAAEA,WAAW,IAAI3L;YAAK;UAC7C,CAAC,CAAC,CAAC;;UAEH;UACA,IAAI2L,WAAW,IAAIA,WAAW,CAACZ,EAAE,EAAE;YACjChL,QAAQ,CAAC,gBAAgB4L,WAAW,CAACZ,EAAE,EAAE,CAAC;YAC1C;YACA2B,UAAU,CAAC,MAAM;cACf9B,MAAM,CAACW,QAAQ,CAACuB,MAAM,CAAC,CAAC;YAC1B,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM,IAAIhC,MAAM,EAAE;YACjB/K,QAAQ,CAAC,gBAAgB+K,MAAM,EAAE,CAAC;YAClC;YACA4B,UAAU,CAAC,MAAM;cACf9B,MAAM,CAACW,QAAQ,CAACuB,MAAM,CAAC,CAAC;YAC1B,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACL;YACA,MAAM7E,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;YAC/C,IAAIe,UAAU,EAAE;cACd,IAAI;gBACF,MAAME,QAAQ,GAAGf,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;gBACvC,IAAIE,QAAQ,IAAIA,QAAQ,CAAC4C,EAAE,EAAE;kBAC3BhL,QAAQ,CAAC,gBAAgBoI,QAAQ,CAAC4C,EAAE,EAAE,CAAC;kBACvC;kBACA2B,UAAU,CAAC,MAAM;oBACf9B,MAAM,CAACW,QAAQ,CAACuB,MAAM,CAAC,CAAC;kBAC1B,CAAC,EAAE,GAAG,CAAC;kBACP;gBACF;cACF,CAAC,CAAC,OAAO1E,GAAG,EAAE;gBACZd,OAAO,CAAC9G,KAAK,CAAC,4CAA4C,EAAE4H,GAAG,CAAC;cAClE;YACF;YACA;YACArI,QAAQ,CAAC,GAAG,CAAC;YACb6K,MAAM,CAACW,QAAQ,CAACuB,MAAM,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,OAAOC,SAAS,EAAE;QAAA,IAAAC,mBAAA,EAAAC,qBAAA;QAClB3F,OAAO,CAAC9G,KAAK,CAAC,2BAA2B,EAAEuM,SAAS,CAAC;QACrDtM,QAAQ,CAAC,kBAAkB,EAAAuM,mBAAA,GAAAD,SAAS,CAACvC,QAAQ,cAAAwC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBjI,IAAI,cAAAkI,qBAAA,uBAAxBA,qBAAA,CAA0B1E,OAAO,KAAIwE,SAAS,CAACxE,OAAO,EAAE,CAAC;QACpFnJ,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,4BAA4B,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOuI,GAAG,EAAE;MACZd,OAAO,CAAC9G,KAAK,CAAC,eAAe,EAAE4H,GAAG,CAAC;MACnC3H,QAAQ,CAAC,kBAAkB2H,GAAG,CAACG,OAAO,EAAE,CAAC;MACzCnJ,KAAK,CAACoB,KAAK,CAACX,CAAC,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRU,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACEd,OAAA,CAACtC,SAAS;MAACkQ,EAAE,EAAE;QACbC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,eACAjO,OAAA,CAAChC,gBAAgB;QAAC0L,IAAI,EAAE;MAAG;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEhB;EAEA,IAAI,CAAC3N,IAAI,EAAE;IACT,oBACEV,OAAA,CAACtC,SAAS;MAAC4Q,QAAQ,EAAC,IAAI;MAACV,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eACrCjO,OAAA,CAAClC,KAAK;QAAC0Q,SAAS,EAAE,CAAE;QAACZ,EAAE,EAAE;UAAEa,CAAC,EAAE,CAAC;UAAEC,YAAY,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAV,QAAA,gBACtEjO,OAAA,CAACnC,UAAU;UAAC+Q,OAAO,EAAC,IAAI;UAACC,YAAY;UAACjB,EAAE,EAAE;YAAEkB,UAAU,EAAE;UAAI,CAAE;UAAAb,QAAA,EAC3D1N,CAAC,CAAC,sBAAsB;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACbrO,OAAA,CAAC/B,KAAK;UAAC8Q,QAAQ,EAAC,OAAO;UAACnB,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EACnC/M,KAAK,IAAIX,CAAC,CAAC,sBAAsB;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACRrO,OAAA,CAACzB,KAAK;UAAC0Q,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAACpB,cAAc,EAAC,QAAQ;UAAAG,QAAA,gBACxDjO,OAAA,CAACpC,MAAM;YAACgR,OAAO,EAAC,WAAW;YAACO,OAAO,EAAEA,CAAA,KAAM7D,MAAM,CAACW,QAAQ,CAACuB,MAAM,CAAC,CAAE;YAAAS,QAAA,EACjE1N,CAAC,CAAC,iBAAiB;UAAC;YAAA2N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACTrO,OAAA,CAACpC,MAAM;YAACgR,OAAO,EAAC,UAAU;YAACO,OAAO,EAAEA,CAAA,KAAM1O,QAAQ,CAAC,GAAG,CAAE;YAAAwN,QAAA,EACrD1N,CAAC,CAAC,eAAe;UAAC;YAAA2N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACErO,OAAA,CAACtC,SAAS;IAAC4Q,QAAQ,EAAC,IAAI;IAACV,EAAE,EAAE;MAAEwB,EAAE,EAAE;IAAE,CAAE;IAAAnB,QAAA,gBACrCjO,OAAA,CAAClC,KAAK;MAAC0Q,SAAS,EAAE,CAAE;MAACZ,EAAE,EAAE;QACvBa,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,CAAC;QACfW,UAAU,EAAE;MACd,CAAE;MAAApB,QAAA,gBAEAjO,OAAA,CAAC9B,GAAG;QAAC0P,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBiB,EAAE,EAAE;QACN,CAAE;QAAAf,QAAA,gBACAjO,OAAA,CAACnC,UAAU;UAAC+Q,OAAO,EAAC,IAAI;UAAChB,EAAE,EAAE;YAC3BkB,UAAU,EAAE,GAAG;YACfO,UAAU,EAAE,kDAAkD;YAC9DC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE;UACvB,CAAE;UAAAtB,QAAA,EACC1N,CAAC,CAAC,qBAAqB;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbrO,OAAA,CAACpC,MAAM;UACLuR,OAAO,EAAEA,CAAA,KAAM;YACb;YACA,IAAIzO,IAAI,IAAIA,IAAI,CAAC+K,EAAE,EAAE;cACnBhL,QAAQ,CAAC,gBAAgBC,IAAI,CAAC+K,EAAE,EAAE,CAAC;YACrC,CAAC,MAAM;cACL;cACA,MAAM9C,UAAU,GAAGC,YAAY,CAAChB,OAAO,CAAC,MAAM,CAAC;cAC/C,IAAIe,UAAU,EAAE;gBACd,IAAI;kBACF,MAAME,QAAQ,GAAGf,IAAI,CAACC,KAAK,CAACY,UAAU,CAAC;kBACvC,IAAIE,QAAQ,IAAIA,QAAQ,CAAC4C,EAAE,EAAE;oBAC3BhL,QAAQ,CAAC,gBAAgBoI,QAAQ,CAAC4C,EAAE,EAAE,CAAC;oBACvC;kBACF;gBACF,CAAC,CAAC,OAAO3C,GAAG,EAAE;kBACZd,OAAO,CAAC9G,KAAK,CAAC,4CAA4C,EAAE4H,GAAG,CAAC;gBAClE;cACF;cACA;cACArI,QAAQ,CAAC,GAAG,CAAC;YACf;UACF,CAAE;UACF+O,SAAS,eAAExP,OAAA,CAAChB,SAAS;YAAAkP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBO,OAAO,EAAC,UAAU;UAClBhB,EAAE,EAAE;YAAEc,YAAY,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAxB,QAAA,EAE/B1N,CAAC,CAAC,uBAAuB;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrO,OAAA,CAAC9B,GAAG;QAAC0P,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACf6B,aAAa,EAAE,QAAQ;UACvB3B,UAAU,EAAE,QAAQ;UACpBiB,EAAE,EAAE;QACN,CAAE;QAAAf,QAAA,gBACAjO,OAAA,CAAC9B,GAAG;UAAC0P,EAAE,EAAE;YAAE+B,QAAQ,EAAE;UAAW,CAAE;UAAA1B,QAAA,gBAChCjO,OAAA,CAAC7B,MAAM;YACL6I,GAAG,EAAE1F,OAAO,KAAKZ,IAAI,CAAC8L,UAAU,GAC7B9L,IAAI,CAAC8L,UAAU,CAAC/C,UAAU,CAAC,gBAAgB,CAAC,GAC3C,gCAAgC/I,IAAI,CAAC8L,UAAU,EAAE,GAChD9L,IAAI,CAAC8L,UAAU,CAAC/C,UAAU,CAAC,MAAM,CAAC,GACjC/I,IAAI,CAAC8L,UAAU,GACf,8CAA8C9L,IAAI,CAAC8L,UAAU,CAACoD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,EAC/E,GAEH,IAAI,CACJ;YACFjC,EAAE,EAAE;cACFxI,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE,GAAG;cACXyK,QAAQ,EAAE,EAAE;cACZC,MAAM,EAAE,mBAAmB;cAC3BC,SAAS,EAAE;YACb,CAAE;YAAA/B,QAAA,EAED,EAAA5N,UAAA,GAAAK,IAAI,CAAC0I,IAAI,cAAA/I,UAAA,uBAATA,UAAA,CAAW4P,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,OAAA5P,WAAA,GAAII,IAAI,CAACF,KAAK,cAAAF,WAAA,uBAAVA,WAAA,CAAY2P,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;UAAG;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACTrO,OAAA,CAAC5B,UAAU;YACT+R,KAAK,EAAC,SAAS;YACfC,SAAS,EAAC,OAAO;YACjBxC,EAAE,EAAE;cACF+B,QAAQ,EAAE,UAAU;cACpBU,MAAM,EAAE,CAAC;cACTC,KAAK,EAAE,CAAC;cACRC,OAAO,EAAE,kBAAkB;cAC3B,SAAS,EAAE;gBAAEA,OAAO,EAAE;cAAe;YACvC,CAAE;YAAAtC,QAAA,gBAEFjO,OAAA,CAACf,WAAW;cAAAiP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfrO,OAAA;cAAOwJ,IAAI,EAAC,MAAM;cAACgH,MAAM;cAACC,QAAQ,EAAEnH,gBAAiB;cAACoH,MAAM,EAAC;YAAS;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNrO,OAAA,CAACnC,UAAU;UAAC+Q,OAAO,EAAC,SAAS;UAACuB,KAAK,EAAC,gBAAgB;UAACvC,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAChE1N,CAAC,CAAC,6BAA6B;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,EAGZjM,gBAAgB,iBACfpC,OAAA,CAAC9B,GAAG;UAAC0P,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEQ,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACxDjO,OAAA,CAAChC,gBAAgB;YAAC0L,IAAI,EAAE,EAAG;YAACkE,EAAE,EAAE;cAAE+C,EAAE,EAAE;YAAE;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CrO,OAAA,CAACnC,UAAU;YAAC+Q,OAAO,EAAC,OAAO;YAACuB,KAAK,EAAC,SAAS;YAAAlC,QAAA,EACxC1N,CAAC,CAAC,+BAA+B;UAAC;YAAA2N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN,EAGAnM,YAAY,IAAI,CAACE,gBAAgB,iBAChCpC,OAAA,CAAC9B,GAAG;UAAC0P,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEI,SAAS,EAAE;UAAS,CAAE;UAAAV,QAAA,eACtCjO,OAAA,CAAC/B,KAAK;YACJ8Q,QAAQ,EAAE7M,YAAY,CAAC4E,QAAQ,GAAG,SAAS,GAAG,SAAU;YACxD8G,EAAE,EAAE;cACFC,OAAO,EAAE,aAAa;cACtBE,UAAU,EAAE,QAAQ;cACpBO,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,EAED/L,YAAY,CAAC4E,QAAQ,gBACpB9G,OAAA,CAAC9B,GAAG;cAAC0P,EAAE,EAAE;gBAAEe,SAAS,EAAE;cAAS,CAAE;cAAAV,QAAA,gBAC/BjO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEkB,UAAU,EAAE;gBAAI,CAAE;gBAAAb,QAAA,EACjD1N,CAAC,CAAC,6BAA6B;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACbrO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,SAAS;gBAAChB,EAAE,EAAE;kBAAEC,OAAO,EAAE,OAAO;kBAAEU,EAAE,EAAE;gBAAI,CAAE;gBAAAN,QAAA,GAC7D1N,CAAC,CAAC,iBAAiB,CAAC,EAAC,IAAE,EAAC2B,YAAY,CAAC0E,KAAK,EAAC,IAAE,EAACrG,CAAC,CAAC,eAAe,CAAC,EAAC,IAAE,EAACgG,IAAI,CAACqK,KAAK,CAAC1O,YAAY,CAAC2E,KAAK,CAAC,EAAC,GACtG;cAAA;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,SAAS;gBAAChB,EAAE,EAAE;kBAAEC,OAAO,EAAE,OAAO;kBAAEU,EAAE,EAAE,GAAG;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAf,QAAA,EACpE1N,CAAC,CAAC,+BAA+B;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACbrO,OAAA,CAACpC,MAAM;gBACL8L,IAAI,EAAC,OAAO;gBACZkF,OAAO,EAAC,UAAU;gBAClBuB,KAAK,EAAC,SAAS;gBACfhB,OAAO,EAAE7E,yBAA0B;gBACnCsD,EAAE,EAAE;kBAAEW,EAAE,EAAE,CAAC;kBAAEuB,QAAQ,EAAE;gBAAU,CAAE;gBAAA7B,QAAA,EAElC1N,CAAC,CAAC,mBAAmB;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENrO,OAAA,CAAAE,SAAA;cAAA+N,QAAA,gBACEjO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEkB,UAAU,EAAE;gBAAI,CAAE;gBAAAb,QAAA,EACjD1N,CAAC,CAAC,0BAA0B;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACbrO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,SAAS;gBAAChB,EAAE,EAAE;kBAAEC,OAAO,EAAE,OAAO;kBAAEU,EAAE,EAAE;gBAAI,CAAE;gBAAAN,QAAA,GAC7D1N,CAAC,CAAC,iBAAiB,CAAC,EAAC,IAAE,EAAC2B,YAAY,CAAC0E,KAAK,EAAC,IAAE,EAACrG,CAAC,CAAC,eAAe,CAAC,EAAC,IAAE,EAACgG,IAAI,CAACqK,KAAK,CAAC1O,YAAY,CAAC2E,KAAK,CAAC,EAAC,GACtG;cAAA;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,eACb;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELnN,KAAK,iBACJlB,OAAA,CAAC/B,KAAK;QAAC8Q,QAAQ,EAAC,OAAO;QAACnB,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,EACnC/M;MAAK;QAAAgN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDrO,OAAA;QAAM6Q,QAAQ,EAAE1F,YAAa;QAAA8C,QAAA,eAC3BjO,OAAA,CAACjC,IAAI;UAAC+S,SAAS;UAAC5B,OAAO,EAAE,CAAE;UAAAjB,QAAA,gBAEzBjO,OAAA,CAACjC,IAAI;YAACgT,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBjO,OAAA,CAACnC,UAAU;cAAC+Q,OAAO,EAAC,IAAI;cAAChB,EAAE,EAAE;gBAAEoB,EAAE,EAAE,CAAC;gBAAEF,UAAU,EAAE;cAAI,CAAE;cAAAb,QAAA,gBACtDjO,OAAA,CAACV,MAAM;gBAAC6Q,KAAK,EAAC,SAAS;gBAACvC,EAAE,EAAE;kBAAEsD,aAAa,EAAE,QAAQ;kBAAEP,EAAE,EAAE;gBAAE;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACjE9N,CAAC,CAAC,sBAAsB,CAAC;YAAA;cAAA2N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eAEbrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,kBAAkB,CAAE;cAC7B6I,IAAI,EAAC,MAAM;cACXgI,SAAS;cACT/H,KAAK,EAAEzI,IAAI,CAACwI,IAAI,IAAI,EAAG;cACvBqH,QAAQ,EAAEvH,YAAa;cACvBmI,MAAM,EAAC,QAAQ;cACfC,SAAS,EAAE;gBACTC,KAAK,EAAE;kBACLC,cAAc,eACZxR,OAAA,CAACxB,cAAc;oBAACmR,QAAQ,EAAC,OAAO;oBAAA1B,QAAA,eAC9BjO,OAAA,CAACV,MAAM;sBAAC6Q,KAAK,EAAC;oBAAQ;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX;gBAEpB;cACF;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,eAAe,CAAE;cAC1B6I,IAAI,EAAC,OAAO;cACZgI,SAAS;cACT/H,KAAK,EAAEzI,IAAI,CAACJ,KAAK,IAAI,EAAG;cACxBiR,QAAQ;cACRJ,MAAM,EAAC,QAAQ;cACfC,SAAS,EAAE;gBACTC,KAAK,EAAE;kBACLC,cAAc,eACZxR,OAAA,CAACxB,cAAc;oBAACmR,QAAQ,EAAC,OAAO;oBAAA1B,QAAA,eAC9BjO,OAAA,CAACd,KAAK;sBAACiR,KAAK,EAAC;oBAAQ;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAEpB;cACF;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,eAAe,CAAE;cAC1B6I,IAAI,EAAC,OAAO;cACZgI,SAAS;cACT/H,KAAK,EAAEzI,IAAI,CAACoL,KAAK,IAAI,EAAG;cACxByE,QAAQ,EAAEvH,YAAa;cACvBmI,MAAM,EAAC,QAAQ;cACfC,SAAS,EAAE;gBACTC,KAAK,EAAE;kBACLC,cAAc,eACZxR,OAAA,CAACxB,cAAc;oBAACmR,QAAQ,EAAC,OAAO;oBAAA1B,QAAA,eAC9BjO,OAAA,CAACb,KAAK;sBAACgR,KAAK,EAAC;oBAAQ;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAEpB;cACF;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,kBAAkB,CAAE;cAC7B6I,IAAI,EAAC,UAAU;cACfgI,SAAS;cACT/H,KAAK,EAAEzI,IAAI,CAACqL,QAAQ,IAAI,EAAG;cAC3BwE,QAAQ,EAAEvH,YAAa;cACvBmI,MAAM,EAAC,QAAQ;cACfC,SAAS,EAAE;gBACTC,KAAK,EAAE;kBACLC,cAAc,eACZxR,OAAA,CAACxB,cAAc;oBAACmR,QAAQ,EAAC,OAAO;oBAAA1B,QAAA,eAC9BjO,OAAA,CAACZ,UAAU;sBAAC+Q,KAAK,EAAC;oBAAQ;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAEpB;cACF;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFrO,OAAA,CAAC9B,GAAG;cAAC0P,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,eACjBjO,OAAA,CAACpC,MAAM;gBACLgR,OAAO,EAAC,UAAU;gBAClBuB,KAAK,EAAC,SAAS;gBACfX,SAAS,eAAExP,OAAA,CAACP,IAAI;kBAAAyO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpBc,OAAO,EAAEzE,wBAAyB;gBAClC0G,SAAS;gBACTxD,EAAE,EAAE;kBACFc,YAAY,EAAE,CAAC;kBACfU,EAAE,EAAE,CAAC;kBACLsC,aAAa,EAAE,MAAM;kBACrB5C,UAAU,EAAE;gBACd,CAAE;gBAAAb,QAAA,EAED1N,CAAC,CAAC,wBAAwB;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPrO,OAAA,CAACjC,IAAI;YAACgT,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBjO,OAAA,CAACnC,UAAU;cAAC+Q,OAAO,EAAC,IAAI;cAAChB,EAAE,EAAE;gBAAEoB,EAAE,EAAE,CAAC;gBAAEF,UAAU,EAAE;cAAI,CAAE;cAAAb,QAAA,gBACtDjO,OAAA,CAACX,IAAI;gBAAC8Q,KAAK,EAAC,SAAS;gBAACvC,EAAE,EAAE;kBAAEsD,aAAa,EAAE,QAAQ;kBAAEP,EAAE,EAAE;gBAAE;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC/D9N,CAAC,CAAC,6BAA6B,CAAC;YAAA;cAAA2N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAEbrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,cAAc,CAAE;cACzB6I,IAAI,EAAC,MAAM;cACXgI,SAAS;cACT/H,KAAK,EAAE5F,aAAa,CAAC7C,IAAI,CAAC8C,IAAI,IAAI,UAAU,CAAE;cAC9C+N,QAAQ;cACRJ,MAAM,EAAC,QAAQ;cACfM,UAAU,EAAEpR,CAAC,CAAC,0BAA0B,CAAE;cAC1CqR,UAAU,EAAE;gBACVhE,EAAE,EAAE;kBACF8D,aAAa,EAAE;gBACjB;cACF;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFrO,OAAA,CAACrC,SAAS;cACRwT,KAAK,EAAE5Q,CAAC,CAAC,iBAAiB,CAAE;cAC5B6I,IAAI,EAAC,OAAO;cACZgI,SAAS;cACTS,SAAS;cACTC,IAAI,EAAE,CAAE;cACRzI,KAAK,EAAEzI,IAAI,CAACsL,KAAK,IAAI,EAAG;cACxBuE,QAAQ,EAAEvH,YAAa;cACvBmI,MAAM,EAAC,QAAQ;cACfzD,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFrO,OAAA,CAAC9B,GAAG;cAAC0P,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACjBjO,OAAA,CAACnC,UAAU;gBAAC+Q,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAZ,QAAA,EACzC1N,CAAC,CAAC,yBAAyB;cAAC;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACbrO,OAAA,CAAC9B,GAAG;gBAAC0P,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEkE,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE,CAAC;kBAAEhD,EAAE,EAAE;gBAAE,CAAE;gBAAAf,QAAA,EAC3DvM,MAAM,CAACM,GAAG,CAAC,CAAC+H,KAAK,EAAEhE,GAAG,kBACrB/F,OAAA,CAAC3B,IAAI;kBAEH8S,KAAK,EAAEpH,KAAM;kBACbkI,QAAQ,EAAEA,CAAA,KAAM7H,iBAAiB,CAACL,KAAK,CAAE;kBACzCoG,KAAK,EAAC,SAAS;kBACfvB,OAAO,EAAC,UAAU;kBAClBsD,UAAU,eAAElS,OAAA,CAACT,KAAK;oBAACuQ,QAAQ,EAAC;kBAAO;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvCT,EAAE,EAAE;oBAAEc,YAAY,EAAE;kBAAE;gBAAE,GANnB3I,GAAG;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOT,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrO,OAAA,CAAC9B,GAAG;gBAAC0P,EAAE,EAAE;kBAAE+B,QAAQ,EAAE;gBAAW,CAAE;gBAACwC,SAAS,EAAC,2BAA2B;gBAAAlE,QAAA,gBACtEjO,OAAA,CAAC9B,GAAG;kBAAC0P,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEmE,GAAG,EAAE;kBAAE,CAAE;kBAAA/D,QAAA,gBACnCjO,OAAA,CAACrC,SAAS;oBACRwT,KAAK,EAAE5Q,CAAC,CAAC,kBAAkB,CAAE;oBAC7B8I,KAAK,EAAE7H,QAAS;oBAChBiP,QAAQ,EAAGtH,CAAC,IAAK;sBACf1H,WAAW,CAAC0H,CAAC,CAAC9B,MAAM,CAACgC,KAAK,CAAC;sBAC3BxH,qBAAqB,CAACsH,CAAC,CAAC9B,MAAM,CAACgC,KAAK,CAACpF,MAAM,GAAG,CAAC,CAAC;oBAClD,CAAE;oBACFmO,OAAO,EAAEA,CAAA,KAAMvQ,qBAAqB,CAACL,QAAQ,CAACyC,MAAM,GAAG,CAAC,CAAE;oBAC1DyF,IAAI,EAAC,OAAO;oBACZ0H,SAAS;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACFrO,OAAA,CAACpC,MAAM;oBACLuR,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAAC,CAAE;oBAChC+E,OAAO,EAAC,WAAW;oBACnBY,SAAS,eAAExP,OAAA,CAACR,GAAG;sBAAA0O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACnBT,EAAE,EAAE;sBAAEyE,UAAU,EAAE;oBAAS,CAAE;oBAAApE,QAAA,EAE5B1N,CAAC,CAAC,YAAY;kBAAC;oBAAA2N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLzM,kBAAkB,IAAIsI,cAAc,CAACjG,MAAM,GAAG,CAAC,iBAC9CjE,OAAA,CAAClC,KAAK;kBACJ8P,EAAE,EAAE;oBACF+B,QAAQ,EAAE,UAAU;oBACpB2C,GAAG,EAAE,MAAM;oBACXC,IAAI,EAAE,CAAC;oBACPjC,KAAK,EAAE,CAAC;oBACRkC,MAAM,EAAE,IAAI;oBACZC,SAAS,EAAE,GAAG;oBACdC,SAAS,EAAE,MAAM;oBACjBnE,EAAE,EAAE;kBACN,CAAE;kBAAAN,QAAA,EAED/D,cAAc,CAACyI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC3Q,GAAG,CAAE+H,KAAK,iBACrC/J,OAAA,CAAC9B,GAAG;oBAEFiR,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACE,KAAK,CAAE;oBACrC6D,EAAE,EAAE;sBACFa,CAAC,EAAE,CAAC;sBACJmE,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACTrC,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAAtC,QAAA,EAEDlE;kBAAK,GAVDA,KAAK;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWP,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPrO,OAAA,CAACjC,IAAI;YAACgT,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA/C,QAAA,gBAChBjO,OAAA,CAAC1B,OAAO;cAACsP,EAAE,EAAE;gBAAEiF,EAAE,EAAE;cAAE;YAAE;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1BrO,OAAA,CAAC9B,GAAG;cAAC0P,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAAG,QAAA,eACvDjO,OAAA,CAACpC,MAAM;gBACL4L,IAAI,EAAC,QAAQ;gBACboF,OAAO,EAAC,WAAW;gBACnBlF,IAAI,EAAC,OAAO;gBACZ+H,QAAQ,EAAEzQ,UAAW;gBACrB4M,EAAE,EAAE;kBACF6B,EAAE,EAAE,CAAC;kBACLL,EAAE,EAAE,GAAG;kBACPV,YAAY,EAAE,EAAE;kBAChBoB,QAAQ,EAAE,MAAM;kBAChB,SAAS,EAAE;oBACTgD,SAAS,EAAE,kBAAkB;oBAC7B9C,SAAS,EAAE;kBACb;gBACF,CAAE;gBAAA/B,QAAA,EAEDjN,UAAU,gBACThB,OAAA,CAAAE,SAAA;kBAAA+N,QAAA,gBACEjO,OAAA,CAAChC,gBAAgB;oBAAC0L,IAAI,EAAE,EAAG;oBAACkE,EAAE,EAAE;sBAAE+C,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC5C9N,CAAC,CAAC,eAAe,CAAC;gBAAA,eACnB,CAAC,GAEHA,CAAC,CAAC,oBAAoB;cACvB;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRrO,OAAA,CAACvB,MAAM;MACLsU,IAAI,EAAEzQ,kBAAmB;MACzB0Q,OAAO,EAAErI,yBAA0B;MACnC2D,QAAQ,EAAC,IAAI;MACb8C,SAAS;MAAAnD,QAAA,gBAETjO,OAAA,CAACtB,WAAW;QAACkP,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAb,QAAA,EAClC1N,CAAC,CAAC,wBAAwB;MAAC;QAAA2N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEdrO,OAAA,CAACrB,aAAa;QAAAsP,QAAA,GACXpL,aAAa,iBACZ7C,OAAA,CAAC/B,KAAK;UAAC8Q,QAAQ,EAAC,OAAO;UAACnB,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EACnCpL;QAAa;UAAAqL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACR,eAEDrO,OAAA,CAACnB,iBAAiB;UAAC+O,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,EAC9B1N,CAAC,CAAC,mCAAmC;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEpBrO,OAAA,CAACrC,SAAS;UACRwT,KAAK,EAAE5Q,CAAC,CAAC,yBAAyB,CAAE;UACpC6I,IAAI,EAAC,iBAAiB;UACtBI,IAAI,EAAEzG,mBAAmB,GAAG,MAAM,GAAG,UAAW;UAChDsG,KAAK,EAAE7G,YAAY,CAACE,eAAgB;UACpC+N,QAAQ,EAAE7F,oBAAqB;UAC/BwG,SAAS;UACTC,MAAM,EAAC,QAAQ;UACfO,UAAU,EAAE;YACVqB,YAAY,eACVjT,OAAA,CAACxB,cAAc;cAACmR,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BjO,OAAA,CAAC5B,UAAU;gBACT+Q,OAAO,EAAEA,CAAA,KAAMnM,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAC5DmQ,IAAI,EAAC,KAAK;gBAAAjF,QAAA,EAETlL,mBAAmB,gBAAG/C,OAAA,CAACL,aAAa;kBAAAuO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGrO,OAAA,CAACN,UAAU;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFrO,OAAA,CAACrC,SAAS;UACRwT,KAAK,EAAE5Q,CAAC,CAAC,qBAAqB,CAAE;UAChC6I,IAAI,EAAC,aAAa;UAClBI,IAAI,EAAEvG,eAAe,GAAG,MAAM,GAAG,UAAW;UAC5CoG,KAAK,EAAE7G,YAAY,CAACG,WAAY;UAChC8N,QAAQ,EAAE7F,oBAAqB;UAC/BwG,SAAS;UACTC,MAAM,EAAC,QAAQ;UACfO,UAAU,EAAE;YACVqB,YAAY,eACVjT,OAAA,CAACxB,cAAc;cAACmR,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BjO,OAAA,CAAC5B,UAAU;gBACT+Q,OAAO,EAAEA,CAAA,KAAMjM,kBAAkB,CAAC,CAACD,eAAe,CAAE;gBACpDiQ,IAAI,EAAC,KAAK;gBAAAjF,QAAA,EAEThL,eAAe,gBAAGjD,OAAA,CAACL,aAAa;kBAAAuO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGrO,OAAA,CAACN,UAAU;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGD7L,YAAY,CAACG,WAAW,iBACvB3C,OAAA,CAAC9B,GAAG;UAAC0P,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACxBjO,OAAA,CAACnC,UAAU;YAAC+Q,OAAO,EAAC,OAAO;YAACC,YAAY;YAAAZ,QAAA,GACrC1N,CAAC,CAAC,0BAA0B,CAAC,EAAC,GAC/B,eAAAP,OAAA,CAAC9B,GAAG;cAACkS,SAAS,EAAC,MAAM;cACnBxC,EAAE,EAAE;gBACFuF,EAAE,EAAE,CAAC;gBACLrE,UAAU,EAAE,MAAM;gBAClBqB,KAAK,EAAE5M,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GAC3D,cAAc,GACdgD,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GACtD,cAAc,GACd;cACR,CAAE;cAAA0N,QAAA,EAED1K,gBAAgB,CAAC2M,WAAW,CAAC;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbrO,OAAA,CAAC9B,GAAG;YAAC0P,EAAE,EAAE;cAAExI,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,CAAC;cAAEkL,OAAO,EAAE,UAAU;cAAE7B,YAAY,EAAE,CAAC;cAAE0E,QAAQ,EAAE;YAAS,CAAE;YAAAnF,QAAA,eAC9FjO,OAAA,CAAC9B,GAAG;cACF0P,EAAE,EAAE;gBACFvI,MAAM,EAAE,MAAM;gBACdD,KAAK,EAAE7B,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GAC3D,MAAM,GACNgD,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GACtD,KAAK,GACL,KAAK;gBACXgQ,OAAO,EAAEhN,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GAC7D,cAAc,GACdgD,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,GACtD,cAAc,GACd,YAAY;gBAClB8S,UAAU,EAAE;cACd;YAAE;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGL9K,gBAAgB,KAAKhD,CAAC,CAAC,gCAAgC,CAAC,iBACvDP,OAAA,CAAC9B,GAAG;YAAC0P,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEE,CAAC,EAAE,CAAC;cAAE8B,OAAO,EAAE,SAAS;cAAE7B,YAAY,EAAE;YAAE,CAAE;YAAAT,QAAA,gBAC5DjO,OAAA,CAACnC,UAAU;cAAC+Q,OAAO,EAAC,WAAW;cAACC,YAAY;cAACsB,KAAK,EAAC,SAAS;cAAAlC,QAAA,GACzD1N,CAAC,CAAC,4BAA4B,CAAC,EAAC,GACnC;YAAA;cAAA2N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrO,OAAA,CAACjC,IAAI;cAAC+S,SAAS;cAAC5B,OAAO,EAAE,CAAE;cAAAjB,QAAA,gBACzBjO,OAAA,CAACjC,IAAI;gBAACgT,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACsC,EAAE,EAAE,CAAE;gBAAArF,QAAA,eACvBjO,OAAA,CAACnC,UAAU;kBAAC+Q,OAAO,EAAC,OAAO;kBACzBuB,KAAK,EAAE3N,YAAY,CAACG,WAAW,CAACsB,MAAM,IAAI,CAAC,GAAG,cAAc,GAAG,gBAAiB;kBAChF2J,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAE,QAAA,GAE7CzL,YAAY,CAACG,WAAW,CAACsB,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAAC1D,CAAC,CAAC,uBAAuB,CAAC;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrO,OAAA,CAACjC,IAAI;gBAACgT,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACsC,EAAE,EAAE,CAAE;gBAAArF,QAAA,eACvBjO,OAAA,CAACnC,UAAU;kBAAC+Q,OAAO,EAAC,OAAO;kBACzBuB,KAAK,EAAE,OAAO,CAAChM,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,cAAc,GAAG,gBAAiB;kBAClFiL,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAE,QAAA,GAE7C,OAAO,CAAC9J,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACpC,CAAC,CAAC,sBAAsB,CAAC;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrO,OAAA,CAACjC,IAAI;gBAACgT,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACsC,EAAE,EAAE,CAAE;gBAAArF,QAAA,eACvBjO,OAAA,CAACnC,UAAU;kBAAC+Q,OAAO,EAAC,OAAO;kBACzBuB,KAAK,EAAE,OAAO,CAAChM,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,cAAc,GAAG,gBAAiB;kBAClFiL,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAE,QAAA,GAE7C,OAAO,CAAC9J,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACpC,CAAC,CAAC,sBAAsB,CAAC;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrO,OAAA,CAACjC,IAAI;gBAACgT,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACsC,EAAE,EAAE,CAAE;gBAAArF,QAAA,eACvBjO,OAAA,CAACnC,UAAU;kBAAC+Q,OAAO,EAAC,OAAO;kBACzBuB,KAAK,EAAE,IAAI,CAAChM,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,cAAc,GAAG,gBAAiB;kBAC/EiL,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAE,QAAA,GAE7C,IAAI,CAAC9J,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACpC,CAAC,CAAC,kBAAkB,CAAC;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrO,OAAA,CAACjC,IAAI;gBAACgT,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAA/C,QAAA,eAChBjO,OAAA,CAACnC,UAAU;kBAAC+Q,OAAO,EAAC,OAAO;kBACzBuB,KAAK,EAAE,wBAAwB,CAAChM,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,cAAc,GAAG,gBAAiB;kBACnGiL,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAE,QAAA,GAE7C,wBAAwB,CAAC9J,IAAI,CAAC3B,YAAY,CAACG,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACpC,CAAC,CAAC,wBAAwB,CAAC;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDrO,OAAA,CAACrC,SAAS;UACRwT,KAAK,EAAE5Q,CAAC,CAAC,4BAA4B,CAAE;UACvC6I,IAAI,EAAC,iBAAiB;UACtBI,IAAI,EAAErG,mBAAmB,GAAG,MAAM,GAAG,UAAW;UAChDkG,KAAK,EAAE7G,YAAY,CAACI,eAAgB;UACpC6N,QAAQ,EAAE7F,oBAAqB;UAC/BwG,SAAS;UACTC,MAAM,EAAC,QAAQ;UACfO,UAAU,EAAE;YACVqB,YAAY,eACVjT,OAAA,CAACxB,cAAc;cAACmR,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BjO,OAAA,CAAC5B,UAAU;gBACT+Q,OAAO,EAAEA,CAAA,KAAM/L,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAC5D+P,IAAI,EAAC,KAAK;gBAAAjF,QAAA,EAET9K,mBAAmB,gBAAGnD,OAAA,CAACL,aAAa;kBAAAuO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGrO,OAAA,CAACN,UAAU;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBrO,OAAA,CAACpB,aAAa;QAACgP,EAAE,EAAE;UAAE6B,EAAE,EAAE,CAAC;UAAE8D,EAAE,EAAE;QAAE,CAAE;QAAAtF,QAAA,gBAClCjO,OAAA,CAACpC,MAAM;UACLuR,OAAO,EAAExE,yBAA0B;UACnCiE,OAAO,EAAC,UAAU;UAClB6C,QAAQ,EAAEpO,gBAAiB;UAAA4K,QAAA,EAE1B1N,CAAC,CAAC,eAAe;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACTrO,OAAA,CAACpC,MAAM;UACLuR,OAAO,EAAEtE,oBAAqB;UAC9B+D,OAAO,EAAC,WAAW;UACnB6C,QAAQ,EAAEpO,gBAAiB;UAC3BmM,SAAS,EAAEnM,gBAAgB,gBAAGrD,OAAA,CAAChC,gBAAgB;YAAC0L,IAAI,EAAE;UAAG;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrO,OAAA,CAACP,IAAI;YAAAyO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAEvE5K,gBAAgB,GAAG9C,CAAC,CAAC,iBAAiB,CAAC,GAAGA,CAAC,CAAC,wBAAwB;QAAC;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACjO,EAAA,CA14CID,eAAe;EAAA,QACLP,cAAc,EACVd,SAAS,EACVC,WAAW;AAAA;AAAAyU,EAAA,GAHxBrT,eAAe;AA44CrB,eAAeA,eAAe;AAAC,IAAAqT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}