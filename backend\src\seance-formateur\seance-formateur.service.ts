import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { FileType } from '@prisma/client';
import { Session2Service } from '../session2/session2.service';

interface CreateSeanceDto {
  title: string;
  startTime: string | Date;
  session2Id: number;
}

interface AddMediaDto {
  seanceId: number;
  type: FileType;
  fileUrl: string;
}

@Injectable()
export class SeanceFormateurService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly session2Service: Session2Service,
  ) {}

  async create(data: CreateSeanceDto, formateurId: number): Promise<{ message: string; seanceId: number }> {
    const { title, startTime, session2Id } = data;
    if (!title || !startTime || !session2Id || !formateurId) {
      throw new BadRequestException('Tous les champs sont obligatoires');
    }
    try {
      const seance = await this.prisma.seanceFormateur.create({
        data: {
          title,
          startTime: new Date(startTime),
          session2Id: Number(session2Id),
          formateurId,
        },
      });
      return { message: 'Séance créée ✅', seanceId: seance.id };
    } catch (error) {
      if (error.code === 'P2002') {
        throw new BadRequestException('Une séance avec ces paramètres existe déjà');
      }
      if (error.code === 'P2003') {
        throw new BadRequestException('Référence invalide - vérifiez que l\'utilisateur et le programme existent');
      }
      throw new BadRequestException(error.message || 'Erreur lors de la création de la séance');
    }
  }

  async getAverageRatingForSeance(seanceId: number): Promise<number | null> {
    if (!seanceId) throw new BadRequestException('seanceId requis');

    const feedbacks = await this.prisma.seanceFeedback.findMany({
      where: { seanceId },
      select: {
        sessionRating: true,
        contentQuality: true,
        sessionOrganization: true,
        objectivesAchieved: true,
        trainerRating: true,
        trainerClarity: true,
        trainerAvailability: true,
        trainerPedagogy: true,
        trainerInteraction: true,
        teamRating: true,
        teamCollaboration: true,
        teamParticipation: true,
        teamCommunication: true,
      },
    });

    if (feedbacks.length === 0) return null;

    const perFeedbackAverages: number[] = [];

    feedbacks.forEach(fb => {
      const ratings = [
        fb.sessionRating,
        fb.contentQuality,
        fb.sessionOrganization,
        fb.objectivesAchieved,
        fb.trainerRating,
        fb.trainerClarity,
        fb.trainerAvailability,
        fb.trainerPedagogy,
        fb.trainerInteraction,
        fb.teamRating,
        fb.teamCollaboration,
        fb.teamParticipation,
        fb.teamCommunication,
      ].filter(r => typeof r === 'number' && r >= 1 && r <= 5);

      if (ratings.length > 0) {
        const avg = ratings.reduce((a, b) => a + b, 0) / ratings.length;
        perFeedbackAverages.push(avg);
      }
    });

    if (perFeedbackAverages.length === 0) return null;

    const averageRating = perFeedbackAverages.reduce((a, b) => a + b, 0) / perFeedbackAverages.length;
    return parseFloat(averageRating.toFixed(1));
  }

  async findAll(): Promise<any[]> {
    return this.prisma.seanceFormateur.findMany({
      include: {
        formateur: true,
        session2: {
          include: { program: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByFormateur(formateurId: number): Promise<any[]> {
    if (!formateurId) throw new BadRequestException('formateurId requis');

    const seances = await this.prisma.seanceFormateur.findMany({
      where: { formateurId },
      include: {
        session2: {
          include: { program: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const resultWithRatings = await Promise.all(
      seances.map(async (seance) => {
        const averageRating = await this.getAverageRatingForSeance(seance.id);
        return {
          ...seance,
          averageRating,
        };
      }),
    );

    return resultWithRatings;
  }

  async findOne(id: number): Promise<any> {
    if (!id) throw new BadRequestException('id requis');

    const seance = await this.prisma.seanceFormateur.findUnique({
      where: { id },
      include: {
        formateur: true,
        session2: {
          include: { program: true },
        },
      },
    });

    if (!seance) throw new NotFoundException('Séance non trouvée');

    const averageRating = await this.getAverageRatingForSeance(seance.id);

    return {
      ...seance,
      averageRating,
    };
  }

  async remove(id: number): Promise<{ message: string }> {
    if (!id) throw new BadRequestException('id requis');
    await this.prisma.seanceFormateur.delete({ where: { id } });
    return { message: 'Séance supprimée 🗑️' };
  }

  async getSession2Details(session2Id: number): Promise<any> {
    if (!session2Id) throw new BadRequestException('session2Id requis');

    const session2 = await this.prisma.session2.findUnique({
      where: { id: session2Id },
      include: {
        program: true,
        session2Modules: {
          include: {
            module: true,
            courses: {
              include: {
                course: true,
                contenus: { include: { contenu: true } },
              },
            },
          },
        },
      },
    });

    if (!session2) throw new NotFoundException('Session2 non trouvée');

    const seances = await this.prisma.seanceFormateur.findMany({
      where: { session2Id },
      select: { id: true },
    });

    if (seances.length === 0) return { ...session2, averageRating: null };

    let sumOfSeanceAverages = 0;
    let numberOfValidSeances = 0;

    for (const seance of seances) {
      const seanceAverage = await this.getAverageRatingForSeance(seance.id);
      if (seanceAverage !== null) {
        sumOfSeanceAverages += seanceAverage;
        numberOfValidSeances += 1;
      }
    }

    if (numberOfValidSeances === 0) {
      return { ...session2, averageRating: null };
    }

    const averageRating = sumOfSeanceAverages / numberOfValidSeances;

    return {
      ...session2,
      averageRating: parseFloat(averageRating.toFixed(1)),
    };
  }

  async addMediaToSeance(data: AddMediaDto): Promise<any> {
    const { seanceId, type, fileUrl } = data;
    if (!seanceId || !type || !fileUrl) {
      throw new BadRequestException('Tous les champs sont obligatoires pour le média');
    }
    return this.prisma.seanceMedia.create({
      data: { seanceId, type, fileUrl },
    });
  }

  async removeMedia(id: number): Promise<any> {
    if (!id) throw new BadRequestException('id requis');
    return this.prisma.seanceMedia.delete({ where: { id } });
  }

  async getMediaForSeance(seanceId: number): Promise<any[]> {
    if (!seanceId) throw new BadRequestException('seanceId requis');
    return this.prisma.seanceMedia.findMany({ where: { seanceId } });
  }

  async findBySession2(session2Id: number): Promise<any[]> {
    if (!session2Id) throw new BadRequestException('session2Id requis');

    const seances = await this.prisma.seanceFormateur.findMany({
      where: { session2Id },
      include: {
        formateur: true,
        session2: {
          include: { program: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const resultWithRatings = await Promise.all(
      seances.map(async (seance) => {
        const averageRating = await this.getAverageRatingForSeance(seance.id);
        return {
          ...seance,
          averageRating,
        };
      }),
    );

    return resultWithRatings;
  }
}
