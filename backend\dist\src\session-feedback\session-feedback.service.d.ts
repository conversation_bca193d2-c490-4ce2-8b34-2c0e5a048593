import { PrismaService } from 'nestjs-prisma';
export declare class SessionFeedbackService {
    private prisma;
    constructor(prisma: PrismaService);
    create(dto: any): Promise<{
        message: string;
        averageRating: number;
    }>;
    private generateFeedbackMessage;
    cleanupOldFeedbacks(sessionId: number): Promise<void>;
    getSessionFeedbacks(sessionId: number): Promise<{
        studentName: string;
        studentEmail: string;
        coordinates: null;
        user: {
            id: number;
            createdAt: Date;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        } | null;
        id: number;
        sessionId: number;
        userId: number | null;
        rating: number;
        comments: string | null;
        ratings: string | null;
        formData: string | null;
        createdAt: Date;
    }[]>;
    getStudentFeedbacks(sessionId: number, userId: number): Promise<{
        id: number;
        sessionId: number;
        userId: number;
        rating: number | null;
        comments: string;
        feedback: string;
        sessionComments: string | null;
        trainerComments: string | null;
        teamComments: string | null;
        suggestions: string | null;
        ratings: null;
        formData: null;
        createdAt: Date;
        studentName: string;
        studentEmail: string;
        user: {
            id: number;
            createdAt: Date;
            name: string | null;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    }[]>;
    getSessionFeedbackList(sessionId: number): Promise<{
        id: any;
        userId: any;
        studentName: any;
        studentEmail: any;
        fullFeedback: any;
        averageRating: number | null;
        scoreLabel: string;
        emoji: string;
    }[]>;
    getStats(): Promise<{
        totalFeedbacks: number;
        averageRating: number;
        ratingDistribution: {
            Exceptionnel: number;
            Excellent: number;
            'Tr\u00E8s bien': number;
            Bien: number;
            Moyen: number;
            Insuffisant: number;
            'Tr\u00E8s insuffisant': number;
        };
    }>;
    private calculateWeightedScore;
    private getScoreDistribution;
    getAnalytics(range?: string): Promise<{
        averageRating: number;
        ratingDistribution: {
            Exceptionnel: number;
            Excellent: number;
            'Tr\u00E8s bien': number;
            Bien: number;
            Moyen: number;
            Insuffisant: number;
            'Tr\u00E8s insuffisant': number;
        };
        timelineData: {
            date: string;
            count: number;
        }[] | {
            month: string;
            count: number;
        }[];
    }>;
    private filterByTimeRange;
    private generateTimelineData;
}
