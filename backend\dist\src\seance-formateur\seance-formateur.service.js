"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeanceFormateurService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const session2_service_1 = require("../session2/session2.service");
let SeanceFormateurService = class SeanceFormateurService {
    prisma;
    session2Service;
    constructor(prisma, session2Service) {
        this.prisma = prisma;
        this.session2Service = session2Service;
    }
    async create(data, formateurId) {
        const { title, startTime, session2Id } = data;
        if (!title || !startTime || !session2Id || !formateurId) {
            throw new common_1.BadRequestException('Tous les champs sont obligatoires');
        }
        try {
            const seance = await this.prisma.seanceFormateur.create({
                data: {
                    title,
                    startTime: new Date(startTime),
                    session2Id: Number(session2Id),
                    formateurId,
                },
            });
            return { message: 'Séance créée ✅', seanceId: seance.id };
        }
        catch (error) {
            if (error.code === 'P2002') {
                throw new common_1.BadRequestException('Une séance avec ces paramètres existe déjà');
            }
            if (error.code === 'P2003') {
                throw new common_1.BadRequestException('Référence invalide - vérifiez que l\'utilisateur et le programme existent');
            }
            throw new common_1.BadRequestException(error.message || 'Erreur lors de la création de la séance');
        }
    }
    async getAverageRatingForSeance(seanceId) {
        if (!seanceId)
            throw new common_1.BadRequestException('seanceId requis');
        const feedbacks = await this.prisma.seanceFeedback.findMany({
            where: { seanceId },
            select: {
                sessionRating: true,
                contentQuality: true,
                sessionOrganization: true,
                objectivesAchieved: true,
                trainerRating: true,
                trainerClarity: true,
                trainerAvailability: true,
                trainerPedagogy: true,
                trainerInteraction: true,
                teamRating: true,
                teamCollaboration: true,
                teamParticipation: true,
                teamCommunication: true,
            },
        });
        if (feedbacks.length === 0)
            return null;
        const perFeedbackAverages = [];
        feedbacks.forEach(fb => {
            const ratings = [
                fb.sessionRating,
                fb.contentQuality,
                fb.sessionOrganization,
                fb.objectivesAchieved,
                fb.trainerRating,
                fb.trainerClarity,
                fb.trainerAvailability,
                fb.trainerPedagogy,
                fb.trainerInteraction,
                fb.teamRating,
                fb.teamCollaboration,
                fb.teamParticipation,
                fb.teamCommunication,
            ].filter(r => typeof r === 'number' && r >= 1 && r <= 5);
            if (ratings.length > 0) {
                const avg = ratings.reduce((a, b) => a + b, 0) / ratings.length;
                perFeedbackAverages.push(avg);
            }
        });
        if (perFeedbackAverages.length === 0)
            return null;
        const averageRating = perFeedbackAverages.reduce((a, b) => a + b, 0) / perFeedbackAverages.length;
        return parseFloat(averageRating.toFixed(1));
    }
    async findAll() {
        return this.prisma.seanceFormateur.findMany({
            include: {
                formateur: true,
                session2: {
                    include: { program: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findByFormateur(formateurId) {
        if (!formateurId)
            throw new common_1.BadRequestException('formateurId requis');
        const seances = await this.prisma.seanceFormateur.findMany({
            where: { formateurId },
            include: {
                session2: {
                    include: { program: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
        const resultWithRatings = await Promise.all(seances.map(async (seance) => {
            const averageRating = await this.getAverageRatingForSeance(seance.id);
            return {
                ...seance,
                averageRating,
            };
        }));
        return resultWithRatings;
    }
    async findOne(id) {
        if (!id)
            throw new common_1.BadRequestException('id requis');
        const seance = await this.prisma.seanceFormateur.findUnique({
            where: { id },
            include: {
                formateur: true,
                session2: {
                    include: { program: true },
                },
            },
        });
        if (!seance)
            throw new common_1.NotFoundException('Séance non trouvée');
        const averageRating = await this.getAverageRatingForSeance(seance.id);
        return {
            ...seance,
            averageRating,
        };
    }
    async remove(id) {
        if (!id)
            throw new common_1.BadRequestException('id requis');
        await this.prisma.seanceFormateur.delete({ where: { id } });
        return { message: 'Séance supprimée 🗑️' };
    }
    async getSession2Details(session2Id) {
        if (!session2Id)
            throw new common_1.BadRequestException('session2Id requis');
        const session2 = await this.prisma.session2.findUnique({
            where: { id: session2Id },
            include: {
                program: true,
                session2Modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: { include: { contenu: true } },
                            },
                        },
                    },
                },
            },
        });
        if (!session2)
            throw new common_1.NotFoundException('Session2 non trouvée');
        const seances = await this.prisma.seanceFormateur.findMany({
            where: { session2Id },
            select: { id: true },
        });
        if (seances.length === 0)
            return { ...session2, averageRating: null };
        let sumOfSeanceAverages = 0;
        let numberOfValidSeances = 0;
        for (const seance of seances) {
            const seanceAverage = await this.getAverageRatingForSeance(seance.id);
            if (seanceAverage !== null) {
                sumOfSeanceAverages += seanceAverage;
                numberOfValidSeances += 1;
            }
        }
        if (numberOfValidSeances === 0) {
            return { ...session2, averageRating: null };
        }
        const averageRating = sumOfSeanceAverages / numberOfValidSeances;
        return {
            ...session2,
            averageRating: parseFloat(averageRating.toFixed(1)),
        };
    }
    async addMediaToSeance(data) {
        const { seanceId, type, fileUrl } = data;
        if (!seanceId || !type || !fileUrl) {
            throw new common_1.BadRequestException('Tous les champs sont obligatoires pour le média');
        }
        return this.prisma.seanceMedia.create({
            data: { seanceId, type, fileUrl },
        });
    }
    async removeMedia(id) {
        if (!id)
            throw new common_1.BadRequestException('id requis');
        return this.prisma.seanceMedia.delete({ where: { id } });
    }
    async getMediaForSeance(seanceId) {
        if (!seanceId)
            throw new common_1.BadRequestException('seanceId requis');
        return this.prisma.seanceMedia.findMany({ where: { seanceId } });
    }
    async findBySession2(session2Id) {
        if (!session2Id)
            throw new common_1.BadRequestException('session2Id requis');
        const seances = await this.prisma.seanceFormateur.findMany({
            where: { session2Id },
            include: {
                formateur: true,
                session2: {
                    include: { program: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
        const resultWithRatings = await Promise.all(seances.map(async (seance) => {
            const averageRating = await this.getAverageRatingForSeance(seance.id);
            return {
                ...seance,
                averageRating,
            };
        }));
        return resultWithRatings;
    }
};
exports.SeanceFormateurService = SeanceFormateurService;
exports.SeanceFormateurService = SeanceFormateurService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService,
        session2_service_1.Session2Service])
], SeanceFormateurService);
//# sourceMappingURL=seance-formateur.service.js.map