{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\SessionList.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Typography, Paper, Box, Divider, Stack, Button, Avatar, IconButton, TextField, Chip, Dialog, DialogTitle, DialogContent, MenuItem, Select, FormControl, InputLabel } from \"@mui/material\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport PersonAddAlt1Icon from \"@mui/icons-material/PersonAddAlt1\";\nimport RocketLaunchIcon from \"@mui/icons-material/RocketLaunch\";\nimport { Close, Facebook, Twitter, LinkedIn, ContentCopy, Feedback } from \"@mui/icons-material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { toast } from \"react-toastify\";\nimport { useNavigate } from \"react-router-dom\";\nimport AddSessionFeedback from './AddSessionFeedback';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SessionList = () => {\n  _s();\n  var _shareModal$session2, _shareModal$session3, _shareModal$session4, _shareModal$session4$, _shareModal$session5, _shareModal$session5$, _shareModal$session6, _shareModal$session6$, _shareModal$session7, _shareModal$session7$;\n  const [showAddUserId, setShowAddUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState(\"\");\n  const [addLoading, setAddLoading] = useState(false);\n  const [sessions, setSessions] = useState([]);\n  const [assignedUsersMap, setAssignedUsersMap] = useState({});\n  const [sidebarOpen, setSidebarOpen] = useState({});\n  const [shareModal, setShareModal] = useState({\n    open: false,\n    session: null\n  });\n  const [shareText, setShareText] = useState('');\n  const [openFeedbackDialog, setOpenFeedbackDialog] = useState(false);\n  const [selectedSession, setSelectedSession] = useState(null);\n  const {\n    t\n  } = useTranslation();\n  const navigate = useNavigate();\n  const fetchSessions = React.useCallback(async () => {\n    try {\n      const res = await axios.get(\"http://localhost:8000/session2\");\n      setSessions(res.data);\n      const usersMap = {};\n      await Promise.all(res.data.map(async session => {\n        try {\n          const resp = await axios.get(`http://localhost:8000/session2/${session.id}/users`);\n          usersMap[session.id] = resp.data || [];\n        } catch {\n          usersMap[session.id] = [];\n        }\n      }));\n      setAssignedUsersMap(usersMap);\n    } catch {\n      toast.error(t(\"sessions.loadError\"));\n    }\n  }, [t]);\n  useEffect(() => {\n    fetchSessions();\n  }, [fetchSessions]);\n  const handleDelete = async id => {\n    try {\n      await axios.delete(`http://localhost:8000/session2/${id}`);\n      toast.success(t(\"sessions.deleteSuccess\"));\n      fetchSessions();\n    } catch {\n      toast.error(t(\"sessions.deleteError\"));\n    }\n  };\n  const handleStatusChange = async (sessionId, newStatus) => {\n    try {\n      await axios.patch(`http://localhost:8000/session2/${sessionId}/status`, {\n        status: newStatus\n      });\n      toast.success(t(\"sessions.statusUpdated\"));\n      fetchSessions();\n    } catch {\n      toast.error(t(\"sessions.statusUpdateError\"));\n    }\n  };\n  const handleRemoveUser = async (sessionId, userId) => {\n    try {\n      await axios.delete(`http://localhost:8000/session2/${sessionId}/remove-user/${userId}`);\n      toast.success(t(\"sessions.userRemoved\"));\n      await fetchSessions();\n    } catch (e) {\n      var _e$response, _e$response$data;\n      toast.error(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : (_e$response$data = _e$response.data) === null || _e$response$data === void 0 ? void 0 : _e$response$data.message) || t(\"sessions.removeUserError\"));\n    }\n  };\n  const handleAddUser = async sessionId => {\n    if (!userEmail) {\n      toast.error(t(\"sessions.enterEmail\"));\n      return;\n    }\n    setAddLoading(true);\n    try {\n      await axios.post(`http://localhost:8000/session2/${sessionId}/add-user`, {\n        email: userEmail\n      });\n      toast.success(t(\"sessions.userAdded\"));\n      setShowAddUserId(null);\n      setUserEmail(\"\");\n      await fetchSessions();\n    } catch (e) {\n      var _e$response2, _e$response2$data;\n      toast.error(((_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : (_e$response2$data = _e$response2.data) === null || _e$response2$data === void 0 ? void 0 : _e$response2$data.message) || t(\"sessions.addUserError\"));\n    } finally {\n      setAddLoading(false);\n    }\n  };\n  const handleToggleSidebar = sessionId => {\n    setSidebarOpen(prev => ({\n      ...prev,\n      [sessionId]: !prev[sessionId]\n    }));\n  };\n  const handleShare = session => {\n    var _session$program, _session$startDate, _session$endDate, _session$session2Modu;\n    const text = `🌟 ${t(\"sessions.newSessionAvailable\")} 🌟\\n\\n🎯 ${session.name}\\n\\n📚 ${t(\"sessions.program\")}: ${((_session$program = session.program) === null || _session$program === void 0 ? void 0 : _session$program.name) || t(\"sessions.program\")}\\n📅 ${t(\"sessions.period\")}: ${(_session$startDate = session.startDate) === null || _session$startDate === void 0 ? void 0 : _session$startDate.slice(0, 10)} ➜ ${(_session$endDate = session.endDate) === null || _session$endDate === void 0 ? void 0 : _session$endDate.slice(0, 10)}\\n\\n${((_session$session2Modu = session.session2Modules) === null || _session$session2Modu === void 0 ? void 0 : _session$session2Modu.length) > 0 ? `🎓 ${t(\"sessions.includedModules\")}:\\n` + session.session2Modules.map(mod => {\n      var _mod$module;\n      return `✅ ${(_mod$module = mod.module) === null || _mod$module === void 0 ? void 0 : _mod$module.name}`;\n    }).join('\\n') + '\\n\\n' : ''}🚀 ${t(\"sessions.uniqueOpportunity\")}\\n\\n💡 ${t(\"sessions.registerNow\")}\\n\\n#Formation #Éducation #DéveloppementProfessionnel #Apprentissage #Compétences #LMS #Success`;\n    setShareText(text);\n    setShareModal({\n      open: true,\n      session\n    });\n  };\n  const handleSocialShare = platform => {\n    const encodedText = encodeURIComponent(shareText);\n    const urls = {\n      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,\n      twitter: `https://twitter.com/intent/tweet?text=${encodedText}`,\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`\n    };\n    window.open(urls[platform], '_blank', 'width=600,height=400');\n  };\n  const handleCopyText = async () => {\n    try {\n      await navigator.clipboard.writeText(shareText);\n      toast.success(t('sessions.textCopied'));\n    } catch (err) {\n      toast.error(t('sessions.copyError'));\n    }\n  };\n  const handleDownloadPreview = async () => {\n    try {\n      var _shareModal$session;\n      // Dynamically import html2canvas\n      const html2canvas = (await import('html2canvas')).default;\n      const element = document.getElementById(\"session-preview\");\n      if (!element) return;\n      const canvas = await html2canvas(element);\n      const dataURL = canvas.toDataURL(\"image/png\");\n      const link = document.createElement(\"a\");\n      link.href = dataURL;\n      link.download = `session-${((_shareModal$session = shareModal.session) === null || _shareModal$session === void 0 ? void 0 : _shareModal$session.name) || \"preview\"}.png`;\n      link.click();\n    } catch (error) {\n      console.error('Error generating image:', error);\n      toast.error(t('sessions.imageGenerationError'));\n    }\n  };\n  const openFeedbackForm = session => {\n    setSelectedSession(session);\n    setOpenFeedbackDialog(true);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      p: 4,\n      borderRadius: 4,\n      backgroundColor: \"#fefefe\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      fontWeight: \"bold\",\n      gutterBottom: true,\n      children: [\"\\uD83D\\uDCCB \", t('sessions.sessionList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), sessions.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      mt: 2,\n      color: \"text.secondary\",\n      children: t(\"sessions.noSessions\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this) : sessions.map(session => {\n      var _session$program2, _session$startDate2, _session$endDate2, _session$session2Modu2;\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 1,\n        sx: {\n          mt: 4,\n          p: 3,\n          borderRadius: 3,\n          backgroundColor: \"#ffffff\",\n          border: \"1px solid #e0e0e0\",\n          display: \"flex\",\n          flexDirection: \"row\",\n          justifyContent: \"space-between\",\n          alignItems: \"flex-start\",\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          flex: 1,\n          children: [session.imageUrl && /*#__PURE__*/_jsxDEV(Box, {\n            mb: 2,\n            display: \"flex\",\n            justifyContent: \"center\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: session.imageUrl,\n              alt: \"Session\",\n              style: {\n                maxWidth: \"100%\",\n                maxHeight: 180,\n                borderRadius: 16,\n                objectFit: \"cover\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              color: \"primary\",\n              children: [\"\\uD83E\\uDDFE \", session.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: session.status,\n                color: session.status === \"ACTIVE\" ? \"success\" : session.status === \"INACTIVE\" ? \"default\" : session.status === \"COMPLETED\" ? \"primary\" : \"secondary\",\n                sx: {\n                  fontWeight: 700,\n                  textTransform: \"capitalize\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: `status-label-${session.id}`,\n                  children: t(\"sessions.status\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: `status-label-${session.id}`,\n                  value: session.status,\n                  label: t(\"sessions.status\"),\n                  onChange: e => handleStatusChange(session.id, e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ACTIVE\",\n                    children: t(\"sessions.active\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"INACTIVE\",\n                    children: t(\"sessions.inactive\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMPLETED\",\n                    children: t(\"sessions.completed\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ARCHIVED\",\n                    children: t(\"sessions.archived\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), !sidebarOpen[session.id] && /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleDelete(session.id),\n              children: t(\"sessions.delete\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RocketLaunchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 32\n              }, this),\n              onClick: () => navigate(`/sessions/${session.id}/seances`),\n              children: t(\"sessions.join\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(PersonAddAlt1Icon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleToggleSidebar(session.id),\n              children: t(\"sessions.addUser\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              size: \"small\",\n              onClick: () => handleShare(session),\n              children: [\"\\uD83D\\uDCE4 \", t(\"sessions.share\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              onClick: () => navigate(`/sessions/${session.id}/feedbacklist`),\n              children: [\"\\uD83D\\uDCCB \", t(\"sessions.sessionFeedbackList\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"info\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(Feedback, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 32\n              }, this),\n              onClick: () => openFeedbackForm(session),\n              children: [\"\\uD83D\\uDCDD \", t(\"sessions.feedback\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 17\n          }, this), showAddUserId === session.id && /*#__PURE__*/_jsxDEV(Box, {\n            mt: 2,\n            mb: 2,\n            display: \"flex\",\n            gap: 1,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              type: \"email\",\n              placeholder: t(\"sessions.userEmailPlaceholder\"),\n              value: userEmail,\n              onChange: e => setUserEmail(e.target.value),\n              size: \"small\",\n              sx: {\n                minWidth: 220\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"small\",\n              color: \"secondary\",\n              onClick: () => handleAddUser(session.id),\n              disabled: addLoading,\n              children: addLoading ? t(\"sessions.adding\") : t(\"sessions.add\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              size: \"small\",\n              onClick: () => {\n                setShowAddUserId(null);\n                setUserEmail(\"\");\n              },\n              children: t(\"sessions.cancel\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            mb: 0.5,\n            children: [\"\\uD83D\\uDCDA \", t(\"sessions.program\"), \" : \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ((_session$program2 = session.program) === null || _session$program2 === void 0 ? void 0 : _session$program2.name) || t(\"sessions.unknown\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            mb: 0.5,\n            children: [\"\\uD83D\\uDCC5 \", t(\"sessions.period\"), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: (_session$startDate2 = session.startDate) === null || _session$startDate2 === void 0 ? void 0 : _session$startDate2.slice(0, 10)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 43\n            }, this), \" \", t(\"sessions.to\"), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: (_session$endDate2 = session.endDate) === null || _session$endDate2 === void 0 ? void 0 : _session$endDate2.slice(0, 10)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            mb: 0.5,\n            children: [\"\\u2B50 \", t(\"sessions.averageRating\"), \" : \", session.averageFeedbackRating !== null ? /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [session.averageFeedbackRating.toFixed(1), \"/5 (\", session.feedbackCount, \" \", t(\"sessions.feedbacks\"), \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 23\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#999'\n              },\n              children: t(\"sessions.noFeedbacks\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), ((_session$session2Modu2 = session.session2Modules) === null || _session$session2Modu2 === void 0 ? void 0 : _session$session2Modu2.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              fontWeight: \"bold\",\n              children: [\"\\uD83E\\uDDF1 \", t(\"sessions.modulesContent\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this), session.session2Modules.map(mod => {\n              var _mod$module2, _mod$courses;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                mt: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: \"bold\",\n                  color: \"secondary.main\",\n                  children: [\"\\uD83D\\uDCE6 \", (_mod$module2 = mod.module) === null || _mod$module2 === void 0 ? void 0 : _mod$module2.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this), (_mod$courses = mod.courses) === null || _mod$courses === void 0 ? void 0 : _mod$courses.map(c => {\n                  var _c$course, _c$contenus;\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    ml: 2,\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      color: \"text.primary\",\n                      children: [\"\\uD83D\\uDCD8 \", (_c$course = c.course) === null || _c$course === void 0 ? void 0 : _c$course.title]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 1,\n                      mt: 1,\n                      flexWrap: \"wrap\",\n                      children: (_c$contenus = c.contenus) === null || _c$contenus === void 0 ? void 0 : _c$contenus.map(ct => {\n                        var _ct$contenu2;\n                        return /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outlined\",\n                          color: \"info\",\n                          size: \"small\",\n                          sx: {\n                            mr: 1,\n                            mb: 1,\n                            borderRadius: 2\n                          },\n                          onClick: () => {\n                            var _ct$contenu;\n                            return ((_ct$contenu = ct.contenu) === null || _ct$contenu === void 0 ? void 0 : _ct$contenu.fileUrl) && window.open(ct.contenu.fileUrl, \"_blank\");\n                          },\n                          children: [\"\\uD83D\\uDCC4 \", (_ct$contenu2 = ct.contenu) === null || _ct$contenu2 === void 0 ? void 0 : _ct$contenu2.title]\n                        }, ct.id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 391,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 27\n                    }, this)]\n                  }, c.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, mod.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this);\n            })]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), sidebarOpen[session.id] && /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 4,\n          sx: {\n            minWidth: 350,\n            maxWidth: 400,\n            bgcolor: \"#f8fbff\",\n            borderRadius: 4,\n            p: 3,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"stretch\",\n            boxShadow: \"0 12px 36px 0 rgba(25, 118, 210, 0.09)\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              fontWeight: 700,\n              color: \"primary\",\n              fontSize: 18,\n              children: t(\"sessions.members\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              color: \"primary\",\n              onClick: () => handleToggleSidebar(session.id),\n              sx: {\n                fontWeight: 600,\n                fontSize: 14,\n                textTransform: \"none\"\n              },\n              children: t(\"sessions.hide\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            mb: 3,\n            bgcolor: \"#eaf0f9\",\n            borderRadius: 2,\n            p: 1.5,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              type: \"email\",\n              placeholder: t(\"sessions.addByEmail\"),\n              value: showAddUserId === session.id ? userEmail : \"\",\n              onFocus: () => setShowAddUserId(session.id),\n              onChange: e => setUserEmail(e.target.value),\n              variant: \"outlined\",\n              sx: {\n                flex: 1,\n                bgcolor: \"#fff\",\n                borderRadius: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              disabled: addLoading,\n              onClick: () => handleAddUser(session.id),\n              sx: {\n                bgcolor: \"#1976d2\",\n                color: \"#fff\",\n                \"&:hover\": {\n                  bgcolor: \"#1565c0\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(PersonAddAlt1Icon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: (assignedUsersMap[session.id] || []).length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              fontSize: 14,\n              children: t(\"sessions.noUsers\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 21\n            }, this) : assignedUsersMap[session.id].map(user => /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              p: 2,\n              bgcolor: \"#fff\",\n              borderRadius: 2,\n              sx: {\n                boxShadow: \"0 2px 8px rgba(25,118,210,.04)\",\n                cursor: \"pointer\",\n                transition: \"background .15s\",\n                \"&:hover\": {\n                  background: \"#f0f6ff\"\n                }\n              },\n              onClick: () => navigate(`/ProfilePage/${user.id}`),\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: user.profilePic || undefined,\n                sx: {\n                  width: 38,\n                  height: 38,\n                  fontWeight: 700,\n                  fontSize: 16,\n                  bgcolor: user.profilePic ? \"transparent\" : \"#B5C7D3\"\n                },\n                children: !user.profilePic && user.name ? user.name[0].toUpperCase() : null\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: 600,\n                  fontSize: 14,\n                  color: \"#222\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  fontSize: 12,\n                  color: \"#999\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: e => {\n                  e.stopPropagation();\n                  handleRemoveUser(session.id, user.id);\n                },\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 25\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this)]\n      }, session.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: shareModal.open,\n      onClose: () => setShareModal({\n        open: false,\n        session: null\n      }),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"\\uD83D\\uDCE4 \", t(\"sessions.shareSession\"), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setShareModal({\n            open: false,\n            session: null\n          }),\n          sx: {\n            position: 'absolute',\n            right: 8,\n            top: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          id: \"session-preview\",\n          sx: {\n            borderRadius: 4,\n            overflow: \"hidden\",\n            bgcolor: \"#ffffff\",\n            border: \"2px solid #1976d2\",\n            boxShadow: 3,\n            mb: 3,\n            maxWidth: 800,\n            mx: \"auto\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              background: \"linear-gradient(90deg, #1976d2, #42a5f5)\",\n              color: \"#fff\",\n              p: 3,\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"bold\",\n              children: [\"\\uD83C\\uDF93 \", (_shareModal$session2 = shareModal.session) === null || _shareModal$session2 === void 0 ? void 0 : _shareModal$session2.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: [\"\\uD83D\\uDE80 \", t(\"sessions.newOpportunity\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), ((_shareModal$session3 = shareModal.session) === null || _shareModal$session3 === void 0 ? void 0 : _shareModal$session3.imageUrl) && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              backgroundColor: \"#e3f2fd\",\n              p: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: shareModal.session.imageUrl,\n              alt: \"Session\",\n              style: {\n                maxWidth: \"100%\",\n                maxHeight: 240,\n                borderRadius: 12,\n                objectFit: \"cover\",\n                boxShadow: \"0 4px 16px rgba(0,0,0,0.15)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 16,\n              mb: 1,\n              children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: t(\"sessions.program\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 20\n              }, this), \" : \", (_shareModal$session4 = shareModal.session) === null || _shareModal$session4 === void 0 ? void 0 : (_shareModal$session4$ = _shareModal$session4.program) === null || _shareModal$session4$ === void 0 ? void 0 : _shareModal$session4$.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 16,\n              mb: 2,\n              children: [\"\\uD83D\\uDCC5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: t(\"sessions.period\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 20\n              }, this), \" :\", \" \", (_shareModal$session5 = shareModal.session) === null || _shareModal$session5 === void 0 ? void 0 : (_shareModal$session5$ = _shareModal$session5.startDate) === null || _shareModal$session5$ === void 0 ? void 0 : _shareModal$session5$.slice(0, 10), \" \\u279C \", (_shareModal$session6 = shareModal.session) === null || _shareModal$session6 === void 0 ? void 0 : (_shareModal$session6$ = _shareModal$session6.endDate) === null || _shareModal$session6$ === void 0 ? void 0 : _shareModal$session6$.slice(0, 10)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this), ((_shareModal$session7 = shareModal.session) === null || _shareModal$session7 === void 0 ? void 0 : (_shareModal$session7$ = _shareModal$session7.session2Modules) === null || _shareModal$session7$ === void 0 ? void 0 : _shareModal$session7$.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                fontWeight: \"bold\",\n                fontSize: 16,\n                mb: 1,\n                children: [\"\\uD83E\\uDDF1 \", t(\"sessions.modulesContent\")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  paddingLeft: 20\n                },\n                children: shareModal.session.session2Modules.map(mod => {\n                  var _mod$module3;\n                  return /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      fontSize: 14,\n                      children: [\"\\u2705 \", (_mod$module3 = mod.module) === null || _mod$module3 === void 0 ? void 0 : _mod$module3.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this)\n                  }, mod.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 14,\n              mt: 3,\n              color: \"text.secondary\",\n              children: \"#Formation #\\xC9ducation #LMS #Apprentissage #Succ\\xE8s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          multiline: true,\n          rows: 8,\n          fullWidth: true,\n          value: shareText,\n          onChange: e => setShareText(e.target.value),\n          variant: \"outlined\",\n          label: `📝 ${t(\"sessions.customizePost\")}`,\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          flexWrap: \"wrap\",\n          gap: 1,\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('facebook'),\n            sx: {\n              bgcolor: '#1877f2'\n            },\n            children: \"Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Twitter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('twitter'),\n            sx: {\n              bgcolor: '#1da1f2'\n            },\n            children: \"Twitter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(LinkedIn, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('linkedin'),\n            sx: {\n              bgcolor: '#0077b5'\n            },\n            children: \"LinkedIn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(ContentCopy, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 51\n            }, this),\n            onClick: handleCopyText,\n            children: [\"\\uD83D\\uDCCB \", t(\"sessions.copyText\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleDownloadPreview,\n            children: [\"\\uD83D\\uDCBE \", t(\"sessions.downloadPreview\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddSessionFeedback, {\n      open: openFeedbackDialog,\n      onClose: () => setOpenFeedbackDialog(false),\n      session: selectedSession\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(SessionList, \"2Mzd3JKXmtPJanl74M9iUhTTJFE=\", false, function () {\n  return [useTranslation, useNavigate];\n});\n_c = SessionList;\nexport default SessionList;\nvar _c;\n$RefreshReg$(_c, \"SessionList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Typography", "Paper", "Box", "Divider", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "IconButton", "TextField", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuItem", "Select", "FormControl", "InputLabel", "DeleteIcon", "PersonAddAlt1Icon", "RocketLaunchIcon", "Close", "Facebook", "Twitter", "LinkedIn", "ContentCopy", "<PERSON><PERSON><PERSON>", "useTranslation", "axios", "toast", "useNavigate", "AddSessionFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SessionList", "_s", "_shareModal$session2", "_shareModal$session3", "_shareModal$session4", "_shareModal$session4$", "_shareModal$session5", "_shareModal$session5$", "_shareModal$session6", "_shareModal$session6$", "_shareModal$session7", "_shareModal$session7$", "showAddUserId", "setShowAddUserId", "userEmail", "setUserEmail", "addLoading", "setAddLoading", "sessions", "setSessions", "assignedUsersMap", "setAssignedUsersMap", "sidebarOpen", "setSidebarOpen", "shareModal", "setShareModal", "open", "session", "shareText", "setShareText", "openFeedbackDialog", "setOpenFeedbackDialog", "selectedSession", "setSelectedSession", "t", "navigate", "fetchSessions", "useCallback", "res", "get", "data", "usersMap", "Promise", "all", "map", "resp", "id", "error", "handleDelete", "delete", "success", "handleStatusChange", "sessionId", "newStatus", "patch", "status", "handleRemoveUser", "userId", "e", "_e$response", "_e$response$data", "response", "message", "handleAddUser", "post", "email", "_e$response2", "_e$response2$data", "handleToggleSidebar", "prev", "handleShare", "_session$program", "_session$startDate", "_session$endDate", "_session$session2Modu", "text", "name", "program", "startDate", "slice", "endDate", "session2Modules", "length", "mod", "_mod$module", "module", "join", "handleSocialShare", "platform", "encodedText", "encodeURIComponent", "urls", "facebook", "window", "location", "href", "twitter", "linkedin", "handleCopyText", "navigator", "clipboard", "writeText", "err", "handleDownloadPreview", "_shareModal$session", "html2canvas", "default", "element", "document", "getElementById", "canvas", "dataURL", "toDataURL", "link", "createElement", "download", "click", "console", "openFeedbackForm", "elevation", "sx", "p", "borderRadius", "backgroundColor", "children", "variant", "fontWeight", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mt", "color", "_session$program2", "_session$startDate2", "_session$endDate2", "_session$session2Modu2", "border", "display", "flexDirection", "justifyContent", "alignItems", "gap", "flex", "imageUrl", "mb", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "maxHeight", "objectFit", "direction", "spacing", "label", "textTransform", "size", "min<PERSON><PERSON><PERSON>", "labelId", "value", "onChange", "target", "startIcon", "onClick", "type", "placeholder", "disabled", "averageFeedbackRating", "toFixed", "feedbackCount", "my", "_mod$module2", "_mod$courses", "courses", "c", "_c$course", "_c$contenus", "ml", "course", "title", "flexWrap", "contenus", "ct", "_ct$contenu2", "mr", "_ct$contenu", "contenu", "fileUrl", "bgcolor", "boxShadow", "fontSize", "onFocus", "user", "cursor", "transition", "background", "profilePic", "undefined", "width", "height", "toUpperCase", "role", "stopPropagation", "onClose", "fullWidth", "position", "right", "top", "overflow", "mx", "textAlign", "paddingLeft", "_mod$module3", "multiline", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/SessionList.js"], "sourcesContent": [" import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Typo<PERSON>,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  Stack,\r\n  Button,\r\n  Avatar,\r\n  IconButton,\r\n  TextField,\r\n  Chip,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  MenuItem,\r\n  Select,\r\n  FormControl,\r\n  InputLabel,\r\n} from \"@mui/material\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport PersonAddAlt1Icon from \"@mui/icons-material/PersonAddAlt1\";\r\nimport RocketLaunchIcon from \"@mui/icons-material/RocketLaunch\";\r\nimport { Close, Facebook, Twitter, LinkedIn, ContentCopy, Feedback } from \"@mui/icons-material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AddSessionFeedback from './AddSessionFeedback';\r\n\r\nconst SessionList = () => {\r\n  const [showAddUserId, setShowAddUserId] = useState(null);\r\n  const [userEmail, setUserEmail] = useState(\"\");\r\n  const [addLoading, setAddLoading] = useState(false);\r\n  const [sessions, setSessions] = useState([]);\r\n  const [assignedUsersMap, setAssignedUsersMap] = useState({});\r\n  const [sidebarOpen, setSidebarOpen] = useState({});\r\n  const [shareModal, setShareModal] = useState({ open: false, session: null });\r\n  const [shareText, setShareText] = useState('');\r\n  const [openFeedbackDialog, setOpenFeedbackDialog] = useState(false);\r\n  const [selectedSession, setSelectedSession] = useState(null);\r\n  const { t } = useTranslation();\r\n  const navigate = useNavigate();\r\n\r\n  const fetchSessions = React.useCallback(async () => {\r\n    try {\r\n      const res = await axios.get(\"http://localhost:8000/session2\");\r\n      setSessions(res.data);\r\n      const usersMap = {};\r\n      await Promise.all(\r\n        res.data.map(async (session) => {\r\n          try {\r\n            const resp = await axios.get(`http://localhost:8000/session2/${session.id}/users`);\r\n            usersMap[session.id] = resp.data || [];\r\n          } catch {\r\n            usersMap[session.id] = [];\r\n          }\r\n        })\r\n      );\r\n      setAssignedUsersMap(usersMap);\r\n    } catch {\r\n      toast.error(t(\"sessions.loadError\"));\r\n    }\r\n  }, [t]);\r\n\r\n  useEffect(() => {\r\n    fetchSessions();\r\n  }, [fetchSessions]);\r\n\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/session2/${id}`);\r\n      toast.success(t(\"sessions.deleteSuccess\"));\r\n      fetchSessions();\r\n    } catch {\r\n      toast.error(t(\"sessions.deleteError\"));\r\n    }\r\n  };\r\n\r\n  const handleStatusChange = async (sessionId, newStatus) => {\r\n    try {\r\n      await axios.patch(`http://localhost:8000/session2/${sessionId}/status`, {\r\n        status: newStatus,\r\n      });\r\n      toast.success(t(\"sessions.statusUpdated\"));\r\n      fetchSessions();\r\n    } catch {\r\n      toast.error(t(\"sessions.statusUpdateError\"));\r\n    }\r\n  };\r\n\r\n  const handleRemoveUser = async (sessionId, userId) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/session2/${sessionId}/remove-user/${userId}`);\r\n      toast.success(t(\"sessions.userRemoved\"));\r\n      await fetchSessions();\r\n    } catch (e) {\r\n      toast.error(\r\n        e.response?.data?.message || t(\"sessions.removeUserError\")\r\n      );\r\n    }\r\n  };\r\n\r\n  const handleAddUser = async (sessionId) => {\r\n    if (!userEmail) {\r\n      toast.error(t(\"sessions.enterEmail\"));\r\n      return;\r\n    }\r\n    setAddLoading(true);\r\n    try {\r\n      await axios.post(`http://localhost:8000/session2/${sessionId}/add-user`, {\r\n        email: userEmail,\r\n      });\r\n      toast.success(t(\"sessions.userAdded\"));\r\n      setShowAddUserId(null);\r\n      setUserEmail(\"\");\r\n      await fetchSessions();\r\n    } catch (e) {\r\n      toast.error(\r\n        e.response?.data?.message || t(\"sessions.addUserError\")\r\n      );\r\n    } finally {\r\n      setAddLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleToggleSidebar = (sessionId) => {\r\n    setSidebarOpen(prev => ({\r\n      ...prev,\r\n      [sessionId]: !prev[sessionId]\r\n    }));\r\n  };\r\n\r\n  const handleShare = (session) => {\r\n    const text = `🌟 ${t(\"sessions.newSessionAvailable\")} 🌟\\n\\n🎯 ${session.name}\\n\\n📚 ${t(\"sessions.program\")}: ${session.program?.name || t(\"sessions.program\")}\\n📅 ${t(\"sessions.period\")}: ${session.startDate?.slice(0, 10)} ➜ ${session.endDate?.slice(0, 10)}\\n\\n${session.session2Modules?.length > 0 ? `🎓 ${t(\"sessions.includedModules\")}:\\n` + session.session2Modules.map(mod => `✅ ${mod.module?.name}`).join('\\n') + '\\n\\n' : ''}🚀 ${t(\"sessions.uniqueOpportunity\")}\\n\\n💡 ${t(\"sessions.registerNow\")}\\n\\n#Formation #Éducation #DéveloppementProfessionnel #Apprentissage #Compétences #LMS #Success`;\r\n    setShareText(text);\r\n    setShareModal({ open: true, session });\r\n  };\r\n\r\n  const handleSocialShare = (platform) => {\r\n    const encodedText = encodeURIComponent(shareText);\r\n    const urls = {\r\n      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,\r\n      twitter: `https://twitter.com/intent/tweet?text=${encodedText}`,\r\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`\r\n    };\r\n    window.open(urls[platform], '_blank', 'width=600,height=400');\r\n  };\r\n\r\n  const handleCopyText = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(shareText);\r\n      toast.success(t('sessions.textCopied'));\r\n    } catch (err) {\r\n      toast.error(t('sessions.copyError'));\r\n    }\r\n  };\r\n\r\n  const handleDownloadPreview = async () => {\r\n    try {\r\n      // Dynamically import html2canvas\r\n      const html2canvas = (await import('html2canvas')).default;\r\n      \r\n      const element = document.getElementById(\"session-preview\");\r\n      if (!element) return;\r\n\r\n      const canvas = await html2canvas(element);\r\n      const dataURL = canvas.toDataURL(\"image/png\");\r\n\r\n      const link = document.createElement(\"a\");\r\n      link.href = dataURL;\r\n      link.download = `session-${shareModal.session?.name || \"preview\"}.png`;\r\n      link.click();\r\n    } catch (error) {\r\n      console.error('Error generating image:', error);\r\n      toast.error(t('sessions.imageGenerationError'));\r\n    }\r\n  };\r\n\r\n  const openFeedbackForm = (session) => {\r\n    setSelectedSession(session);\r\n    setOpenFeedbackDialog(true);\r\n  };\r\n\r\n  return (\r\n    <Paper elevation={3} sx={{ p: 4, borderRadius: 4, backgroundColor: \"#fefefe\" }}>\r\n      <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom>\r\n        📋 {t('sessions.sessionList')}\r\n      </Typography>\r\n\r\n      {sessions.length === 0 ? (\r\n        <Typography mt={2} color=\"text.secondary\">\r\n          {t(\"sessions.noSessions\")}\r\n        </Typography>\r\n      ) : (\r\n        sessions.map((session) => (\r\n          <Paper\r\n            key={session.id}\r\n            elevation={1}\r\n            sx={{\r\n              mt: 4,\r\n              p: 3,\r\n              borderRadius: 3,\r\n              backgroundColor: \"#ffffff\",\r\n              border: \"1px solid #e0e0e0\",\r\n              display: \"flex\",\r\n              flexDirection: \"row\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"flex-start\",\r\n              gap: 3\r\n            }}\r\n          >\r\n            {/* Main Content */}\r\n            <Box flex={1}>\r\n              {session.imageUrl && (\r\n                <Box mb={2} display=\"flex\" justifyContent=\"center\">\r\n                  <img\r\n                    src={session.imageUrl}\r\n                    alt=\"Session\"\r\n                    style={{\r\n                      maxWidth: \"100%\",\r\n                      maxHeight: 180,\r\n                      borderRadius: 16,\r\n                      objectFit: \"cover\",\r\n                    }}\r\n                  />\r\n                </Box>\r\n              )}\r\n\r\n              <Stack\r\n                direction=\"row\"\r\n                justifyContent=\"space-between\"\r\n                alignItems=\"center\"\r\n                mb={1}\r\n              >\r\n                <Typography variant=\"h6\" fontWeight=\"bold\" color=\"primary\">\r\n                  🧾 {session.name}\r\n                </Typography>\r\n                <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n                  <Chip\r\n                    label={session.status}\r\n                    color={\r\n                      session.status === \"ACTIVE\"\r\n                        ? \"success\"\r\n                        : session.status === \"INACTIVE\"\r\n                        ? \"default\"\r\n                        : session.status === \"COMPLETED\"\r\n                        ? \"primary\"\r\n                        : \"secondary\"\r\n                    }\r\n                    sx={{ fontWeight: 700, textTransform: \"capitalize\" }}\r\n                  />\r\n                  <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n                    <InputLabel id={`status-label-${session.id}`}>{t(\"sessions.status\")}</InputLabel>\r\n                    <Select\r\n                      labelId={`status-label-${session.id}`}\r\n                      value={session.status}\r\n                      label={t(\"sessions.status\")}\r\n                      onChange={e => handleStatusChange(session.id, e.target.value)}\r\n                    >\r\n                      <MenuItem value=\"ACTIVE\">{t(\"sessions.active\")}</MenuItem>\r\n                      <MenuItem value=\"INACTIVE\">{t(\"sessions.inactive\")}</MenuItem>\r\n                      <MenuItem value=\"COMPLETED\">{t(\"sessions.completed\")}</MenuItem>\r\n                      <MenuItem value=\"ARCHIVED\">{t(\"sessions.archived\")}</MenuItem>\r\n                    </Select>\r\n                  </FormControl>\r\n                </Stack>\r\n              </Stack>\r\n\r\n              {!sidebarOpen[session.id] && (\r\n                <Stack direction=\"row\" spacing={1} alignItems=\"center\" mb={2}>\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    color=\"error\"\r\n                    size=\"small\"\r\n                    startIcon={<DeleteIcon />}\r\n                    onClick={() => handleDelete(session.id)}\r\n                  >\r\n                    {t(\"sessions.delete\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    startIcon={<RocketLaunchIcon />}\r\n                    onClick={() => navigate(`/sessions/${session.id}/seances`)}\r\n                  >\r\n                    {t(\"sessions.join\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    startIcon={<PersonAddAlt1Icon />}\r\n                    onClick={() => handleToggleSidebar(session.id)}\r\n                  >\r\n                    {t(\"sessions.addUser\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    color=\"secondary\"\r\n                    size=\"small\"\r\n                    onClick={() => handleShare(session)}\r\n                  >\r\n                    📤 {t(\"sessions.share\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    onClick={() => navigate(`/sessions/${session.id}/feedbacklist`)}\r\n                  >\r\n                    📋 {t(\"sessions.sessionFeedbackList\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"info\"\r\n                    size=\"small\"\r\n                    startIcon={<Feedback />}\r\n                    onClick={() => openFeedbackForm(session)}\r\n                  >\r\n                    📝 {t(\"sessions.feedback\")}\r\n                  </Button>\r\n                </Stack>\r\n              )}\r\n\r\n              {/* Add User Section */}\r\n              {showAddUserId === session.id && (\r\n                <Box mt={2} mb={2} display=\"flex\" gap={1} alignItems=\"center\">\r\n                  <TextField\r\n                    type=\"email\"\r\n                    placeholder={t(\"sessions.userEmailPlaceholder\")}\r\n                    value={userEmail}\r\n                    onChange={e => setUserEmail(e.target.value)}\r\n                    size=\"small\"\r\n                    sx={{ minWidth: 220 }}\r\n                  />\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    size=\"small\"\r\n                    color=\"secondary\"\r\n                    onClick={() => handleAddUser(session.id)}\r\n                    disabled={addLoading}\r\n                  >\r\n                    {addLoading ? t(\"sessions.adding\") : t(\"sessions.add\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"text\"\r\n                    size=\"small\"\r\n                    onClick={() => { setShowAddUserId(null); setUserEmail(\"\"); }}\r\n                  >\r\n                    {t(\"sessions.cancel\")}\r\n                  </Button>\r\n                </Box>\r\n              )}\r\n\r\n              <Typography variant=\"body2\" mb={0.5}>\r\n                📚 {t(\"sessions.program\")} : <strong>{session.program?.name || t(\"sessions.unknown\")}</strong>\r\n              </Typography>\r\n              <Typography variant=\"body2\" mb={0.5}>\r\n                📅 {t(\"sessions.period\")} <strong>{session.startDate?.slice(0, 10)}</strong> {t(\"sessions.to\")}{\" \"}\r\n                <strong>{session.endDate?.slice(0, 10)}</strong>\r\n              </Typography>\r\n              <Typography variant=\"body2\" mb={0.5}>\r\n                ⭐ {t(\"sessions.averageRating\")} : {\r\n                  session.averageFeedbackRating !== null\r\n                    ? <strong>{session.averageFeedbackRating.toFixed(1)}/5 ({session.feedbackCount} {t(\"sessions.feedbacks\")})</strong>\r\n                    : <span style={{ color: '#999' }}>{t(\"sessions.noFeedbacks\")}</span>\r\n                }\r\n              </Typography>\r\n\r\n              {/* Modules and Contents */}\r\n              {session.session2Modules?.length > 0 && (\r\n                <>\r\n                  <Divider sx={{ my: 2 }} />\r\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\r\n                    🧱 {t(\"sessions.modulesContent\")}\r\n                  </Typography>\r\n                  {session.session2Modules.map((mod) => (\r\n                    <Box key={mod.id} mt={1}>\r\n                      <Typography fontWeight=\"bold\" color=\"secondary.main\">\r\n                        📦 {mod.module?.name}\r\n                      </Typography>\r\n                      {mod.courses?.map((c) => (\r\n                        <Box key={c.id} ml={2} mt={1}>\r\n                          <Typography variant=\"body2\" fontWeight=\"bold\" color=\"text.primary\">\r\n                            📘 {c.course?.title}\r\n                          </Typography>\r\n                          <Stack direction=\"row\" spacing={1} mt={1} flexWrap=\"wrap\">\r\n                            {c.contenus?.map((ct) => (\r\n                              <Button\r\n                                key={ct.id}\r\n                                variant=\"outlined\"\r\n                                color=\"info\"\r\n                                size=\"small\"\r\n                                sx={{ mr: 1, mb: 1, borderRadius: 2 }}\r\n                                onClick={() =>\r\n                                  ct.contenu?.fileUrl &&\r\n                                  window.open(ct.contenu.fileUrl, \"_blank\")\r\n                                }\r\n                              >\r\n                                📄 {ct.contenu?.title}\r\n                              </Button>\r\n                            ))}\r\n                          </Stack>\r\n                        </Box>\r\n                      ))}\r\n                    </Box>\r\n                  ))}\r\n                </>\r\n              )}\r\n            </Box>\r\n\r\n            {/* Sidebar for Users */}\r\n            {sidebarOpen[session.id] && (\r\n              <Paper\r\n                elevation={4}\r\n                sx={{\r\n                  minWidth: 350,\r\n                  maxWidth: 400,\r\n                  bgcolor: \"#f8fbff\",\r\n                  borderRadius: 4,\r\n                  p: 3,\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"stretch\",\r\n                  boxShadow: \"0 12px 36px 0 rgba(25, 118, 210, 0.09)\",\r\n                }}\r\n              >\r\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\r\n                  <Typography fontWeight={700} color=\"primary\" fontSize={18}>\r\n                    {t(\"sessions.members\")}\r\n                  </Typography>\r\n                  <Button\r\n                    variant=\"text\"\r\n                    color=\"primary\"\r\n                    onClick={() => handleToggleSidebar(session.id)}\r\n                    sx={{ fontWeight: 600, fontSize: 14, textTransform: \"none\" }}\r\n                  >\r\n                    {t(\"sessions.hide\")}\r\n                  </Button>\r\n                </Box>\r\n\r\n                {/* Add User Input */}\r\n                <Box\r\n                  display=\"flex\"\r\n                  alignItems=\"center\"\r\n                  gap={1}\r\n                  mb={3}\r\n                  bgcolor=\"#eaf0f9\"\r\n                  borderRadius={2}\r\n                  p={1.5}\r\n                >\r\n                  <TextField\r\n                    size=\"small\"\r\n                    type=\"email\"\r\n                    placeholder={t(\"sessions.addByEmail\")}\r\n                    value={showAddUserId === session.id ? userEmail : \"\"}\r\n                    onFocus={() => setShowAddUserId(session.id)}\r\n                    onChange={(e) => setUserEmail(e.target.value)}\r\n                    variant=\"outlined\"\r\n                    sx={{ flex: 1, bgcolor: \"#fff\", borderRadius: 2 }}\r\n                  />\r\n                  <IconButton\r\n                    color=\"primary\"\r\n                    disabled={addLoading}\r\n                    onClick={() => handleAddUser(session.id)}\r\n                    sx={{ bgcolor: \"#1976d2\", color: \"#fff\", \"&:hover\": { bgcolor: \"#1565c0\" } }}\r\n                  >\r\n                    <PersonAddAlt1Icon />\r\n                  </IconButton>\r\n                </Box>\r\n\r\n                {/* Users List */}\r\n                <Stack spacing={2}>\r\n                  {(assignedUsersMap[session.id] || []).length === 0 ? (\r\n                    <Typography color=\"text.secondary\" fontSize={14}>\r\n                      {t(\"sessions.noUsers\")}\r\n                    </Typography>\r\n                  ) : (\r\n                    assignedUsersMap[session.id].map((user) => (\r\n                      <Box\r\n                        key={user.id}\r\n                        display=\"flex\"\r\n                        alignItems=\"center\"\r\n                        gap={2}\r\n                        p={2}\r\n                        bgcolor=\"#fff\"\r\n                        borderRadius={2}\r\n                        sx={{\r\n                          boxShadow: \"0 2px 8px rgba(25,118,210,.04)\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"background .15s\",\r\n                          \"&:hover\": { background: \"#f0f6ff\" }\r\n                        }}\r\n                        onClick={() => navigate(`/ProfilePage/${user.id}`)}\r\n                      >\r\n                        <Avatar\r\n                          src={user.profilePic || undefined}\r\n                          sx={{\r\n                            width: 38, height: 38, fontWeight: 700, fontSize: 16,\r\n                            bgcolor: user.profilePic ? \"transparent\" : \"#B5C7D3\",\r\n                          }}\r\n                        >\r\n                          {!user.profilePic && user.name ? user.name[0].toUpperCase() : null}\r\n                        </Avatar>\r\n                        <Box sx={{ flex: 1 }}>\r\n                          <Typography fontWeight={600} fontSize={14} color=\"#222\">\r\n                            {user.name}\r\n                          </Typography>\r\n                          <Typography fontSize={12} color=\"#999\">\r\n                            {user.role}\r\n                          </Typography>\r\n                        </Box>\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          color=\"error\"\r\n                          onClick={e => {\r\n                            e.stopPropagation();\r\n                            handleRemoveUser(session.id, user.id);\r\n                          }}\r\n                        >\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Box>\r\n                    ))\r\n                  )}\r\n                </Stack>\r\n              </Paper>\r\n            )}\r\n          </Paper>\r\n        ))\r\n      )}\r\n\r\n      {/* Share Modal */}\r\n      <Dialog open={shareModal.open} onClose={() => setShareModal({ open: false, session: null })} maxWidth=\"md\" fullWidth>\r\n        <DialogTitle>\r\n          📤 {t(\"sessions.shareSession\")}\r\n          <IconButton onClick={() => setShareModal({ open: false, session: null })} sx={{ position: 'absolute', right: 8, top: 8 }}>\r\n            <Close />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          {/* Session Preview Widget */}\r\n          <Box\r\n            id=\"session-preview\"\r\n            sx={{\r\n              borderRadius: 4,\r\n              overflow: \"hidden\",\r\n              bgcolor: \"#ffffff\",\r\n              border: \"2px solid #1976d2\",\r\n              boxShadow: 3,\r\n              mb: 3,\r\n              maxWidth: 800,\r\n              mx: \"auto\"\r\n            }}\r\n          >\r\n            <Box\r\n              sx={{\r\n                background: \"linear-gradient(90deg, #1976d2, #42a5f5)\",\r\n                color: \"#fff\",\r\n                p: 3,\r\n                textAlign: \"center\"\r\n              }}\r\n            >\r\n              <Typography variant=\"h5\" fontWeight=\"bold\">\r\n                🎓 {shareModal.session?.name}\r\n              </Typography>\r\n              <Typography variant=\"subtitle1\">\r\n                🚀 {t(\"sessions.newOpportunity\")}\r\n              </Typography>\r\n            </Box>\r\n\r\n            {shareModal.session?.imageUrl && (\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  backgroundColor: \"#e3f2fd\",\r\n                  p: 2\r\n                }}\r\n              >\r\n                <img\r\n                  src={shareModal.session.imageUrl}\r\n                  alt=\"Session\"\r\n                  style={{\r\n                    maxWidth: \"100%\",\r\n                    maxHeight: 240,\r\n                    borderRadius: 12,\r\n                    objectFit: \"cover\",\r\n                    boxShadow: \"0 4px 16px rgba(0,0,0,0.15)\"\r\n                  }}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            <Box sx={{ p: 3 }}>\r\n              <Typography fontSize={16} mb={1}>\r\n                📚 <strong>{t(\"sessions.program\")}</strong> : {shareModal.session?.program?.name}\r\n              </Typography>\r\n              <Typography fontSize={16} mb={2}>\r\n                📅 <strong>{t(\"sessions.period\")}</strong> :{\" \"}\r\n                {shareModal.session?.startDate?.slice(0, 10)} ➜ {shareModal.session?.endDate?.slice(0, 10)}\r\n              </Typography>\r\n\r\n              {shareModal.session?.session2Modules?.length > 0 && (\r\n                <>\r\n                  <Typography fontWeight=\"bold\" fontSize={16} mb={1}>\r\n                    🧱 {t(\"sessions.modulesContent\")}\r\n                  </Typography>\r\n                  <ul style={{ paddingLeft: 20 }}>\r\n                    {shareModal.session.session2Modules.map((mod) => (\r\n                      <li key={mod.id}>\r\n                        <Typography fontSize={14}>✅ {mod.module?.name}</Typography>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </>\r\n              )}\r\n\r\n              <Typography fontSize={14} mt={3} color=\"text.secondary\">\r\n                #Formation #Éducation #LMS #Apprentissage #Succès\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          {/* Share Text and Buttons */}\r\n          <TextField\r\n            multiline\r\n            rows={8}\r\n            fullWidth\r\n            value={shareText}\r\n            onChange={(e) => setShareText(e.target.value)}\r\n            variant=\"outlined\"\r\n            label={`📝 ${t(\"sessions.customizePost\")}`}\r\n            sx={{ mb: 3 }}\r\n          />\r\n\r\n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\" gap={1} mb={2}>\r\n            <Button variant=\"contained\" startIcon={<Facebook />} onClick={() => handleSocialShare('facebook')} sx={{ bgcolor: '#1877f2' }}>Facebook</Button>\r\n            <Button variant=\"contained\" startIcon={<Twitter />} onClick={() => handleSocialShare('twitter')} sx={{ bgcolor: '#1da1f2' }}>Twitter</Button>\r\n            <Button variant=\"contained\" startIcon={<LinkedIn />} onClick={() => handleSocialShare('linkedin')} sx={{ bgcolor: '#0077b5' }}>LinkedIn</Button>\r\n            <Button variant=\"outlined\" startIcon={<ContentCopy />} onClick={handleCopyText}>📋 {t(\"sessions.copyText\")}</Button>\r\n            <Button variant=\"outlined\" onClick={handleDownloadPreview}>💾 {t(\"sessions.downloadPreview\")}</Button>\r\n          </Stack>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Feedback Dialog */}\r\n      <AddSessionFeedback \r\n        open={openFeedbackDialog} \r\n        onClose={() => setOpenFeedbackDialog(false)}\r\n        session={selectedSession}\r\n      />\r\n\r\n      {/* Session Feedback List Dialog */}\r\n      {/* <SessionFeedbackList\r\n        sessionId={selectedSessionForFeedbackList?.id}\r\n        open={openSessionFeedbackList}\r\n        onClose={() => setOpenSessionFeedbackList(false)}\r\n      /> */}\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default SessionList;\r\n"], "mappings": ";;AAAC,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SACEC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,qBAAqB;AAC/F,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;EACxB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC;IAAE8D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM;IAAEsE;EAAE,CAAC,GAAG3C,cAAc,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAE9B,MAAM0C,aAAa,GAAG1E,KAAK,CAAC2E,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMC,GAAG,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,gCAAgC,CAAC;MAC7DpB,WAAW,CAACmB,GAAG,CAACE,IAAI,CAAC;MACrB,MAAMC,QAAQ,GAAG,CAAC,CAAC;MACnB,MAAMC,OAAO,CAACC,GAAG,CACfL,GAAG,CAACE,IAAI,CAACI,GAAG,CAAC,MAAOjB,OAAO,IAAK;QAC9B,IAAI;UACF,MAAMkB,IAAI,GAAG,MAAMrD,KAAK,CAAC+C,GAAG,CAAC,kCAAkCZ,OAAO,CAACmB,EAAE,QAAQ,CAAC;UAClFL,QAAQ,CAACd,OAAO,CAACmB,EAAE,CAAC,GAAGD,IAAI,CAACL,IAAI,IAAI,EAAE;QACxC,CAAC,CAAC,MAAM;UACNC,QAAQ,CAACd,OAAO,CAACmB,EAAE,CAAC,GAAG,EAAE;QAC3B;MACF,CAAC,CACH,CAAC;MACDzB,mBAAmB,CAACoB,QAAQ,CAAC;IAC/B,CAAC,CAAC,MAAM;MACNhD,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACtC;EACF,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC;EAEPvE,SAAS,CAAC,MAAM;IACdyE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMY,YAAY,GAAG,MAAOF,EAAE,IAAK;IACjC,IAAI;MACF,MAAMtD,KAAK,CAACyD,MAAM,CAAC,kCAAkCH,EAAE,EAAE,CAAC;MAC1DrD,KAAK,CAACyD,OAAO,CAAChB,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAC1CE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,MAAM;MACN3C,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,sBAAsB,CAAC,CAAC;IACxC;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,SAAS,KAAK;IACzD,IAAI;MACF,MAAM7D,KAAK,CAAC8D,KAAK,CAAC,kCAAkCF,SAAS,SAAS,EAAE;QACtEG,MAAM,EAAEF;MACV,CAAC,CAAC;MACF5D,KAAK,CAACyD,OAAO,CAAChB,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAC1CE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,MAAM;MACN3C,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,4BAA4B,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAAA,CAAOJ,SAAS,EAAEK,MAAM,KAAK;IACpD,IAAI;MACF,MAAMjE,KAAK,CAACyD,MAAM,CAAC,kCAAkCG,SAAS,gBAAgBK,MAAM,EAAE,CAAC;MACvFhE,KAAK,CAACyD,OAAO,CAAChB,CAAC,CAAC,sBAAsB,CAAC,CAAC;MACxC,MAAME,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOsB,CAAC,EAAE;MAAA,IAAAC,WAAA,EAAAC,gBAAA;MACVnE,KAAK,CAACsD,KAAK,CACT,EAAAY,WAAA,GAAAD,CAAC,CAACG,QAAQ,cAAAF,WAAA,wBAAAC,gBAAA,GAAVD,WAAA,CAAYnB,IAAI,cAAAoB,gBAAA,uBAAhBA,gBAAA,CAAkBE,OAAO,KAAI5B,CAAC,CAAC,0BAA0B,CAC3D,CAAC;IACH;EACF,CAAC;EAED,MAAM6B,aAAa,GAAG,MAAOX,SAAS,IAAK;IACzC,IAAI,CAACtC,SAAS,EAAE;MACdrB,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACrC;IACF;IACAjB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMzB,KAAK,CAACwE,IAAI,CAAC,kCAAkCZ,SAAS,WAAW,EAAE;QACvEa,KAAK,EAAEnD;MACT,CAAC,CAAC;MACFrB,KAAK,CAACyD,OAAO,CAAChB,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtCrB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,YAAY,CAAC,EAAE,CAAC;MAChB,MAAMqB,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOsB,CAAC,EAAE;MAAA,IAAAQ,YAAA,EAAAC,iBAAA;MACV1E,KAAK,CAACsD,KAAK,CACT,EAAAmB,YAAA,GAAAR,CAAC,CAACG,QAAQ,cAAAK,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAY1B,IAAI,cAAA2B,iBAAA,uBAAhBA,iBAAA,CAAkBL,OAAO,KAAI5B,CAAC,CAAC,uBAAuB,CACxD,CAAC;IACH,CAAC,SAAS;MACRjB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmD,mBAAmB,GAAIhB,SAAS,IAAK;IACzC7B,cAAc,CAAC8C,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACjB,SAAS,GAAG,CAACiB,IAAI,CAACjB,SAAS;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkB,WAAW,GAAI3C,OAAO,IAAK;IAAA,IAAA4C,gBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IAC/B,MAAMC,IAAI,GAAG,MAAMzC,CAAC,CAAC,8BAA8B,CAAC,aAAaP,OAAO,CAACiD,IAAI,UAAU1C,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAAqC,gBAAA,GAAA5C,OAAO,CAACkD,OAAO,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBK,IAAI,KAAI1C,CAAC,CAAC,kBAAkB,CAAC,QAAQA,CAAC,CAAC,iBAAiB,CAAC,MAAAsC,kBAAA,GAAK7C,OAAO,CAACmD,SAAS,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,OAAAN,gBAAA,GAAM9C,OAAO,CAACqD,OAAO,cAAAP,gBAAA,uBAAfA,gBAAA,CAAiBM,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,EAAAL,qBAAA,GAAA/C,OAAO,CAACsD,eAAe,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBQ,MAAM,IAAG,CAAC,GAAG,MAAMhD,CAAC,CAAC,0BAA0B,CAAC,KAAK,GAAGP,OAAO,CAACsD,eAAe,CAACrC,GAAG,CAACuC,GAAG;MAAA,IAAAC,WAAA;MAAA,OAAI,MAAAA,WAAA,GAAKD,GAAG,CAACE,MAAM,cAAAD,WAAA,uBAAVA,WAAA,CAAYR,IAAI,EAAE;IAAA,EAAC,CAACU,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,MAAMpD,CAAC,CAAC,4BAA4B,CAAC,UAAUA,CAAC,CAAC,sBAAsB,CAAC,iGAAiG;IACvlBL,YAAY,CAAC8C,IAAI,CAAC;IAClBlD,aAAa,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC;IAAQ,CAAC,CAAC;EACxC,CAAC;EAED,MAAM4D,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,WAAW,GAAGC,kBAAkB,CAAC9D,SAAS,CAAC;IACjD,MAAM+D,IAAI,GAAG;MACXC,QAAQ,EAAE,gDAAgDF,kBAAkB,CAACG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAE;MACpGC,OAAO,EAAE,yCAAyCP,WAAW,EAAE;MAC/DQ,QAAQ,EAAE,uDAAuDP,kBAAkB,CAACG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC3G,CAAC;IACDF,MAAM,CAACnE,IAAI,CAACiE,IAAI,CAACH,QAAQ,CAAC,EAAE,QAAQ,EAAE,sBAAsB,CAAC;EAC/D,CAAC;EAED,MAAMU,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACzE,SAAS,CAAC;MAC9CnC,KAAK,CAACyD,OAAO,CAAChB,CAAC,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOoE,GAAG,EAAE;MACZ7G,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMqE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MAAA,IAAAC,mBAAA;MACF;MACA,MAAMC,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC,aAAa,CAAC,EAAEC,OAAO;MAEzD,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC1D,IAAI,CAACF,OAAO,EAAE;MAEd,MAAMG,MAAM,GAAG,MAAML,WAAW,CAACE,OAAO,CAAC;MACzC,MAAMI,OAAO,GAAGD,MAAM,CAACE,SAAS,CAAC,WAAW,CAAC;MAE7C,MAAMC,IAAI,GAAGL,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAAClB,IAAI,GAAGgB,OAAO;MACnBE,IAAI,CAACE,QAAQ,GAAG,WAAW,EAAAX,mBAAA,GAAAhF,UAAU,CAACG,OAAO,cAAA6E,mBAAA,uBAAlBA,mBAAA,CAAoB5B,IAAI,KAAI,SAAS,MAAM;MACtEqC,IAAI,CAACG,KAAK,CAAC,CAAC;IACd,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdsE,OAAO,CAACtE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CtD,KAAK,CAACsD,KAAK,CAACb,CAAC,CAAC,+BAA+B,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMoF,gBAAgB,GAAI3F,OAAO,IAAK;IACpCM,kBAAkB,CAACN,OAAO,CAAC;IAC3BI,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,oBACElC,OAAA,CAAC/B,KAAK;IAACyJ,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,YAAY,EAAE,CAAC;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAC7E/H,OAAA,CAAChC,UAAU;MAACgK,OAAO,EAAC,IAAI;MAACC,UAAU,EAAC,MAAM;MAACC,YAAY;MAAAH,QAAA,GAAC,eACnD,EAAC1F,CAAC,CAAC,sBAAsB,CAAC;IAAA;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,EAEZjH,QAAQ,CAACgE,MAAM,KAAK,CAAC,gBACpBrF,OAAA,CAAChC,UAAU;MAACuK,EAAE,EAAE,CAAE;MAACC,KAAK,EAAC,gBAAgB;MAAAT,QAAA,EACtC1F,CAAC,CAAC,qBAAqB;IAAC;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,GAEbjH,QAAQ,CAAC0B,GAAG,CAAEjB,OAAO;MAAA,IAAA2G,iBAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,sBAAA;MAAA,oBACnB5I,OAAA,CAAC/B,KAAK;QAEJyJ,SAAS,EAAE,CAAE;QACbC,EAAE,EAAE;UACFY,EAAE,EAAE,CAAC;UACLX,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACfC,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3BC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,YAAY;UACxBC,GAAG,EAAE;QACP,CAAE;QAAAnB,QAAA,gBAGF/H,OAAA,CAAC9B,GAAG;UAACiL,IAAI,EAAE,CAAE;UAAApB,QAAA,GACVjG,OAAO,CAACsH,QAAQ,iBACfpJ,OAAA,CAAC9B,GAAG;YAACmL,EAAE,EAAE,CAAE;YAACP,OAAO,EAAC,MAAM;YAACE,cAAc,EAAC,QAAQ;YAAAjB,QAAA,eAChD/H,OAAA;cACEsJ,GAAG,EAAExH,OAAO,CAACsH,QAAS;cACtBG,GAAG,EAAC,SAAS;cACbC,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,GAAG;gBACd7B,YAAY,EAAE,EAAE;gBAChB8B,SAAS,EAAE;cACb;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDtI,OAAA,CAAC5B,KAAK;YACJwL,SAAS,EAAC,KAAK;YACfZ,cAAc,EAAC,eAAe;YAC9BC,UAAU,EAAC,QAAQ;YACnBI,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAEN/H,OAAA,CAAChC,UAAU;cAACgK,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACO,KAAK,EAAC,SAAS;cAAAT,QAAA,GAAC,eACtD,EAACjG,OAAO,CAACiD,IAAI;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACbtI,OAAA,CAAC5B,KAAK;cAACwL,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACZ,UAAU,EAAC,QAAQ;cAAAlB,QAAA,gBACpD/H,OAAA,CAACvB,IAAI;gBACHqL,KAAK,EAAEhI,OAAO,CAAC4B,MAAO;gBACtB8E,KAAK,EACH1G,OAAO,CAAC4B,MAAM,KAAK,QAAQ,GACvB,SAAS,GACT5B,OAAO,CAAC4B,MAAM,KAAK,UAAU,GAC7B,SAAS,GACT5B,OAAO,CAAC4B,MAAM,KAAK,WAAW,GAC9B,SAAS,GACT,WACL;gBACDiE,EAAE,EAAE;kBAAEM,UAAU,EAAE,GAAG;kBAAE8B,aAAa,EAAE;gBAAa;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACFtI,OAAA,CAACjB,WAAW;gBAACiL,IAAI,EAAC,OAAO;gBAACrC,EAAE,EAAE;kBAAEsC,QAAQ,EAAE;gBAAI,CAAE;gBAAAlC,QAAA,gBAC9C/H,OAAA,CAAChB,UAAU;kBAACiE,EAAE,EAAE,gBAAgBnB,OAAO,CAACmB,EAAE,EAAG;kBAAA8E,QAAA,EAAE1F,CAAC,CAAC,iBAAiB;gBAAC;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACjFtI,OAAA,CAAClB,MAAM;kBACLoL,OAAO,EAAE,gBAAgBpI,OAAO,CAACmB,EAAE,EAAG;kBACtCkH,KAAK,EAAErI,OAAO,CAAC4B,MAAO;kBACtBoG,KAAK,EAAEzH,CAAC,CAAC,iBAAiB,CAAE;kBAC5B+H,QAAQ,EAAEvG,CAAC,IAAIP,kBAAkB,CAACxB,OAAO,CAACmB,EAAE,EAAEY,CAAC,CAACwG,MAAM,CAACF,KAAK,CAAE;kBAAApC,QAAA,gBAE9D/H,OAAA,CAACnB,QAAQ;oBAACsL,KAAK,EAAC,QAAQ;oBAAApC,QAAA,EAAE1F,CAAC,CAAC,iBAAiB;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC1DtI,OAAA,CAACnB,QAAQ;oBAACsL,KAAK,EAAC,UAAU;oBAAApC,QAAA,EAAE1F,CAAC,CAAC,mBAAmB;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9DtI,OAAA,CAACnB,QAAQ;oBAACsL,KAAK,EAAC,WAAW;oBAAApC,QAAA,EAAE1F,CAAC,CAAC,oBAAoB;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChEtI,OAAA,CAACnB,QAAQ;oBAACsL,KAAK,EAAC,UAAU;oBAAApC,QAAA,EAAE1F,CAAC,CAAC,mBAAmB;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEP,CAAC7G,WAAW,CAACK,OAAO,CAACmB,EAAE,CAAC,iBACvBjD,OAAA,CAAC5B,KAAK;YAACwL,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACZ,UAAU,EAAC,QAAQ;YAACI,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAC3D/H,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBQ,KAAK,EAAC,OAAO;cACbwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtK,OAAA,CAACf,UAAU;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BiC,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAACrB,OAAO,CAACmB,EAAE,CAAE;cAAA8E,QAAA,EAEvC1F,CAAC,CAAC,iBAAiB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtK,OAAA,CAACb,gBAAgB;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCiC,OAAO,EAAEA,CAAA,KAAMjI,QAAQ,CAAC,aAAaR,OAAO,CAACmB,EAAE,UAAU,CAAE;cAAA8E,QAAA,EAE1D1F,CAAC,CAAC,eAAe;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtK,OAAA,CAACd,iBAAiB;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACjCiC,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAACzC,OAAO,CAACmB,EAAE,CAAE;cAAA8E,QAAA,EAE9C1F,CAAC,CAAC,kBAAkB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBQ,KAAK,EAAC,WAAW;cACjBwB,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM9F,WAAW,CAAC3C,OAAO,CAAE;cAAAiG,QAAA,GACrC,eACI,EAAC1F,CAAC,CAAC,gBAAgB,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAMjI,QAAQ,CAAC,aAAaR,OAAO,CAACmB,EAAE,eAAe,CAAE;cAAA8E,QAAA,GACjE,eACI,EAAC1F,CAAC,CAAC,8BAA8B,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,MAAM;cACZwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtK,OAAA,CAACP,QAAQ;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBiC,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC3F,OAAO,CAAE;cAAAiG,QAAA,GAC1C,eACI,EAAC1F,CAAC,CAAC,mBAAmB,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR,EAGAvH,aAAa,KAAKe,OAAO,CAACmB,EAAE,iBAC3BjD,OAAA,CAAC9B,GAAG;YAACqK,EAAE,EAAE,CAAE;YAACc,EAAE,EAAE,CAAE;YAACP,OAAO,EAAC,MAAM;YAACI,GAAG,EAAE,CAAE;YAACD,UAAU,EAAC,QAAQ;YAAAlB,QAAA,gBAC3D/H,OAAA,CAACxB,SAAS;cACRgM,IAAI,EAAC,OAAO;cACZC,WAAW,EAAEpI,CAAC,CAAC,+BAA+B,CAAE;cAChD8H,KAAK,EAAElJ,SAAU;cACjBmJ,QAAQ,EAAEvG,CAAC,IAAI3C,YAAY,CAAC2C,CAAC,CAACwG,MAAM,CAACF,KAAK,CAAE;cAC5CH,IAAI,EAAC,OAAO;cACZrC,EAAE,EAAE;gBAAEsC,QAAQ,EAAE;cAAI;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,WAAW;cACnBgC,IAAI,EAAC,OAAO;cACZxB,KAAK,EAAC,WAAW;cACjB+B,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAACpC,OAAO,CAACmB,EAAE,CAAE;cACzCyH,QAAQ,EAAEvJ,UAAW;cAAA4G,QAAA,EAEpB5G,UAAU,GAAGkB,CAAC,CAAC,iBAAiB,CAAC,GAAGA,CAAC,CAAC,cAAc;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACTtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,MAAM;cACdgC,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM;gBAAEvJ,gBAAgB,CAAC,IAAI,CAAC;gBAAEE,YAAY,CAAC,EAAE,CAAC;cAAE,CAAE;cAAA6G,QAAA,EAE5D1F,CAAC,CAAC,iBAAiB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eAEDtI,OAAA,CAAChC,UAAU;YAACgK,OAAO,EAAC,OAAO;YAACqB,EAAE,EAAE,GAAI;YAAAtB,QAAA,GAAC,eAChC,EAAC1F,CAAC,CAAC,kBAAkB,CAAC,EAAC,KAAG,eAAArC,OAAA;cAAA+H,QAAA,EAAS,EAAAU,iBAAA,GAAA3G,OAAO,CAACkD,OAAO,cAAAyD,iBAAA,uBAAfA,iBAAA,CAAiB1D,IAAI,KAAI1C,CAAC,CAAC,kBAAkB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACbtI,OAAA,CAAChC,UAAU;YAACgK,OAAO,EAAC,OAAO;YAACqB,EAAE,EAAE,GAAI;YAAAtB,QAAA,GAAC,eAChC,EAAC1F,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC,eAAArC,OAAA;cAAA+H,QAAA,GAAAW,mBAAA,GAAS5G,OAAO,CAACmD,SAAS,cAAAyD,mBAAA,uBAAjBA,mBAAA,CAAmBxD,KAAK,CAAC,CAAC,EAAE,EAAE;YAAC;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KAAC,EAACjG,CAAC,CAAC,aAAa,CAAC,EAAE,GAAG,eACnGrC,OAAA;cAAA+H,QAAA,GAAAY,iBAAA,GAAS7G,OAAO,CAACqD,OAAO,cAAAwD,iBAAA,uBAAfA,iBAAA,CAAiBzD,KAAK,CAAC,CAAC,EAAE,EAAE;YAAC;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACbtI,OAAA,CAAChC,UAAU;YAACgK,OAAO,EAAC,OAAO;YAACqB,EAAE,EAAE,GAAI;YAAAtB,QAAA,GAAC,SACjC,EAAC1F,CAAC,CAAC,wBAAwB,CAAC,EAAC,KAAG,EAChCP,OAAO,CAAC6I,qBAAqB,KAAK,IAAI,gBAClC3K,OAAA;cAAA+H,QAAA,GAASjG,OAAO,CAAC6I,qBAAqB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAAC9I,OAAO,CAAC+I,aAAa,EAAC,GAAC,EAACxI,CAAC,CAAC,oBAAoB,CAAC,EAAC,GAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBACjHtI,OAAA;cAAMwJ,KAAK,EAAE;gBAAEhB,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAE1F,CAAC,CAAC,sBAAsB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE9D,CAAC,EAGZ,EAAAM,sBAAA,GAAA9G,OAAO,CAACsD,eAAe,cAAAwD,sBAAA,uBAAvBA,sBAAA,CAAyBvD,MAAM,IAAG,CAAC,iBAClCrF,OAAA,CAAAE,SAAA;YAAA6H,QAAA,gBACE/H,OAAA,CAAC7B,OAAO;cAACwJ,EAAE,EAAE;gBAAEmD,EAAE,EAAE;cAAE;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1BtI,OAAA,CAAChC,UAAU;cAACgK,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAAAF,QAAA,GAAC,eAC7C,EAAC1F,CAAC,CAAC,yBAAyB,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,EACZxG,OAAO,CAACsD,eAAe,CAACrC,GAAG,CAAEuC,GAAG;cAAA,IAAAyF,YAAA,EAAAC,YAAA;cAAA,oBAC/BhL,OAAA,CAAC9B,GAAG;gBAAcqK,EAAE,EAAE,CAAE;gBAAAR,QAAA,gBACtB/H,OAAA,CAAChC,UAAU;kBAACiK,UAAU,EAAC,MAAM;kBAACO,KAAK,EAAC,gBAAgB;kBAAAT,QAAA,GAAC,eAChD,GAAAgD,YAAA,GAACzF,GAAG,CAACE,MAAM,cAAAuF,YAAA,uBAAVA,YAAA,CAAYhG,IAAI;gBAAA;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GAAA0C,YAAA,GACZ1F,GAAG,CAAC2F,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAajI,GAAG,CAAEmI,CAAC;kBAAA,IAAAC,SAAA,EAAAC,WAAA;kBAAA,oBAClBpL,OAAA,CAAC9B,GAAG;oBAAYmN,EAAE,EAAE,CAAE;oBAAC9C,EAAE,EAAE,CAAE;oBAAAR,QAAA,gBAC3B/H,OAAA,CAAChC,UAAU;sBAACgK,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAACO,KAAK,EAAC,cAAc;sBAAAT,QAAA,GAAC,eAC9D,GAAAoD,SAAA,GAACD,CAAC,CAACI,MAAM,cAAAH,SAAA,uBAARA,SAAA,CAAUI,KAAK;oBAAA;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACbtI,OAAA,CAAC5B,KAAK;sBAACwL,SAAS,EAAC,KAAK;sBAACC,OAAO,EAAE,CAAE;sBAACtB,EAAE,EAAE,CAAE;sBAACiD,QAAQ,EAAC,MAAM;sBAAAzD,QAAA,GAAAqD,WAAA,GACtDF,CAAC,CAACO,QAAQ,cAAAL,WAAA,uBAAVA,WAAA,CAAYrI,GAAG,CAAE2I,EAAE;wBAAA,IAAAC,YAAA;wBAAA,oBAClB3L,OAAA,CAAC3B,MAAM;0BAEL2J,OAAO,EAAC,UAAU;0BAClBQ,KAAK,EAAC,MAAM;0BACZwB,IAAI,EAAC,OAAO;0BACZrC,EAAE,EAAE;4BAAEiE,EAAE,EAAE,CAAC;4BAAEvC,EAAE,EAAE,CAAC;4BAAExB,YAAY,EAAE;0BAAE,CAAE;0BACtC0C,OAAO,EAAEA,CAAA;4BAAA,IAAAsB,WAAA;4BAAA,OACP,EAAAA,WAAA,GAAAH,EAAE,CAACI,OAAO,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,OAAO,KACnB/F,MAAM,CAACnE,IAAI,CAAC6J,EAAE,CAACI,OAAO,CAACC,OAAO,EAAE,QAAQ,CAAC;0BAAA,CAC1C;0BAAAhE,QAAA,GACF,eACI,GAAA4D,YAAA,GAACD,EAAE,CAACI,OAAO,cAAAH,YAAA,uBAAVA,YAAA,CAAYJ,KAAK;wBAAA,GAVhBG,EAAE,CAACzI,EAAE;0BAAAkF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAWJ,CAAC;sBAAA,CACV;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA,GApBA4C,CAAC,CAACjI,EAAE;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBT,CAAC;gBAAA,CACP,CAAC;cAAA,GA3BMhD,GAAG,CAACrC,EAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BX,CAAC;YAAA,CACP,CAAC;UAAA,eACF,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL7G,WAAW,CAACK,OAAO,CAACmB,EAAE,CAAC,iBACtBjD,OAAA,CAAC/B,KAAK;UACJyJ,SAAS,EAAE,CAAE;UACbC,EAAE,EAAE;YACFsC,QAAQ,EAAE,GAAG;YACbR,QAAQ,EAAE,GAAG;YACbuC,OAAO,EAAE,SAAS;YAClBnE,YAAY,EAAE,CAAC;YACfD,CAAC,EAAE,CAAC;YACJkB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBE,UAAU,EAAE,SAAS;YACrBgD,SAAS,EAAE;UACb,CAAE;UAAAlE,QAAA,gBAEF/H,OAAA,CAAC9B,GAAG;YAAC4K,OAAO,EAAC,MAAM;YAACG,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAACK,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAC3E/H,OAAA,CAAChC,UAAU;cAACiK,UAAU,EAAE,GAAI;cAACO,KAAK,EAAC,SAAS;cAAC0D,QAAQ,EAAE,EAAG;cAAAnE,QAAA,EACvD1F,CAAC,CAAC,kBAAkB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACbtI,OAAA,CAAC3B,MAAM;cACL2J,OAAO,EAAC,MAAM;cACdQ,KAAK,EAAC,SAAS;cACf+B,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAACzC,OAAO,CAACmB,EAAE,CAAE;cAC/C0E,EAAE,EAAE;gBAAEM,UAAU,EAAE,GAAG;gBAAEiE,QAAQ,EAAE,EAAE;gBAAEnC,aAAa,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAE5D1F,CAAC,CAAC,eAAe;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtI,OAAA,CAAC9B,GAAG;YACF4K,OAAO,EAAC,MAAM;YACdG,UAAU,EAAC,QAAQ;YACnBC,GAAG,EAAE,CAAE;YACPG,EAAE,EAAE,CAAE;YACN2C,OAAO,EAAC,SAAS;YACjBnE,YAAY,EAAE,CAAE;YAChBD,CAAC,EAAE,GAAI;YAAAG,QAAA,gBAEP/H,OAAA,CAACxB,SAAS;cACRwL,IAAI,EAAC,OAAO;cACZQ,IAAI,EAAC,OAAO;cACZC,WAAW,EAAEpI,CAAC,CAAC,qBAAqB,CAAE;cACtC8H,KAAK,EAAEpJ,aAAa,KAAKe,OAAO,CAACmB,EAAE,GAAGhC,SAAS,GAAG,EAAG;cACrDkL,OAAO,EAAEA,CAAA,KAAMnL,gBAAgB,CAACc,OAAO,CAACmB,EAAE,CAAE;cAC5CmH,QAAQ,EAAGvG,CAAC,IAAK3C,YAAY,CAAC2C,CAAC,CAACwG,MAAM,CAACF,KAAK,CAAE;cAC9CnC,OAAO,EAAC,UAAU;cAClBL,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAE6C,OAAO,EAAE,MAAM;gBAAEnE,YAAY,EAAE;cAAE;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACFtI,OAAA,CAACzB,UAAU;cACTiK,KAAK,EAAC,SAAS;cACfkC,QAAQ,EAAEvJ,UAAW;cACrBoJ,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAACpC,OAAO,CAACmB,EAAE,CAAE;cACzC0E,EAAE,EAAE;gBAAEqE,OAAO,EAAE,SAAS;gBAAExD,KAAK,EAAE,MAAM;gBAAE,SAAS,EAAE;kBAAEwD,OAAO,EAAE;gBAAU;cAAE,CAAE;cAAAjE,QAAA,eAE7E/H,OAAA,CAACd,iBAAiB;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNtI,OAAA,CAAC5B,KAAK;YAACyL,OAAO,EAAE,CAAE;YAAA9B,QAAA,EACf,CAACxG,gBAAgB,CAACO,OAAO,CAACmB,EAAE,CAAC,IAAI,EAAE,EAAEoC,MAAM,KAAK,CAAC,gBAChDrF,OAAA,CAAChC,UAAU;cAACwK,KAAK,EAAC,gBAAgB;cAAC0D,QAAQ,EAAE,EAAG;cAAAnE,QAAA,EAC7C1F,CAAC,CAAC,kBAAkB;YAAC;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,GAEb/G,gBAAgB,CAACO,OAAO,CAACmB,EAAE,CAAC,CAACF,GAAG,CAAEqJ,IAAI,iBACpCpM,OAAA,CAAC9B,GAAG;cAEF4K,OAAO,EAAC,MAAM;cACdG,UAAU,EAAC,QAAQ;cACnBC,GAAG,EAAE,CAAE;cACPtB,CAAC,EAAE,CAAE;cACLoE,OAAO,EAAC,MAAM;cACdnE,YAAY,EAAE,CAAE;cAChBF,EAAE,EAAE;gBACFsE,SAAS,EAAE,gCAAgC;gBAC3CI,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,iBAAiB;gBAC7B,SAAS,EAAE;kBAAEC,UAAU,EAAE;gBAAU;cACrC,CAAE;cACFhC,OAAO,EAAEA,CAAA,KAAMjI,QAAQ,CAAC,gBAAgB8J,IAAI,CAACnJ,EAAE,EAAE,CAAE;cAAA8E,QAAA,gBAEnD/H,OAAA,CAAC1B,MAAM;gBACLgL,GAAG,EAAE8C,IAAI,CAACI,UAAU,IAAIC,SAAU;gBAClC9E,EAAE,EAAE;kBACF+E,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAE1E,UAAU,EAAE,GAAG;kBAAEiE,QAAQ,EAAE,EAAE;kBACpDF,OAAO,EAAEI,IAAI,CAACI,UAAU,GAAG,aAAa,GAAG;gBAC7C,CAAE;gBAAAzE,QAAA,EAED,CAACqE,IAAI,CAACI,UAAU,IAAIJ,IAAI,CAACrH,IAAI,GAAGqH,IAAI,CAACrH,IAAI,CAAC,CAAC,CAAC,CAAC6H,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACTtI,OAAA,CAAC9B,GAAG;gBAACyJ,EAAE,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAApB,QAAA,gBACnB/H,OAAA,CAAChC,UAAU;kBAACiK,UAAU,EAAE,GAAI;kBAACiE,QAAQ,EAAE,EAAG;kBAAC1D,KAAK,EAAC,MAAM;kBAAAT,QAAA,EACpDqE,IAAI,CAACrH;gBAAI;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACbtI,OAAA,CAAChC,UAAU;kBAACkO,QAAQ,EAAE,EAAG;kBAAC1D,KAAK,EAAC,MAAM;kBAAAT,QAAA,EACnCqE,IAAI,CAACS;gBAAI;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNtI,OAAA,CAACzB,UAAU;gBACTyL,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,OAAO;gBACb+B,OAAO,EAAE1G,CAAC,IAAI;kBACZA,CAAC,CAACiJ,eAAe,CAAC,CAAC;kBACnBnJ,gBAAgB,CAAC7B,OAAO,CAACmB,EAAE,EAAEmJ,IAAI,CAACnJ,EAAE,CAAC;gBACvC,CAAE;gBAAA8E,QAAA,eAEF/H,OAAA,CAACf,UAAU;kBAACiN,QAAQ,EAAC;gBAAO;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA,GAzCR8D,IAAI,CAACnJ,EAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0CT,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR;MAAA,GA5UIxG,OAAO,CAACmB,EAAE;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6UV,CAAC;IAAA,CACT,CACF,eAGDtI,OAAA,CAACtB,MAAM;MAACmD,IAAI,EAAEF,UAAU,CAACE,IAAK;MAACkL,OAAO,EAAEA,CAAA,KAAMnL,aAAa,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAE;MAAC2H,QAAQ,EAAC,IAAI;MAACuD,SAAS;MAAAjF,QAAA,gBAClH/H,OAAA,CAACrB,WAAW;QAAAoJ,QAAA,GAAC,eACR,EAAC1F,CAAC,CAAC,uBAAuB,CAAC,eAC9BrC,OAAA,CAACzB,UAAU;UAACgM,OAAO,EAAEA,CAAA,KAAM3I,aAAa,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAE;UAAC6F,EAAE,EAAE;YAAEsF,QAAQ,EAAE,UAAU;YAAEC,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,eACvH/H,OAAA,CAACZ,KAAK;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdtI,OAAA,CAACpB,aAAa;QAAAmJ,QAAA,gBAEZ/H,OAAA,CAAC9B,GAAG;UACF+E,EAAE,EAAC,iBAAiB;UACpB0E,EAAE,EAAE;YACFE,YAAY,EAAE,CAAC;YACfuF,QAAQ,EAAE,QAAQ;YAClBpB,OAAO,EAAE,SAAS;YAClBnD,MAAM,EAAE,mBAAmB;YAC3BoD,SAAS,EAAE,CAAC;YACZ5C,EAAE,EAAE,CAAC;YACLI,QAAQ,EAAE,GAAG;YACb4D,EAAE,EAAE;UACN,CAAE;UAAAtF,QAAA,gBAEF/H,OAAA,CAAC9B,GAAG;YACFyJ,EAAE,EAAE;cACF4E,UAAU,EAAE,0CAA0C;cACtD/D,KAAK,EAAE,MAAM;cACbZ,CAAC,EAAE,CAAC;cACJ0F,SAAS,EAAE;YACb,CAAE;YAAAvF,QAAA,gBAEF/H,OAAA,CAAChC,UAAU;cAACgK,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAF,QAAA,GAAC,eACtC,GAAA1H,oBAAA,GAACsB,UAAU,CAACG,OAAO,cAAAzB,oBAAA,uBAAlBA,oBAAA,CAAoB0E,IAAI;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACbtI,OAAA,CAAChC,UAAU;cAACgK,OAAO,EAAC,WAAW;cAAAD,QAAA,GAAC,eAC3B,EAAC1F,CAAC,CAAC,yBAAyB,CAAC;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAEL,EAAAhI,oBAAA,GAAAqB,UAAU,CAACG,OAAO,cAAAxB,oBAAA,uBAAlBA,oBAAA,CAAoB8I,QAAQ,kBAC3BpJ,OAAA,CAAC9B,GAAG;YACFyJ,EAAE,EAAE;cACFmB,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBlB,eAAe,EAAE,SAAS;cAC1BF,CAAC,EAAE;YACL,CAAE;YAAAG,QAAA,eAEF/H,OAAA;cACEsJ,GAAG,EAAE3H,UAAU,CAACG,OAAO,CAACsH,QAAS;cACjCG,GAAG,EAAC,SAAS;cACbC,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,GAAG;gBACd7B,YAAY,EAAE,EAAE;gBAChB8B,SAAS,EAAE,OAAO;gBAClBsC,SAAS,EAAE;cACb;YAAE;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDtI,OAAA,CAAC9B,GAAG;YAACyJ,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAG,QAAA,gBAChB/H,OAAA,CAAChC,UAAU;cAACkO,QAAQ,EAAE,EAAG;cAAC7C,EAAE,EAAE,CAAE;cAAAtB,QAAA,GAAC,eAC5B,eAAA/H,OAAA;gBAAA+H,QAAA,EAAS1F,CAAC,CAAC,kBAAkB;cAAC;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,OAAG,GAAA/H,oBAAA,GAACoB,UAAU,CAACG,OAAO,cAAAvB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoByE,OAAO,cAAAxE,qBAAA,uBAA3BA,qBAAA,CAA6BuE,IAAI;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACbtI,OAAA,CAAChC,UAAU;cAACkO,QAAQ,EAAE,EAAG;cAAC7C,EAAE,EAAE,CAAE;cAAAtB,QAAA,GAAC,eAC5B,eAAA/H,OAAA;gBAAA+H,QAAA,EAAS1F,CAAC,CAAC,iBAAiB;cAAC;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,EAAC,GAAG,GAAA7H,oBAAA,GAC/CkB,UAAU,CAACG,OAAO,cAAArB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBwE,SAAS,cAAAvE,qBAAA,uBAA7BA,qBAAA,CAA+BwE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,UAAG,GAAAvE,oBAAA,GAACgB,UAAU,CAACG,OAAO,cAAAnB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBwE,OAAO,cAAAvE,qBAAA,uBAA3BA,qBAAA,CAA6BsE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EAEZ,EAAAzH,oBAAA,GAAAc,UAAU,CAACG,OAAO,cAAAjB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBuE,eAAe,cAAAtE,qBAAA,uBAAnCA,qBAAA,CAAqCuE,MAAM,IAAG,CAAC,iBAC9CrF,OAAA,CAAAE,SAAA;cAAA6H,QAAA,gBACE/H,OAAA,CAAChC,UAAU;gBAACiK,UAAU,EAAC,MAAM;gBAACiE,QAAQ,EAAE,EAAG;gBAAC7C,EAAE,EAAE,CAAE;gBAAAtB,QAAA,GAAC,eAC9C,EAAC1F,CAAC,CAAC,yBAAyB,CAAC;cAAA;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACbtI,OAAA;gBAAIwJ,KAAK,EAAE;kBAAE+D,WAAW,EAAE;gBAAG,CAAE;gBAAAxF,QAAA,EAC5BpG,UAAU,CAACG,OAAO,CAACsD,eAAe,CAACrC,GAAG,CAAEuC,GAAG;kBAAA,IAAAkI,YAAA;kBAAA,oBAC1CxN,OAAA;oBAAA+H,QAAA,eACE/H,OAAA,CAAChC,UAAU;sBAACkO,QAAQ,EAAE,EAAG;sBAAAnE,QAAA,GAAC,SAAE,GAAAyF,YAAA,GAAClI,GAAG,CAACE,MAAM,cAAAgI,YAAA,uBAAVA,YAAA,CAAYzI,IAAI;oBAAA;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC,GADpDhD,GAAG,CAACrC,EAAE;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,eACL,CACH,eAEDtI,OAAA,CAAChC,UAAU;cAACkO,QAAQ,EAAE,EAAG;cAAC3D,EAAE,EAAE,CAAE;cAACC,KAAK,EAAC,gBAAgB;cAAAT,QAAA,EAAC;YAExD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtI,OAAA,CAACxB,SAAS;UACRiP,SAAS;UACTC,IAAI,EAAE,CAAE;UACRV,SAAS;UACT7C,KAAK,EAAEpI,SAAU;UACjBqI,QAAQ,EAAGvG,CAAC,IAAK7B,YAAY,CAAC6B,CAAC,CAACwG,MAAM,CAACF,KAAK,CAAE;UAC9CnC,OAAO,EAAC,UAAU;UAClB8B,KAAK,EAAE,MAAMzH,CAAC,CAAC,wBAAwB,CAAC,EAAG;UAC3CsF,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEFtI,OAAA,CAAC5B,KAAK;UAACwL,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAC2B,QAAQ,EAAC,MAAM;UAACtC,GAAG,EAAE,CAAE;UAACG,EAAE,EAAE,CAAE;UAAAtB,QAAA,gBAC/D/H,OAAA,CAAC3B,MAAM;YAAC2J,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtK,OAAA,CAACX,QAAQ;cAAA8I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAAC,UAAU,CAAE;YAACiC,EAAE,EAAE;cAAEqE,OAAO,EAAE;YAAU,CAAE;YAAAjE,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChJtI,OAAA,CAAC3B,MAAM;YAAC2J,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtK,OAAA,CAACV,OAAO;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAAC,SAAS,CAAE;YAACiC,EAAE,EAAE;cAAEqE,OAAO,EAAE;YAAU,CAAE;YAAAjE,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7ItI,OAAA,CAAC3B,MAAM;YAAC2J,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtK,OAAA,CAACT,QAAQ;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAAC,UAAU,CAAE;YAACiC,EAAE,EAAE;cAAEqE,OAAO,EAAE;YAAU,CAAE;YAAAjE,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChJtI,OAAA,CAAC3B,MAAM;YAAC2J,OAAO,EAAC,UAAU;YAACsC,SAAS,eAAEtK,OAAA,CAACR,WAAW;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAElE,cAAe;YAAA0B,QAAA,GAAC,eAAG,EAAC1F,CAAC,CAAC,mBAAmB,CAAC;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACpHtI,OAAA,CAAC3B,MAAM;YAAC2J,OAAO,EAAC,UAAU;YAACuC,OAAO,EAAE7D,qBAAsB;YAAAqB,QAAA,GAAC,eAAG,EAAC1F,CAAC,CAAC,0BAA0B,CAAC;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtI,OAAA,CAACF,kBAAkB;MACjB+B,IAAI,EAAEI,kBAAmB;MACzB8K,OAAO,EAAEA,CAAA,KAAM7K,qBAAqB,CAAC,KAAK,CAAE;MAC5CJ,OAAO,EAAEK;IAAgB;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQG,CAAC;AAEZ,CAAC;AAAClI,EAAA,CAznBID,WAAW;EAAA,QAWDT,cAAc,EACXG,WAAW;AAAA;AAAA8N,EAAA,GAZxBxN,WAAW;AA2nBjB,eAAeA,WAAW;AAAC,IAAAwN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}