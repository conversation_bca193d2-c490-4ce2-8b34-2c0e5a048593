{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Grid, Card, CardContent, Typography, Stack, Box, Divider, Avatar, ToggleButton, ToggleButtonGroup, CircularProgress, Paper } from \"@mui/material\";\nimport GroupIcon from \"@mui/icons-material/Group\";\nimport SchoolIcon from \"@mui/icons-material/School\";\nimport EventAvailableIcon from \"@mui/icons-material/EventAvailable\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport EmojiEventsIcon from \"@mui/icons-material/EmojiEvents\";\nimport ShowChartIcon from \"@mui/icons-material/ShowChart\";\nimport PieChartOutlineIcon from \"@mui/icons-material/PieChartOutline\";\nimport BarChartIcon from \"@mui/icons-material/BarChart\";\nimport axios from \"axios\";\nimport { <PERSON><PERSON>hart, Bar, PieChart, Pie, Cell, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer, LabelList } from \"recharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = [\"#1976d2\", \"#43a047\", \"#fbc02d\", \"#8e24aa\", \"#e53935\", \"#00bcd4\", \"#ffa726\"];\nconst API_BASE = process.env.REACT_APP_API_URL || \"http://localhost:8000\";\nconst toPieData = (obj, labelMap = {}) => Object.keys(obj || {}).filter(key => key !== \"total\").map((key, idx) => ({\n  name: labelMap[key] || key.charAt(0).toUpperCase() + key.slice(1),\n  value: obj[key],\n  color: COLORS[idx % COLORS.length]\n}));\nexport default function AdminDashboard() {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [topSessions, setTopSessions] = useState([]);\n  const [topFormateurs, setTopFormateurs] = useState([]);\n  const [monthlyRegs, setMonthlyRegs] = useState([]);\n  const [sessionStatusStats, setSessionStatusStats] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [chartTypeSessions, setChartTypeSessions] = useState(\"bar\");\n  const [chartTypeRegs, setChartTypeRegs] = useState(\"bar\");\n  const sessionPieData = toPieData(sessionStatusStats, {\n    active: \"Active\",\n    inactive: \"Inactive\",\n    completed: \"Terminée\",\n    archived: \"Archivée\"\n  });\n  useEffect(() => {\n    setLoading(true);\n    Promise.all([axios.get(`${API_BASE}/dashboard/stats`), axios.get(`${API_BASE}/dashboard/top-sessions`), axios.get(`${API_BASE}/dashboard/top-formateurs`), axios.get(`${API_BASE}/dashboard/monthly-registrations`), axios.get(`${API_BASE}/dashboard/session-status-stats`)]).then(([statsRes, sessionsRes, formateursRes, monthlyRegsRes, sessionStatusRes]) => {\n      setStats(statsRes.data);\n      setTopSessions(sessionsRes.data);\n      setTopFormateurs(formateursRes.data);\n      setMonthlyRegs(monthlyRegsRes.data);\n      setSessionStatusStats(sessionStatusRes.data);\n    }).finally(() => setLoading(false));\n  }, []);\n  if (loading || !stats) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      minHeight: \"80vh\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 64\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n  const sumMonthly = key => monthlyRegs.reduce((acc, cur) => acc + (cur[key] || 0), 0);\n  const monthlyPieData = [{\n    name: \"Étudiants\",\n    value: sumMonthly(\"students\"),\n    color: COLORS[0]\n  }, {\n    name: \"Formateurs\",\n    value: sumMonthly(\"formateurs\"),\n    color: COLORS[1]\n  }, {\n    name: \"Créateurs\",\n    value: sumMonthly(\"creators\"),\n    color: COLORS[2]\n  }, {\n    name: \"Établissements\",\n    value: sumMonthly(\"establishments\"),\n    color: COLORS[3]\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: \"100%\",\n      minHeight: \"100vh\",\n      bgcolor: \"#fafbfc\",\n      py: {\n        xs: 3,\n        md: 6\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: \"100%\",\n        maxWidth: 1280,\n        mx: \"auto\",\n        px: {\n          xs: 1.5,\n          md: 4\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        fontWeight: 800,\n        letterSpacing: 1,\n        color: \"#1976d2\",\n        mb: 1,\n        sx: {\n          textTransform: \"capitalize\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          role: \"img\",\n          \"aria-label\": \"dashboard\",\n          children: \"\\uD83E\\uDDD1\\u200D\\uD83D\\uDCBB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), \" Tableau de bord Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.secondary\",\n        fontSize: 17,\n        mb: 3,\n        children: \"Vue d'ensemble des performances et statistiques de la plateforme\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(GroupIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 21\n            }, this),\n            value: stats.totalUsers,\n            label: \"Total Utilisateurs\",\n            color: \"#1976d2\",\n            extra: /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 3,\n              mt: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"success.main\",\n                children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                  children: stats.activeUsers || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), \" actifs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"error.main\",\n                children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                  children: stats.inactiveUsers || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), \" inactifs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 21\n            }, this),\n            value: stats.totalPrograms,\n            label: \"Programmes\",\n            color: \"#7c43bd\",\n            extra: /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 3,\n              mt: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#7c43bd\",\n                children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                  children: stats.totalProgramsPublished\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), \" publi\\xE9s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                  children: stats.totalProgramsUnpublished\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this), \" brouillons\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(EventAvailableIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 21\n            }, this),\n            value: stats.activeSessions || 0,\n            label: \"Sessions Actives\",\n            color: \"#2e7d32\",\n            extra: /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1.5,\n              mt: 1,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: \"#e53935\",\n                  fontWeight: 700\n                },\n                children: [\"\\uD83D\\uDD34 \", stats.inactiveSessions || 0, \" inactives\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: \"#8e24aa\",\n                  fontWeight: 700\n                },\n                children: [\"\\u2705 \", stats.completedSessions || 0, \" termin\\xE9es\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: \"#757575\",\n                  fontWeight: 700\n                },\n                children: [\"\\uD83D\\uDCE6 \", stats.archivedSessions || 0, \" arch.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            icon: /*#__PURE__*/_jsxDEV(ShowChartIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 21\n            }, this),\n            value: stats.activityRate || \"100%\",\n            label: \"Taux d'Activit\\xE9\",\n            color: \"#fa5a33\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 4,\n          maxWidth: 500,\n          mx: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(EmojiEventsIcon, {\n          sx: {\n            color: \"#FBC02D\"\n          },\n          fontSize: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: \"2rem\",\n              minHeight: 200,\n              boxShadow: \"0 4px 20px rgba(25,118,210,0.07)\",\n              background: \"linear-gradient(90deg, #f6faff 70%, #e3eefe 100%)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: 800,\n                mb: 1.5,\n                children: [/*#__PURE__*/_jsxDEV(GroupIcon, {\n                  sx: {\n                    mr: .5,\n                    color: \"#1976d2\"\n                  },\n                  fontSize: \"medium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), \"R\\xE9partition Utilisateurs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(UserMini, {\n                  label: \"\\xC9tudiants\",\n                  color: \"#1976d2\",\n                  value: stats.totalStudents\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(UserMini, {\n                  label: \"Formateurs\",\n                  color: \"#43a047\",\n                  value: stats.totalFormateurs\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(UserMini, {\n                  label: \"Cr\\xE9ateurs\",\n                  color: \"#fbc02d\",\n                  value: stats.totalCreators\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(UserMini, {\n                  label: \"\\xC9tablissements\",\n                  color: \"#8e24aa\",\n                  value: stats.totalEstablishments\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: \"2rem\",\n              minHeight: 200,\n              boxShadow: \"0 4px 20px rgba(251,192,45,0.11)\",\n              background: \"linear-gradient(90deg, #fff8e1 60%, #ffe082 100%)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: 800,\n                mb: 1.5,\n                children: [/*#__PURE__*/_jsxDEV(EmojiEventsIcon, {\n                  color: \"warning\",\n                  fontSize: \"medium\",\n                  sx: {\n                    mr: .5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), \"Top 3 Sessions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 2,\n                mt: 2,\n                children: topSessions.map((s, idx) => /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    p: 2,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    borderRadius: 2,\n                    background: idx === 0 ? \"linear-gradient(90deg, #fff8e1 60%, #ffe082 100%)\" : \"#f5f5f5\",\n                    borderLeft: idx === 0 ? \"6px solid #FBC02D\" : idx === 1 ? \"6px solid #B0BEC5\" : \"6px solid #FF7043\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 36,\n                      height: 36,\n                      borderRadius: \"50%\",\n                      bgcolor: idx === 0 ? \"#FBC02D\" : idx === 1 ? \"#B0BEC5\" : \"#FF7043\",\n                      color: \"#fff\",\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"center\",\n                      fontWeight: 900,\n                      fontSize: 18,\n                      mr: 2,\n                      boxShadow: idx === 0 ? \"0 0 12px #FBC02D77\" : undefined\n                    },\n                    children: idx === 0 ? \"🥇\" : idx === 1 ? \"🥈\" : \"🥉\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    flex: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      fontWeight: 800,\n                      children: s.sessionName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: s.programName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: [s.enrolledUsers, \" inscrits\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this)]\n                }, s.sessionId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: \"2rem\",\n              minHeight: 200,\n              boxShadow: \"0 4px 20px rgba(33,150,243,0.09)\",\n              background: \"linear-gradient(90deg, #f7f1fa 70%, #ede6f8 100%)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: 800,\n                mb: 1.5,\n                children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n                  color: \"primary\",\n                  fontSize: \"medium\",\n                  sx: {\n                    mr: .5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), \"Top 3 Formateurs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 2,\n                mt: 2,\n                children: topFormateurs.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: \"Bient\\xF4t disponible\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this) : topFormateurs.map((f, idx) => /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: f.profilePic,\n                    alt: f.name,\n                    sx: {\n                      width: 36,\n                      height: 36,\n                      mr: 1,\n                      bgcolor: idx === 0 ? \"#FBC02D\" : idx === 1 ? \"#B0BEC5\" : \"#FF7043\",\n                      border: idx === 0 ? \"2px solid #FBC02D\" : idx === 1 ? \"2px solid #B0BEC5\" : \"2px solid #FF7043\",\n                      boxShadow: idx === 0 ? \"0 0 10px #FBC02D88\" : undefined\n                    },\n                    children: !f.profilePic && f.name ? f.name[0].toUpperCase() : null\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      fontWeight: 800,\n                      children: f.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: f.averageRating ? `⭐ ${f.averageRating}` : \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this)]\n                }, f.formateurId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 4,\n          maxWidth: 500,\n          mx: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(PieChartOutlineIcon, {\n          sx: {\n            color: \"#1976D2\"\n          },\n          fontSize: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: \"2rem\",\n              p: 2,\n              boxShadow: \"0 6px 32px rgba(25,118,210,0.09)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                mb: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: 900,\n                  variant: \"subtitle1\",\n                  children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n                    color: \"primary\",\n                    fontSize: \"medium\",\n                    sx: {\n                      mr: .5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), \"Statut des Sessions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n                  value: chartTypeSessions,\n                  exclusive: true,\n                  size: \"small\",\n                  onChange: (_, value) => value && setChartTypeSessions(value),\n                  sx: {\n                    background: \"#f3f6fb\",\n                    borderRadius: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n                    value: \"bar\",\n                    \"aria-label\": \"Bar\",\n                    children: /*#__PURE__*/_jsxDEV(BarChartIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                    value: \"pie\",\n                    \"aria-label\": \"Pie\",\n                    children: /*#__PURE__*/_jsxDEV(PieChartOutlineIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                height: 230,\n                children: chartTypeSessions === \"bar\" ? /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                  width: \"100%\",\n                  height: \"100%\",\n                  children: /*#__PURE__*/_jsxDEV(BarChart, {\n                    data: sessionPieData,\n                    children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                      dataKey: \"name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                      allowDecimals: false\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                      dataKey: \"value\",\n                      radius: 12,\n                      children: [sessionPieData.map((entry, idx) => /*#__PURE__*/_jsxDEV(Cell, {\n                        fill: entry.color\n                      }, `cell-sess-bar-${idx}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 420,\n                        columnNumber: 29\n                      }, this)), /*#__PURE__*/_jsxDEV(LabelList, {\n                        dataKey: \"value\",\n                        position: \"top\",\n                        fontWeight: 900\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                  width: \"100%\",\n                  height: \"100%\",\n                  children: /*#__PURE__*/_jsxDEV(PieChart, {\n                    children: [/*#__PURE__*/_jsxDEV(Pie, {\n                      data: sessionPieData,\n                      cx: \"50%\",\n                      cy: \"50%\",\n                      innerRadius: 50,\n                      outerRadius: 90,\n                      fill: \"#8884d8\",\n                      paddingAngle: 3,\n                      dataKey: \"value\",\n                      label: true,\n                      children: sessionPieData.map((entry, idx) => /*#__PURE__*/_jsxDEV(Cell, {\n                        fill: entry.color\n                      }, `cell-sess-pie-${idx}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              borderRadius: \"2rem\",\n              p: 2,\n              boxShadow: \"0 6px 32px rgba(33,150,243,0.09)\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                mb: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: 900,\n                  variant: \"subtitle1\",\n                  children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n                    color: \"primary\",\n                    fontSize: \"medium\",\n                    sx: {\n                      mr: .5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this), \"Inscriptions (12 derniers mois)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n                  value: chartTypeRegs,\n                  exclusive: true,\n                  size: \"small\",\n                  onChange: (_, value) => value && setChartTypeRegs(value),\n                  sx: {\n                    background: \"#f3f6fb\",\n                    borderRadius: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n                    value: \"bar\",\n                    \"aria-label\": \"Bar\",\n                    children: /*#__PURE__*/_jsxDEV(BarChartIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n                    value: \"pie\",\n                    \"aria-label\": \"Pie\",\n                    children: /*#__PURE__*/_jsxDEV(PieChartOutlineIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                height: 230,\n                children: chartTypeRegs === \"bar\" ? /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                  width: \"100%\",\n                  height: \"100%\",\n                  children: /*#__PURE__*/_jsxDEV(BarChart, {\n                    data: monthlyRegs,\n                    children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                      dataKey: \"month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                      allowDecimals: false\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                      dataKey: \"students\",\n                      stackId: \"a\",\n                      fill: COLORS[0],\n                      name: \"\\xC9tudiants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                      dataKey: \"formateurs\",\n                      stackId: \"a\",\n                      fill: COLORS[1],\n                      name: \"Formateurs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                      dataKey: \"creators\",\n                      stackId: \"a\",\n                      fill: COLORS[2],\n                      name: \"Cr\\xE9ateurs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                      dataKey: \"establishments\",\n                      stackId: \"a\",\n                      fill: COLORS[3],\n                      name: \"\\xC9tablissements\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                  width: \"100%\",\n                  height: \"100%\",\n                  children: /*#__PURE__*/_jsxDEV(PieChart, {\n                    children: [/*#__PURE__*/_jsxDEV(Pie, {\n                      data: monthlyPieData,\n                      cx: \"50%\",\n                      cy: \"50%\",\n                      innerRadius: 50,\n                      outerRadius: 90,\n                      fill: \"#8884d8\",\n                      paddingAngle: 3,\n                      dataKey: \"value\",\n                      label: true,\n                      children: monthlyPieData.map((entry, idx) => /*#__PURE__*/_jsxDEV(Cell, {\n                        fill: entry.color\n                      }, `cell-month-pie-${idx}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n}\n\n// --- StatCard Component ---\n_s(AdminDashboard, \"88J0538+xo8TdY05lx1GhyfEivs=\");\n_c = AdminDashboard;\nfunction StatCard({\n  icon,\n  value,\n  label,\n  color,\n  extra\n}) {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: `linear-gradient(135deg, ${color}18 60%, #fff 100%)`,\n      boxShadow: `0 6px 32px 0 ${color}18`,\n      borderRadius: \"2rem\",\n      p: 3,\n      minHeight: 140,\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all .18s\",\n      \"&:hover\": {\n        transform: \"translateY(-4px) scale(1.03)\",\n        boxShadow: `0 12px 38px 0 ${color}30`\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        fontWeight: 800,\n        color: color,\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: color,\n          width: 56,\n          height: 56\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      mt: 2,\n      color: color,\n      fontWeight: 700,\n      fontSize: 17,\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), extra]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 532,\n    columnNumber: 5\n  }, this);\n}\n\n// --- UserMini Stat for repartition ---\n_c2 = StatCard;\nfunction UserMini({\n  label,\n  color,\n  value\n}) {\n  return /*#__PURE__*/_jsxDEV(Stack, {\n    direction: \"row\",\n    alignItems: \"center\",\n    spacing: 2,\n    children: [/*#__PURE__*/_jsxDEV(Avatar, {\n      sx: {\n        width: 32,\n        height: 32,\n        bgcolor: color\n      },\n      children: label[0]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      flex: 1,\n      fontWeight: 600,\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      fontWeight: 900,\n      color: color,\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 569,\n    columnNumber: 5\n  }, this);\n}\n_c3 = UserMini;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AdminDashboard\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"UserMini\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Divider", "Avatar", "ToggleButton", "ToggleButtonGroup", "CircularProgress", "Paper", "GroupIcon", "SchoolIcon", "EventAvailableIcon", "StarIcon", "EmojiEventsIcon", "ShowChartIcon", "PieChartOutlineIcon", "BarChartIcon", "axios", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "LabelList", "jsxDEV", "_jsxDEV", "COLORS", "API_BASE", "process", "env", "REACT_APP_API_URL", "toPieData", "obj", "labelMap", "Object", "keys", "filter", "key", "map", "idx", "name", "char<PERSON>t", "toUpperCase", "slice", "value", "color", "length", "AdminDashboard", "_s", "stats", "setStats", "topSessions", "setTopSessions", "topFormateurs", "setTopFormateurs", "monthlyRegs", "setMonthlyRegs", "sessionStatusStats", "setSessionStatusStats", "loading", "setLoading", "chartTypeSessions", "setChartTypeSessions", "chartTypeRegs", "setChartTypeRegs", "session<PERSON>ieData", "active", "inactive", "completed", "archived", "Promise", "all", "get", "then", "statsRes", "sessionsRes", "formateursRes", "monthlyRegsRes", "sessionStatusRes", "data", "finally", "minHeight", "display", "alignItems", "justifyContent", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sumMonthly", "reduce", "acc", "cur", "monthlyPieData", "sx", "width", "bgcolor", "py", "xs", "md", "max<PERSON><PERSON><PERSON>", "mx", "px", "variant", "fontWeight", "letterSpacing", "mb", "textTransform", "role", "fontSize", "container", "spacing", "item", "sm", "StatCard", "icon", "totalUsers", "label", "extra", "direction", "mt", "activeUsers", "inactiveUsers", "totalPrograms", "totalProgramsPublished", "totalProgramsUnpublished", "activeSessions", "inactiveSessions", "completedSessions", "archivedSessions", "activityRate", "my", "borderRadius", "boxShadow", "background", "mr", "UserMini", "totalStudents", "totalFormateurs", "totalCreators", "totalEstablishments", "s", "p", "borderLeft", "height", "undefined", "flex", "<PERSON><PERSON><PERSON>", "programName", "enrolledUsers", "sessionId", "f", "src", "profilePic", "alt", "border", "averageRating", "formateurId", "exclusive", "onChange", "_", "dataKey", "allowDecimals", "radius", "entry", "fill", "position", "cx", "cy", "innerRadius", "outerRadius", "paddingAngle", "stackId", "_c", "flexDirection", "transition", "transform", "_c2", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/AdminDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  CardContent,\r\n  Typo<PERSON>,\r\n  <PERSON>ack,\r\n  Box,\r\n  Divider,\r\n  Avatar,\r\n  ToggleButton,\r\n  ToggleButtonGroup,\r\n  CircularProgress,\r\n  Paper,\r\n} from \"@mui/material\";\r\nimport GroupIcon from \"@mui/icons-material/Group\";\r\nimport SchoolIcon from \"@mui/icons-material/School\";\r\nimport EventAvailableIcon from \"@mui/icons-material/EventAvailable\";\r\nimport StarIcon from \"@mui/icons-material/Star\";\r\nimport EmojiEventsIcon from \"@mui/icons-material/EmojiEvents\";\r\nimport ShowChartIcon from \"@mui/icons-material/ShowChart\";\r\nimport PieChartOutlineIcon from \"@mui/icons-material/PieChartOutline\";\r\nimport BarChartIcon from \"@mui/icons-material/BarChart\";\r\nimport axios from \"axios\";\r\nimport {\r\n  BarChart,\r\n  Bar,\r\n  PieChart,\r\n  Pie,\r\n  Cell,\r\n  XAxis,\r\n  YA<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  Legend,\r\n  ResponsiveContainer,\r\n  LabelList,\r\n} from \"recharts\";\r\n\r\nconst COLORS = [\"#1976d2\", \"#43a047\", \"#fbc02d\", \"#8e24aa\", \"#e53935\", \"#00bcd4\", \"#ffa726\"];\r\nconst API_BASE = process.env.REACT_APP_API_URL || \"http://localhost:8000\";\r\n\r\nconst toPieData = (obj, labelMap = {}) =>\r\n  Object.keys(obj || {})\r\n    .filter((key) => key !== \"total\")\r\n    .map((key, idx) => ({\r\n      name: labelMap[key] || key.charAt(0).toUpperCase() + key.slice(1),\r\n      value: obj[key],\r\n      color: COLORS[idx % COLORS.length],\r\n    }));\r\n\r\nexport default function AdminDashboard() {\r\n  const [stats, setStats] = useState(null);\r\n  const [topSessions, setTopSessions] = useState([]);\r\n  const [topFormateurs, setTopFormateurs] = useState([]);\r\n  const [monthlyRegs, setMonthlyRegs] = useState([]);\r\n  const [sessionStatusStats, setSessionStatusStats] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [chartTypeSessions, setChartTypeSessions] = useState(\"bar\");\r\n  const [chartTypeRegs, setChartTypeRegs] = useState(\"bar\");\r\n\r\n  const sessionPieData = toPieData(sessionStatusStats, {\r\n    active: \"Active\",\r\n    inactive: \"Inactive\",\r\n    completed: \"Terminée\",\r\n    archived: \"Archivée\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    Promise.all([\r\n      axios.get(`${API_BASE}/dashboard/stats`),\r\n      axios.get(`${API_BASE}/dashboard/top-sessions`),\r\n      axios.get(`${API_BASE}/dashboard/top-formateurs`),\r\n      axios.get(`${API_BASE}/dashboard/monthly-registrations`),\r\n      axios.get(`${API_BASE}/dashboard/session-status-stats`)\r\n    ]).then(\r\n      ([\r\n        statsRes,\r\n        sessionsRes,\r\n        formateursRes,\r\n        monthlyRegsRes,\r\n        sessionStatusRes\r\n      ]) => {\r\n        setStats(statsRes.data);\r\n        setTopSessions(sessionsRes.data);\r\n        setTopFormateurs(formateursRes.data);\r\n        setMonthlyRegs(monthlyRegsRes.data);\r\n        setSessionStatusStats(sessionStatusRes.data);\r\n      }\r\n    ).finally(() => setLoading(false));\r\n  }, []);\r\n\r\n  if (loading || !stats) {\r\n    return (\r\n      <Box minHeight=\"80vh\" display=\"flex\" alignItems=\"center\" justifyContent=\"center\">\r\n        <CircularProgress size={64} />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  const sumMonthly = (key) =>\r\n    monthlyRegs.reduce((acc, cur) => acc + (cur[key] || 0), 0);\r\n  const monthlyPieData = [\r\n    { name: \"Étudiants\", value: sumMonthly(\"students\"), color: COLORS[0] },\r\n    { name: \"Formateurs\", value: sumMonthly(\"formateurs\"), color: COLORS[1] },\r\n    { name: \"Créateurs\", value: sumMonthly(\"creators\"), color: COLORS[2] },\r\n    { name: \"Établissements\", value: sumMonthly(\"establishments\"), color: COLORS[3] }\r\n  ];\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        width: \"100%\",\r\n        minHeight: \"100vh\",\r\n        bgcolor: \"#fafbfc\",\r\n        py: { xs: 3, md: 6 },\r\n      }}\r\n    >\r\n      <Box\r\n        sx={{\r\n          width: \"100%\",\r\n          maxWidth: 1280,\r\n          mx: \"auto\",\r\n          px: { xs: 1.5, md: 4 },\r\n        }}\r\n      >\r\n        {/* HEADER */}\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={800}\r\n          letterSpacing={1}\r\n          color=\"#1976d2\"\r\n          mb={1}\r\n          sx={{ textTransform: \"capitalize\" }}\r\n        >\r\n          <span role=\"img\" aria-label=\"dashboard\">🧑‍💻</span> Tableau de bord Admin\r\n        </Typography>\r\n        <Typography color=\"text.secondary\" fontSize={17} mb={3}>\r\n          Vue d'ensemble des performances et statistiques de la plateforme\r\n        </Typography>\r\n\r\n        {/* STAT CARDS */}\r\n        <Grid container spacing={3} mb={3}>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              icon={<GroupIcon fontSize=\"large\" />}\r\n              value={stats.totalUsers}\r\n              label=\"Total Utilisateurs\"\r\n              color=\"#1976d2\"\r\n              extra={\r\n                <Stack direction=\"row\" spacing={3} mt={1}>\r\n                  <Typography variant=\"caption\" color=\"success.main\">\r\n                    <b>{stats.activeUsers || 0}</b> actifs\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"error.main\">\r\n                    <b>{stats.inactiveUsers || 0}</b> inactifs\r\n                  </Typography>\r\n                </Stack>\r\n              }\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              icon={<SchoolIcon fontSize=\"large\" />}\r\n              value={stats.totalPrograms}\r\n              label=\"Programmes\"\r\n              color=\"#7c43bd\"\r\n              extra={\r\n                <Stack direction=\"row\" spacing={3} mt={1}>\r\n                  <Typography variant=\"caption\" color=\"#7c43bd\">\r\n                    <b>{stats.totalProgramsPublished}</b> publiés\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    <b>{stats.totalProgramsUnpublished}</b> brouillons\r\n                  </Typography>\r\n                </Stack>\r\n              }\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              icon={<EventAvailableIcon fontSize=\"large\" />}\r\n              value={stats.activeSessions || 0}\r\n              label=\"Sessions Actives\"\r\n              color=\"#2e7d32\"\r\n              extra={\r\n                <Stack direction=\"row\" spacing={1.5} mt={1} alignItems=\"center\">\r\n                  <Typography variant=\"caption\" sx={{ color: \"#e53935\", fontWeight: 700 }}>\r\n                    🔴 {stats.inactiveSessions || 0} inactives\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" sx={{ color: \"#8e24aa\", fontWeight: 700 }}>\r\n                    ✅ {stats.completedSessions || 0} terminées\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" sx={{ color: \"#757575\", fontWeight: 700 }}>\r\n                    📦 {stats.archivedSessions || 0} arch.\r\n                  </Typography>\r\n                </Stack>\r\n              }\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              icon={<ShowChartIcon fontSize=\"large\" />}\r\n              value={stats.activityRate || \"100%\"}\r\n              label=\"Taux d'Activité\"\r\n              color=\"#fa5a33\"\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* ELITE / TOP 3 */}\r\n        <Divider sx={{ my: 4, maxWidth: 500, mx: \"auto\" }}>\r\n          <EmojiEventsIcon sx={{ color: \"#FBC02D\" }} fontSize=\"large\" />\r\n        </Divider>\r\n        <Grid container spacing={3} mb={3}>\r\n          {/* User repartition */}\r\n          <Grid item xs={12} md={4}>\r\n            <Card sx={{\r\n              borderRadius: \"2rem\",\r\n              minHeight: 200,\r\n              boxShadow: \"0 4px 20px rgba(25,118,210,0.07)\",\r\n              background: \"linear-gradient(90deg, #f6faff 70%, #e3eefe 100%)\"\r\n            }}>\r\n              <CardContent>\r\n                <Typography variant=\"subtitle1\" fontWeight={800} mb={1.5}>\r\n                  <GroupIcon sx={{ mr: .5, color: \"#1976d2\" }} fontSize=\"medium\" />\r\n                  Répartition Utilisateurs\r\n                </Typography>\r\n                <Stack spacing={2}>\r\n                  <UserMini label=\"Étudiants\" color=\"#1976d2\" value={stats.totalStudents} />\r\n                  <UserMini label=\"Formateurs\" color=\"#43a047\" value={stats.totalFormateurs} />\r\n                  <UserMini label=\"Créateurs\" color=\"#fbc02d\" value={stats.totalCreators} />\r\n                  <UserMini label=\"Établissements\" color=\"#8e24aa\" value={stats.totalEstablishments} />\r\n                </Stack>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          {/* Top 3 Sessions */}\r\n          <Grid item xs={12} md={4}>\r\n            <Card sx={{\r\n              borderRadius: \"2rem\",\r\n              minHeight: 200,\r\n              boxShadow: \"0 4px 20px rgba(251,192,45,0.11)\",\r\n              background: \"linear-gradient(90deg, #fff8e1 60%, #ffe082 100%)\"\r\n            }}>\r\n              <CardContent>\r\n                <Typography variant=\"subtitle1\" fontWeight={800} mb={1.5}>\r\n                  <EmojiEventsIcon color=\"warning\" fontSize=\"medium\" sx={{ mr: .5 }} />\r\n                  Top 3 Sessions\r\n                </Typography>\r\n                <Stack spacing={2} mt={2}>\r\n                  {topSessions.map((s, idx) => (\r\n                    <Paper\r\n                      key={s.sessionId}\r\n                      sx={{\r\n                        p: 2,\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        borderRadius: 2,\r\n                        background: idx === 0\r\n                          ? \"linear-gradient(90deg, #fff8e1 60%, #ffe082 100%)\"\r\n                          : \"#f5f5f5\",\r\n                        borderLeft: idx === 0\r\n                          ? \"6px solid #FBC02D\"\r\n                          : idx === 1\r\n                            ? \"6px solid #B0BEC5\"\r\n                            : \"6px solid #FF7043\"\r\n                      }}\r\n                    >\r\n                      <Box\r\n                        sx={{\r\n                          width: 36,\r\n                          height: 36,\r\n                          borderRadius: \"50%\",\r\n                          bgcolor:\r\n                            idx === 0\r\n                              ? \"#FBC02D\"\r\n                              : idx === 1\r\n                                ? \"#B0BEC5\"\r\n                                : \"#FF7043\",\r\n                          color: \"#fff\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          justifyContent: \"center\",\r\n                          fontWeight: 900,\r\n                          fontSize: 18,\r\n                          mr: 2,\r\n                          boxShadow:\r\n                            idx === 0\r\n                              ? \"0 0 12px #FBC02D77\"\r\n                              : undefined,\r\n                        }}\r\n                      >\r\n                        {idx === 0 ? \"🥇\" : idx === 1 ? \"🥈\" : \"🥉\"}\r\n                      </Box>\r\n                      <Box flex={1}>\r\n                        <Typography fontWeight={800}>{s.sessionName}</Typography>\r\n                        <Typography variant=\"caption\" color=\"text.secondary\">\r\n                          {s.programName}\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          {s.enrolledUsers} inscrits\r\n                        </Typography>\r\n                      </Box>\r\n                    </Paper>\r\n                  ))}\r\n                </Stack>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          {/* Top 3 Formateurs */}\r\n          <Grid item xs={12} md={4}>\r\n            <Card sx={{\r\n              borderRadius: \"2rem\",\r\n              minHeight: 200,\r\n              boxShadow: \"0 4px 20px rgba(33,150,243,0.09)\",\r\n              background: \"linear-gradient(90deg, #f7f1fa 70%, #ede6f8 100%)\"\r\n            }}>\r\n              <CardContent>\r\n                <Typography variant=\"subtitle1\" fontWeight={800} mb={1.5}>\r\n                  <StarIcon color=\"primary\" fontSize=\"medium\" sx={{ mr: .5 }} />\r\n                  Top 3 Formateurs\r\n                </Typography>\r\n                <Stack spacing={2} mt={2}>\r\n                  {topFormateurs.length === 0 ? (\r\n                    <Typography color=\"text.secondary\">Bientôt disponible</Typography>\r\n                  ) : (\r\n                    topFormateurs.map((f, idx) => (\r\n                      <Stack\r\n                        direction=\"row\"\r\n                        alignItems=\"center\"\r\n                        spacing={2}\r\n                        key={f.formateurId}\r\n                      >\r\n                        <Avatar\r\n                          src={f.profilePic}\r\n                          alt={f.name}\r\n                          sx={{\r\n                            width: 36,\r\n                            height: 36,\r\n                            mr: 1,\r\n                            bgcolor:\r\n                              idx === 0\r\n                                ? \"#FBC02D\"\r\n                                : idx === 1\r\n                                  ? \"#B0BEC5\"\r\n                                  : \"#FF7043\",\r\n                            border:\r\n                              idx === 0\r\n                                ? \"2px solid #FBC02D\"\r\n                                : idx === 1\r\n                                  ? \"2px solid #B0BEC5\"\r\n                                  : \"2px solid #FF7043\",\r\n                            boxShadow:\r\n                              idx === 0\r\n                                ? \"0 0 10px #FBC02D88\"\r\n                                : undefined,\r\n                          }}\r\n                        >\r\n                          {!f.profilePic && f.name ? f.name[0].toUpperCase() : null}\r\n                        </Avatar>\r\n                        <Box>\r\n                          <Typography fontWeight={800}>{f.name}</Typography>\r\n                          <Typography variant=\"body2\" color=\"text.secondary\">\r\n                            {f.averageRating ? `⭐ ${f.averageRating}` : \"\"}\r\n                          </Typography>\r\n                        </Box>\r\n                      </Stack>\r\n                    ))\r\n                  )}\r\n                </Stack>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* CHARTS / GRAPHS */}\r\n        <Divider sx={{ my: 4, maxWidth: 500, mx: \"auto\" }}>\r\n          <PieChartOutlineIcon sx={{ color: \"#1976D2\" }} fontSize=\"large\" />\r\n        </Divider>\r\n        <Grid container spacing={3}>\r\n          {/* Sessions Status Chart */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card sx={{\r\n              borderRadius: \"2rem\",\r\n              p: 2,\r\n              boxShadow: \"0 6px 32px rgba(25,118,210,0.09)\"\r\n            }}>\r\n              <CardContent>\r\n                <Stack direction=\"row\" alignItems=\"center\" justifyContent=\"space-between\" mb={1}>\r\n                  <Typography fontWeight={900} variant=\"subtitle1\">\r\n                    <ShowChartIcon color=\"primary\" fontSize=\"medium\" sx={{ mr: .5 }} />\r\n                    Statut des Sessions\r\n                  </Typography>\r\n                  <ToggleButtonGroup\r\n                    value={chartTypeSessions}\r\n                    exclusive\r\n                    size=\"small\"\r\n                    onChange={(_, value) => value && setChartTypeSessions(value)}\r\n                    sx={{ background: \"#f3f6fb\", borderRadius: 2 }}\r\n                  >\r\n                    <ToggleButton value=\"bar\" aria-label=\"Bar\">\r\n                      <BarChartIcon fontSize=\"small\" />\r\n                    </ToggleButton>\r\n                    <ToggleButton value=\"pie\" aria-label=\"Pie\">\r\n                      <PieChartOutlineIcon fontSize=\"small\" />\r\n                    </ToggleButton>\r\n                  </ToggleButtonGroup>\r\n                </Stack>\r\n                <Divider sx={{ mb: 2 }} />\r\n                <Box height={230}>\r\n                  {chartTypeSessions === \"bar\" ? (\r\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                      <BarChart data={sessionPieData}>\r\n                        <XAxis dataKey=\"name\" />\r\n                        <YAxis allowDecimals={false} />\r\n                        <Tooltip />\r\n                        <Bar dataKey=\"value\" radius={12}>\r\n                          {sessionPieData.map((entry, idx) => (\r\n                            <Cell key={`cell-sess-bar-${idx}`} fill={entry.color} />\r\n                          ))}\r\n                          <LabelList dataKey=\"value\" position=\"top\" fontWeight={900} />\r\n                        </Bar>\r\n                      </BarChart>\r\n                    </ResponsiveContainer>\r\n                  ) : (\r\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                      <PieChart>\r\n                        <Pie\r\n                          data={sessionPieData}\r\n                          cx=\"50%\"\r\n                          cy=\"50%\"\r\n                          innerRadius={50}\r\n                          outerRadius={90}\r\n                          fill=\"#8884d8\"\r\n                          paddingAngle={3}\r\n                          dataKey=\"value\"\r\n                          label\r\n                        >\r\n                          {sessionPieData.map((entry, idx) => (\r\n                            <Cell key={`cell-sess-pie-${idx}`} fill={entry.color} />\r\n                          ))}\r\n                        </Pie>\r\n                        <Legend />\r\n                        <Tooltip />\r\n                      </PieChart>\r\n                    </ResponsiveContainer>\r\n                  )}\r\n                </Box>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          {/* Monthly Registrations Chart */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card sx={{\r\n              borderRadius: \"2rem\",\r\n              p: 2,\r\n              boxShadow: \"0 6px 32px rgba(33,150,243,0.09)\"\r\n            }}>\r\n              <CardContent>\r\n                <Stack direction=\"row\" alignItems=\"center\" justifyContent=\"space-between\" mb={1}>\r\n                  <Typography fontWeight={900} variant=\"subtitle1\">\r\n                    <ShowChartIcon color=\"primary\" fontSize=\"medium\" sx={{ mr: .5 }} />\r\n                    Inscriptions (12 derniers mois)\r\n                  </Typography>\r\n                  <ToggleButtonGroup\r\n                    value={chartTypeRegs}\r\n                    exclusive\r\n                    size=\"small\"\r\n                    onChange={(_, value) => value && setChartTypeRegs(value)}\r\n                    sx={{ background: \"#f3f6fb\", borderRadius: 2 }}\r\n                  >\r\n                    <ToggleButton value=\"bar\" aria-label=\"Bar\">\r\n                      <BarChartIcon fontSize=\"small\" />\r\n                    </ToggleButton>\r\n                    <ToggleButton value=\"pie\" aria-label=\"Pie\">\r\n                      <PieChartOutlineIcon fontSize=\"small\" />\r\n                    </ToggleButton>\r\n                  </ToggleButtonGroup>\r\n                </Stack>\r\n                <Divider sx={{ mb: 2 }} />\r\n                <Box height={230}>\r\n                  {chartTypeRegs === \"bar\" ? (\r\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                      <BarChart data={monthlyRegs}>\r\n                        <XAxis dataKey=\"month\" />\r\n                        <YAxis allowDecimals={false} />\r\n                        <Tooltip />\r\n                        <Legend />\r\n                        <Bar dataKey=\"students\" stackId=\"a\" fill={COLORS[0]} name=\"Étudiants\" />\r\n                        <Bar dataKey=\"formateurs\" stackId=\"a\" fill={COLORS[1]} name=\"Formateurs\" />\r\n                        <Bar dataKey=\"creators\" stackId=\"a\" fill={COLORS[2]} name=\"Créateurs\" />\r\n                        <Bar dataKey=\"establishments\" stackId=\"a\" fill={COLORS[3]} name=\"Établissements\" />\r\n                      </BarChart>\r\n                    </ResponsiveContainer>\r\n                  ) : (\r\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                      <PieChart>\r\n                        <Pie\r\n                          data={monthlyPieData}\r\n                          cx=\"50%\"\r\n                          cy=\"50%\"\r\n                          innerRadius={50}\r\n                          outerRadius={90}\r\n                          fill=\"#8884d8\"\r\n                          paddingAngle={3}\r\n                          dataKey=\"value\"\r\n                          label\r\n                        >\r\n                          {monthlyPieData.map((entry, idx) => (\r\n                            <Cell key={`cell-month-pie-${idx}`} fill={entry.color} />\r\n                          ))}\r\n                        </Pie>\r\n                        <Legend />\r\n                        <Tooltip />\r\n                      </PieChart>\r\n                    </ResponsiveContainer>\r\n                  )}\r\n                </Box>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        </Grid>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\n// --- StatCard Component ---\r\nfunction StatCard({ icon, value, label, color, extra }) {\r\n  return (\r\n    <Box\r\n      sx={{\r\n        background: `linear-gradient(135deg, ${color}18 60%, #fff 100%)`,\r\n        boxShadow: `0 6px 32px 0 ${color}18`,\r\n        borderRadius: \"2rem\",\r\n        p: 3,\r\n        minHeight: 140,\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"center\",\r\n        transition: \"all .18s\",\r\n        \"&:hover\": {\r\n          transform: \"translateY(-4px) scale(1.03)\",\r\n          boxShadow: `0 12px 38px 0 ${color}30`,\r\n        },\r\n      }}\r\n    >\r\n      <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n        <Typography variant=\"h3\" fontWeight={800} color={color}>\r\n          {value}\r\n        </Typography>\r\n        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>\r\n          {icon}\r\n        </Avatar>\r\n      </Stack>\r\n      <Typography mt={2} color={color} fontWeight={700} fontSize={17}>\r\n        {label}\r\n      </Typography>\r\n      {extra}\r\n    </Box>\r\n  );\r\n}\r\n\r\n// --- UserMini Stat for repartition ---\r\nfunction UserMini({ label, color, value }) {\r\n  return (\r\n    <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n      <Avatar sx={{ width: 32, height: 32, bgcolor: color }}>{label[0]}</Avatar>\r\n      <Typography flex={1} fontWeight={600}>{label}</Typography>\r\n      <Typography fontWeight={900} color={color}>{value}</Typography>\r\n    </Stack>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,QACJ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAC5F,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAEzE,MAAMC,SAAS,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAG,CAAC,CAAC,KACnCC,MAAM,CAACC,IAAI,CAACH,GAAG,IAAI,CAAC,CAAC,CAAC,CACnBI,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,OAAO,CAAC,CAChCC,GAAG,CAAC,CAACD,GAAG,EAAEE,GAAG,MAAM;EAClBC,IAAI,EAAEP,QAAQ,CAACI,GAAG,CAAC,IAAIA,GAAG,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGL,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC;EACjEC,KAAK,EAAEZ,GAAG,CAACK,GAAG,CAAC;EACfQ,KAAK,EAAEnB,MAAM,CAACa,GAAG,GAAGb,MAAM,CAACoB,MAAM;AACnC,CAAC,CAAC,CAAC;AAEP,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM0E,cAAc,GAAGlC,SAAS,CAAC0B,kBAAkB,EAAE;IACnDS,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF/E,SAAS,CAAC,MAAM;IACdsE,UAAU,CAAC,IAAI,CAAC;IAChBU,OAAO,CAACC,GAAG,CAAC,CACV3D,KAAK,CAAC4D,GAAG,CAAC,GAAG7C,QAAQ,kBAAkB,CAAC,EACxCf,KAAK,CAAC4D,GAAG,CAAC,GAAG7C,QAAQ,yBAAyB,CAAC,EAC/Cf,KAAK,CAAC4D,GAAG,CAAC,GAAG7C,QAAQ,2BAA2B,CAAC,EACjDf,KAAK,CAAC4D,GAAG,CAAC,GAAG7C,QAAQ,kCAAkC,CAAC,EACxDf,KAAK,CAAC4D,GAAG,CAAC,GAAG7C,QAAQ,iCAAiC,CAAC,CACxD,CAAC,CAAC8C,IAAI,CACL,CAAC,CACCC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,gBAAgB,CACjB,KAAK;MACJ5B,QAAQ,CAACwB,QAAQ,CAACK,IAAI,CAAC;MACvB3B,cAAc,CAACuB,WAAW,CAACI,IAAI,CAAC;MAChCzB,gBAAgB,CAACsB,aAAa,CAACG,IAAI,CAAC;MACpCvB,cAAc,CAACqB,cAAc,CAACE,IAAI,CAAC;MACnCrB,qBAAqB,CAACoB,gBAAgB,CAACC,IAAI,CAAC;IAC9C,CACF,CAAC,CAACC,OAAO,CAAC,MAAMpB,UAAU,CAAC,KAAK,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,IAAI,CAACV,KAAK,EAAE;IACrB,oBACExB,OAAA,CAAC5B,GAAG;MAACoF,SAAS,EAAC,MAAM;MAACC,OAAO,EAAC,MAAM;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,QAAQ;MAAAC,QAAA,eAC9E5D,OAAA,CAACvB,gBAAgB;QAACoF,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,MAAMC,UAAU,GAAItD,GAAG,IACrBkB,WAAW,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACzD,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5D,MAAM0D,cAAc,GAAG,CACrB;IAAEvD,IAAI,EAAE,WAAW;IAAEI,KAAK,EAAE+C,UAAU,CAAC,UAAU,CAAC;IAAE9C,KAAK,EAAEnB,MAAM,CAAC,CAAC;EAAE,CAAC,EACtE;IAAEc,IAAI,EAAE,YAAY;IAAEI,KAAK,EAAE+C,UAAU,CAAC,YAAY,CAAC;IAAE9C,KAAK,EAAEnB,MAAM,CAAC,CAAC;EAAE,CAAC,EACzE;IAAEc,IAAI,EAAE,WAAW;IAAEI,KAAK,EAAE+C,UAAU,CAAC,UAAU,CAAC;IAAE9C,KAAK,EAAEnB,MAAM,CAAC,CAAC;EAAE,CAAC,EACtE;IAAEc,IAAI,EAAE,gBAAgB;IAAEI,KAAK,EAAE+C,UAAU,CAAC,gBAAgB,CAAC;IAAE9C,KAAK,EAAEnB,MAAM,CAAC,CAAC;EAAE,CAAC,CAClF;EAED,oBACED,OAAA,CAAC5B,GAAG;IACFmG,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbhB,SAAS,EAAE,OAAO;MAClBiB,OAAO,EAAE,SAAS;MAClBC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IACrB,CAAE;IAAAhB,QAAA,eAEF5D,OAAA,CAAC5B,GAAG;MACFmG,EAAE,EAAE;QACFC,KAAK,EAAE,MAAM;QACbK,QAAQ,EAAE,IAAI;QACdC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;UAAEJ,EAAE,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAE;MACvB,CAAE;MAAAhB,QAAA,gBAGF5D,OAAA,CAAC9B,UAAU;QACT8G,OAAO,EAAC,IAAI;QACZC,UAAU,EAAE,GAAI;QAChBC,aAAa,EAAE,CAAE;QACjB9D,KAAK,EAAC,SAAS;QACf+D,EAAE,EAAE,CAAE;QACNZ,EAAE,EAAE;UAAEa,aAAa,EAAE;QAAa,CAAE;QAAAxB,QAAA,gBAEpC5D,OAAA;UAAMqF,IAAI,EAAC,KAAK;UAAC,cAAW,WAAW;UAAAzB,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,0BACtD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC9B,UAAU;QAACkD,KAAK,EAAC,gBAAgB;QAACkE,QAAQ,EAAE,EAAG;QAACH,EAAE,EAAE,CAAE;QAAAvB,QAAA,EAAC;MAExD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbjE,OAAA,CAACjC,IAAI;QAACwH,SAAS;QAACC,OAAO,EAAE,CAAE;QAACL,EAAE,EAAE,CAAE;QAAAvB,QAAA,gBAChC5D,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5D,OAAA,CAAC2F,QAAQ;YACPC,IAAI,eAAE5F,OAAA,CAACrB,SAAS;cAAC2G,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrC9C,KAAK,EAAEK,KAAK,CAACqE,UAAW;YACxBC,KAAK,EAAC,oBAAoB;YAC1B1E,KAAK,EAAC,SAAS;YACf2E,KAAK,eACH/F,OAAA,CAAC7B,KAAK;cAAC6H,SAAS,EAAC,KAAK;cAACR,OAAO,EAAE,CAAE;cAACS,EAAE,EAAE,CAAE;cAAArC,QAAA,gBACvC5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAAC5D,KAAK,EAAC,cAAc;gBAAAwC,QAAA,gBAChD5D,OAAA;kBAAA4D,QAAA,EAAIpC,KAAK,CAAC0E,WAAW,IAAI;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,WACjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAAC5D,KAAK,EAAC,YAAY;gBAAAwC,QAAA,gBAC9C5D,OAAA;kBAAA4D,QAAA,EAAIpC,KAAK,CAAC2E,aAAa,IAAI;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,aACnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5D,OAAA,CAAC2F,QAAQ;YACPC,IAAI,eAAE5F,OAAA,CAACpB,UAAU;cAAC0G,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtC9C,KAAK,EAAEK,KAAK,CAAC4E,aAAc;YAC3BN,KAAK,EAAC,YAAY;YAClB1E,KAAK,EAAC,SAAS;YACf2E,KAAK,eACH/F,OAAA,CAAC7B,KAAK;cAAC6H,SAAS,EAAC,KAAK;cAACR,OAAO,EAAE,CAAE;cAACS,EAAE,EAAE,CAAE;cAAArC,QAAA,gBACvC5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAAC5D,KAAK,EAAC,SAAS;gBAAAwC,QAAA,gBAC3C5D,OAAA;kBAAA4D,QAAA,EAAIpC,KAAK,CAAC6E;gBAAsB;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAAC5D,KAAK,EAAC,gBAAgB;gBAAAwC,QAAA,gBAClD5D,OAAA;kBAAA4D,QAAA,EAAIpC,KAAK,CAAC8E;gBAAwB;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5D,OAAA,CAAC2F,QAAQ;YACPC,IAAI,eAAE5F,OAAA,CAACnB,kBAAkB;cAACyG,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9C9C,KAAK,EAAEK,KAAK,CAAC+E,cAAc,IAAI,CAAE;YACjCT,KAAK,EAAC,kBAAkB;YACxB1E,KAAK,EAAC,SAAS;YACf2E,KAAK,eACH/F,OAAA,CAAC7B,KAAK;cAAC6H,SAAS,EAAC,KAAK;cAACR,OAAO,EAAE,GAAI;cAACS,EAAE,EAAE,CAAE;cAACvC,UAAU,EAAC,QAAQ;cAAAE,QAAA,gBAC7D5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAACT,EAAE,EAAE;kBAAEnD,KAAK,EAAE,SAAS;kBAAE6D,UAAU,EAAE;gBAAI,CAAE;gBAAArB,QAAA,GAAC,eACpE,EAACpC,KAAK,CAACgF,gBAAgB,IAAI,CAAC,EAAC,YAClC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAACT,EAAE,EAAE;kBAAEnD,KAAK,EAAE,SAAS;kBAAE6D,UAAU,EAAE;gBAAI,CAAE;gBAAArB,QAAA,GAAC,SACrE,EAACpC,KAAK,CAACiF,iBAAiB,IAAI,CAAC,EAAC,eAClC;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAACT,EAAE,EAAE;kBAAEnD,KAAK,EAAE,SAAS;kBAAE6D,UAAU,EAAE;gBAAI,CAAE;gBAAArB,QAAA,GAAC,eACpE,EAACpC,KAAK,CAACkF,gBAAgB,IAAI,CAAC,EAAC,QAClC;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9B5D,OAAA,CAAC2F,QAAQ;YACPC,IAAI,eAAE5F,OAAA,CAAChB,aAAa;cAACsG,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzC9C,KAAK,EAAEK,KAAK,CAACmF,YAAY,IAAI,MAAO;YACpCb,KAAK,EAAC,oBAAiB;YACvB1E,KAAK,EAAC;UAAS;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPjE,OAAA,CAAC3B,OAAO;QAACkG,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAE/B,QAAQ,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAO,CAAE;QAAAlB,QAAA,eAChD5D,OAAA,CAACjB,eAAe;UAACwF,EAAE,EAAE;YAAEnD,KAAK,EAAE;UAAU,CAAE;UAACkE,QAAQ,EAAC;QAAO;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACVjE,OAAA,CAACjC,IAAI;QAACwH,SAAS;QAACC,OAAO,EAAE,CAAE;QAACL,EAAE,EAAE,CAAE;QAAAvB,QAAA,gBAEhC5D,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5D,OAAA,CAAChC,IAAI;YAACuG,EAAE,EAAE;cACRsC,YAAY,EAAE,MAAM;cACpBrD,SAAS,EAAE,GAAG;cACdsD,SAAS,EAAE,kCAAkC;cAC7CC,UAAU,EAAE;YACd,CAAE;YAAAnD,QAAA,eACA5D,OAAA,CAAC/B,WAAW;cAAA2F,QAAA,gBACV5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,WAAW;gBAACC,UAAU,EAAE,GAAI;gBAACE,EAAE,EAAE,GAAI;gBAAAvB,QAAA,gBACvD5D,OAAA,CAACrB,SAAS;kBAAC4F,EAAE,EAAE;oBAAEyC,EAAE,EAAE,EAAE;oBAAE5F,KAAK,EAAE;kBAAU,CAAE;kBAACkE,QAAQ,EAAC;gBAAQ;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BAEnE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7B,KAAK;gBAACqH,OAAO,EAAE,CAAE;gBAAA5B,QAAA,gBAChB5D,OAAA,CAACiH,QAAQ;kBAACnB,KAAK,EAAC,cAAW;kBAAC1E,KAAK,EAAC,SAAS;kBAACD,KAAK,EAAEK,KAAK,CAAC0F;gBAAc;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1EjE,OAAA,CAACiH,QAAQ;kBAACnB,KAAK,EAAC,YAAY;kBAAC1E,KAAK,EAAC,SAAS;kBAACD,KAAK,EAAEK,KAAK,CAAC2F;gBAAgB;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7EjE,OAAA,CAACiH,QAAQ;kBAACnB,KAAK,EAAC,cAAW;kBAAC1E,KAAK,EAAC,SAAS;kBAACD,KAAK,EAAEK,KAAK,CAAC4F;gBAAc;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1EjE,OAAA,CAACiH,QAAQ;kBAACnB,KAAK,EAAC,mBAAgB;kBAAC1E,KAAK,EAAC,SAAS;kBAACD,KAAK,EAAEK,KAAK,CAAC6F;gBAAoB;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5D,OAAA,CAAChC,IAAI;YAACuG,EAAE,EAAE;cACRsC,YAAY,EAAE,MAAM;cACpBrD,SAAS,EAAE,GAAG;cACdsD,SAAS,EAAE,kCAAkC;cAC7CC,UAAU,EAAE;YACd,CAAE;YAAAnD,QAAA,eACA5D,OAAA,CAAC/B,WAAW;cAAA2F,QAAA,gBACV5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,WAAW;gBAACC,UAAU,EAAE,GAAI;gBAACE,EAAE,EAAE,GAAI;gBAAAvB,QAAA,gBACvD5D,OAAA,CAACjB,eAAe;kBAACqC,KAAK,EAAC,SAAS;kBAACkE,QAAQ,EAAC,QAAQ;kBAACf,EAAE,EAAE;oBAAEyC,EAAE,EAAE;kBAAG;gBAAE;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAEvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7B,KAAK;gBAACqH,OAAO,EAAE,CAAE;gBAACS,EAAE,EAAE,CAAE;gBAAArC,QAAA,EACtBlC,WAAW,CAACb,GAAG,CAAC,CAACyG,CAAC,EAAExG,GAAG,kBACtBd,OAAA,CAACtB,KAAK;kBAEJ6F,EAAE,EAAE;oBACFgD,CAAC,EAAE,CAAC;oBACJ9D,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBmD,YAAY,EAAE,CAAC;oBACfE,UAAU,EAAEjG,GAAG,KAAK,CAAC,GACjB,mDAAmD,GACnD,SAAS;oBACb0G,UAAU,EAAE1G,GAAG,KAAK,CAAC,GACjB,mBAAmB,GACnBA,GAAG,KAAK,CAAC,GACP,mBAAmB,GACnB;kBACR,CAAE;kBAAA8C,QAAA,gBAEF5D,OAAA,CAAC5B,GAAG;oBACFmG,EAAE,EAAE;sBACFC,KAAK,EAAE,EAAE;sBACTiD,MAAM,EAAE,EAAE;sBACVZ,YAAY,EAAE,KAAK;sBACnBpC,OAAO,EACL3D,GAAG,KAAK,CAAC,GACL,SAAS,GACTA,GAAG,KAAK,CAAC,GACP,SAAS,GACT,SAAS;sBACjBM,KAAK,EAAE,MAAM;sBACbqC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBsB,UAAU,EAAE,GAAG;sBACfK,QAAQ,EAAE,EAAE;sBACZ0B,EAAE,EAAE,CAAC;sBACLF,SAAS,EACPhG,GAAG,KAAK,CAAC,GACL,oBAAoB,GACpB4G;oBACR,CAAE;oBAAA9D,QAAA,EAED9C,GAAG,KAAK,CAAC,GAAG,IAAI,GAAGA,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG;kBAAI;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNjE,OAAA,CAAC5B,GAAG;oBAACuJ,IAAI,EAAE,CAAE;oBAAA/D,QAAA,gBACX5D,OAAA,CAAC9B,UAAU;sBAAC+G,UAAU,EAAE,GAAI;sBAAArB,QAAA,EAAE0D,CAAC,CAACM;oBAAW;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACzDjE,OAAA,CAAC9B,UAAU;sBAAC8G,OAAO,EAAC,SAAS;sBAAC5D,KAAK,EAAC,gBAAgB;sBAAAwC,QAAA,EACjD0D,CAAC,CAACO;oBAAW;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACbjE,OAAA,CAAC9B,UAAU;sBAAC8G,OAAO,EAAC,OAAO;sBAAC5D,KAAK,EAAC,gBAAgB;sBAAAwC,QAAA,GAC/C0D,CAAC,CAACQ,aAAa,EAAC,WACnB;oBAAA;sBAAAhE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GAlDDqD,CAAC,CAACS,SAAS;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmDX,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5D,OAAA,CAAChC,IAAI;YAACuG,EAAE,EAAE;cACRsC,YAAY,EAAE,MAAM;cACpBrD,SAAS,EAAE,GAAG;cACdsD,SAAS,EAAE,kCAAkC;cAC7CC,UAAU,EAAE;YACd,CAAE;YAAAnD,QAAA,eACA5D,OAAA,CAAC/B,WAAW;cAAA2F,QAAA,gBACV5D,OAAA,CAAC9B,UAAU;gBAAC8G,OAAO,EAAC,WAAW;gBAACC,UAAU,EAAE,GAAI;gBAACE,EAAE,EAAE,GAAI;gBAAAvB,QAAA,gBACvD5D,OAAA,CAAClB,QAAQ;kBAACsC,KAAK,EAAC,SAAS;kBAACkE,QAAQ,EAAC,QAAQ;kBAACf,EAAE,EAAE;oBAAEyC,EAAE,EAAE;kBAAG;gBAAE;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEhE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7B,KAAK;gBAACqH,OAAO,EAAE,CAAE;gBAACS,EAAE,EAAE,CAAE;gBAAArC,QAAA,EACtBhC,aAAa,CAACP,MAAM,KAAK,CAAC,gBACzBrB,OAAA,CAAC9B,UAAU;kBAACkD,KAAK,EAAC,gBAAgB;kBAAAwC,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,GAElErC,aAAa,CAACf,GAAG,CAAC,CAACmH,CAAC,EAAElH,GAAG,kBACvBd,OAAA,CAAC7B,KAAK;kBACJ6H,SAAS,EAAC,KAAK;kBACftC,UAAU,EAAC,QAAQ;kBACnB8B,OAAO,EAAE,CAAE;kBAAA5B,QAAA,gBAGX5D,OAAA,CAAC1B,MAAM;oBACL2J,GAAG,EAAED,CAAC,CAACE,UAAW;oBAClBC,GAAG,EAAEH,CAAC,CAACjH,IAAK;oBACZwD,EAAE,EAAE;sBACFC,KAAK,EAAE,EAAE;sBACTiD,MAAM,EAAE,EAAE;sBACVT,EAAE,EAAE,CAAC;sBACLvC,OAAO,EACL3D,GAAG,KAAK,CAAC,GACL,SAAS,GACTA,GAAG,KAAK,CAAC,GACP,SAAS,GACT,SAAS;sBACjBsH,MAAM,EACJtH,GAAG,KAAK,CAAC,GACL,mBAAmB,GACnBA,GAAG,KAAK,CAAC,GACP,mBAAmB,GACnB,mBAAmB;sBAC3BgG,SAAS,EACPhG,GAAG,KAAK,CAAC,GACL,oBAAoB,GACpB4G;oBACR,CAAE;oBAAA9D,QAAA,EAED,CAACoE,CAAC,CAACE,UAAU,IAAIF,CAAC,CAACjH,IAAI,GAAGiH,CAAC,CAACjH,IAAI,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,GAAG;kBAAI;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACTjE,OAAA,CAAC5B,GAAG;oBAAAwF,QAAA,gBACF5D,OAAA,CAAC9B,UAAU;sBAAC+G,UAAU,EAAE,GAAI;sBAAArB,QAAA,EAAEoE,CAAC,CAACjH;oBAAI;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAClDjE,OAAA,CAAC9B,UAAU;sBAAC8G,OAAO,EAAC,OAAO;sBAAC5D,KAAK,EAAC,gBAAgB;sBAAAwC,QAAA,EAC/CoE,CAAC,CAACK,aAAa,GAAG,KAAKL,CAAC,CAACK,aAAa,EAAE,GAAG;oBAAE;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GAlCD+D,CAAC,CAACM,WAAW;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmCb,CACR;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPjE,OAAA,CAAC3B,OAAO;QAACkG,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAE/B,QAAQ,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAO,CAAE;QAAAlB,QAAA,eAChD5D,OAAA,CAACf,mBAAmB;UAACsF,EAAE,EAAE;YAAEnD,KAAK,EAAE;UAAU,CAAE;UAACkE,QAAQ,EAAC;QAAO;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACVjE,OAAA,CAACjC,IAAI;QAACwH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA5B,QAAA,gBAEzB5D,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5D,OAAA,CAAChC,IAAI;YAACuG,EAAE,EAAE;cACRsC,YAAY,EAAE,MAAM;cACpBU,CAAC,EAAE,CAAC;cACJT,SAAS,EAAE;YACb,CAAE;YAAAlD,QAAA,eACA5D,OAAA,CAAC/B,WAAW;cAAA2F,QAAA,gBACV5D,OAAA,CAAC7B,KAAK;gBAAC6H,SAAS,EAAC,KAAK;gBAACtC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAACwB,EAAE,EAAE,CAAE;gBAAAvB,QAAA,gBAC9E5D,OAAA,CAAC9B,UAAU;kBAAC+G,UAAU,EAAE,GAAI;kBAACD,OAAO,EAAC,WAAW;kBAAApB,QAAA,gBAC9C5D,OAAA,CAAChB,aAAa;oBAACoC,KAAK,EAAC,SAAS;oBAACkE,QAAQ,EAAC,QAAQ;oBAACf,EAAE,EAAE;sBAAEyC,EAAE,EAAE;oBAAG;kBAAE;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAErE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACxB,iBAAiB;kBAChB2C,KAAK,EAAEiB,iBAAkB;kBACzBmG,SAAS;kBACT1E,IAAI,EAAC,OAAO;kBACZ2E,QAAQ,EAAEA,CAACC,CAAC,EAAEtH,KAAK,KAAKA,KAAK,IAAIkB,oBAAoB,CAAClB,KAAK,CAAE;kBAC7DoD,EAAE,EAAE;oBAAEwC,UAAU,EAAE,SAAS;oBAAEF,YAAY,EAAE;kBAAE,CAAE;kBAAAjD,QAAA,gBAE/C5D,OAAA,CAACzB,YAAY;oBAAC4C,KAAK,EAAC,KAAK;oBAAC,cAAW,KAAK;oBAAAyC,QAAA,eACxC5D,OAAA,CAACd,YAAY;sBAACoG,QAAQ,EAAC;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACfjE,OAAA,CAACzB,YAAY;oBAAC4C,KAAK,EAAC,KAAK;oBAAC,cAAW,KAAK;oBAAAyC,QAAA,eACxC5D,OAAA,CAACf,mBAAmB;sBAACqG,QAAQ,EAAC;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACRjE,OAAA,CAAC3B,OAAO;gBAACkG,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BjE,OAAA,CAAC5B,GAAG;gBAACqJ,MAAM,EAAE,GAAI;gBAAA7D,QAAA,EACdxB,iBAAiB,KAAK,KAAK,gBAC1BpC,OAAA,CAACH,mBAAmB;kBAAC2E,KAAK,EAAC,MAAM;kBAACiD,MAAM,EAAC,MAAM;kBAAA7D,QAAA,eAC7C5D,OAAA,CAACZ,QAAQ;oBAACkE,IAAI,EAAEd,cAAe;oBAAAoB,QAAA,gBAC7B5D,OAAA,CAACP,KAAK;sBAACiJ,OAAO,EAAC;oBAAM;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxBjE,OAAA,CAACN,KAAK;sBAACiJ,aAAa,EAAE;oBAAM;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BjE,OAAA,CAACL,OAAO;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACXjE,OAAA,CAACX,GAAG;sBAACqJ,OAAO,EAAC,OAAO;sBAACE,MAAM,EAAE,EAAG;sBAAAhF,QAAA,GAC7BpB,cAAc,CAAC3B,GAAG,CAAC,CAACgI,KAAK,EAAE/H,GAAG,kBAC7Bd,OAAA,CAACR,IAAI;wBAA8BsJ,IAAI,EAAED,KAAK,CAACzH;sBAAM,GAA1C,iBAAiBN,GAAG,EAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsB,CACxD,CAAC,eACFjE,OAAA,CAACF,SAAS;wBAAC4I,OAAO,EAAC,OAAO;wBAACK,QAAQ,EAAC,KAAK;wBAAC9D,UAAU,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,gBAEtBjE,OAAA,CAACH,mBAAmB;kBAAC2E,KAAK,EAAC,MAAM;kBAACiD,MAAM,EAAC,MAAM;kBAAA7D,QAAA,eAC7C5D,OAAA,CAACV,QAAQ;oBAAAsE,QAAA,gBACP5D,OAAA,CAACT,GAAG;sBACF+D,IAAI,EAAEd,cAAe;sBACrBwG,EAAE,EAAC,KAAK;sBACRC,EAAE,EAAC,KAAK;sBACRC,WAAW,EAAE,EAAG;sBAChBC,WAAW,EAAE,EAAG;sBAChBL,IAAI,EAAC,SAAS;sBACdM,YAAY,EAAE,CAAE;sBAChBV,OAAO,EAAC,OAAO;sBACf5C,KAAK;sBAAAlC,QAAA,EAEJpB,cAAc,CAAC3B,GAAG,CAAC,CAACgI,KAAK,EAAE/H,GAAG,kBAC7Bd,OAAA,CAACR,IAAI;wBAA8BsJ,IAAI,EAAED,KAAK,CAACzH;sBAAM,GAA1C,iBAAiBN,GAAG,EAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsB,CACxD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNjE,OAAA,CAACJ,MAAM;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVjE,OAAA,CAACL,OAAO;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cACtB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjE,OAAA,CAACjC,IAAI;UAAC0H,IAAI;UAACd,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5D,OAAA,CAAChC,IAAI;YAACuG,EAAE,EAAE;cACRsC,YAAY,EAAE,MAAM;cACpBU,CAAC,EAAE,CAAC;cACJT,SAAS,EAAE;YACb,CAAE;YAAAlD,QAAA,eACA5D,OAAA,CAAC/B,WAAW;cAAA2F,QAAA,gBACV5D,OAAA,CAAC7B,KAAK;gBAAC6H,SAAS,EAAC,KAAK;gBAACtC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAACwB,EAAE,EAAE,CAAE;gBAAAvB,QAAA,gBAC9E5D,OAAA,CAAC9B,UAAU;kBAAC+G,UAAU,EAAE,GAAI;kBAACD,OAAO,EAAC,WAAW;kBAAApB,QAAA,gBAC9C5D,OAAA,CAAChB,aAAa;oBAACoC,KAAK,EAAC,SAAS;oBAACkE,QAAQ,EAAC,QAAQ;oBAACf,EAAE,EAAE;sBAAEyC,EAAE,EAAE;oBAAG;kBAAE;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mCAErE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACxB,iBAAiB;kBAChB2C,KAAK,EAAEmB,aAAc;kBACrBiG,SAAS;kBACT1E,IAAI,EAAC,OAAO;kBACZ2E,QAAQ,EAAEA,CAACC,CAAC,EAAEtH,KAAK,KAAKA,KAAK,IAAIoB,gBAAgB,CAACpB,KAAK,CAAE;kBACzDoD,EAAE,EAAE;oBAAEwC,UAAU,EAAE,SAAS;oBAAEF,YAAY,EAAE;kBAAE,CAAE;kBAAAjD,QAAA,gBAE/C5D,OAAA,CAACzB,YAAY;oBAAC4C,KAAK,EAAC,KAAK;oBAAC,cAAW,KAAK;oBAAAyC,QAAA,eACxC5D,OAAA,CAACd,YAAY;sBAACoG,QAAQ,EAAC;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACfjE,OAAA,CAACzB,YAAY;oBAAC4C,KAAK,EAAC,KAAK;oBAAC,cAAW,KAAK;oBAAAyC,QAAA,eACxC5D,OAAA,CAACf,mBAAmB;sBAACqG,QAAQ,EAAC;oBAAO;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACRjE,OAAA,CAAC3B,OAAO;gBAACkG,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BjE,OAAA,CAAC5B,GAAG;gBAACqJ,MAAM,EAAE,GAAI;gBAAA7D,QAAA,EACdtB,aAAa,KAAK,KAAK,gBACtBtC,OAAA,CAACH,mBAAmB;kBAAC2E,KAAK,EAAC,MAAM;kBAACiD,MAAM,EAAC,MAAM;kBAAA7D,QAAA,eAC7C5D,OAAA,CAACZ,QAAQ;oBAACkE,IAAI,EAAExB,WAAY;oBAAA8B,QAAA,gBAC1B5D,OAAA,CAACP,KAAK;sBAACiJ,OAAO,EAAC;oBAAO;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzBjE,OAAA,CAACN,KAAK;sBAACiJ,aAAa,EAAE;oBAAM;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BjE,OAAA,CAACL,OAAO;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACXjE,OAAA,CAACJ,MAAM;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVjE,OAAA,CAACX,GAAG;sBAACqJ,OAAO,EAAC,UAAU;sBAACW,OAAO,EAAC,GAAG;sBAACP,IAAI,EAAE7I,MAAM,CAAC,CAAC,CAAE;sBAACc,IAAI,EAAC;oBAAW;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxEjE,OAAA,CAACX,GAAG;sBAACqJ,OAAO,EAAC,YAAY;sBAACW,OAAO,EAAC,GAAG;sBAACP,IAAI,EAAE7I,MAAM,CAAC,CAAC,CAAE;sBAACc,IAAI,EAAC;oBAAY;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3EjE,OAAA,CAACX,GAAG;sBAACqJ,OAAO,EAAC,UAAU;sBAACW,OAAO,EAAC,GAAG;sBAACP,IAAI,EAAE7I,MAAM,CAAC,CAAC,CAAE;sBAACc,IAAI,EAAC;oBAAW;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxEjE,OAAA,CAACX,GAAG;sBAACqJ,OAAO,EAAC,gBAAgB;sBAACW,OAAO,EAAC,GAAG;sBAACP,IAAI,EAAE7I,MAAM,CAAC,CAAC,CAAE;sBAACc,IAAI,EAAC;oBAAgB;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,gBAEtBjE,OAAA,CAACH,mBAAmB;kBAAC2E,KAAK,EAAC,MAAM;kBAACiD,MAAM,EAAC,MAAM;kBAAA7D,QAAA,eAC7C5D,OAAA,CAACV,QAAQ;oBAAAsE,QAAA,gBACP5D,OAAA,CAACT,GAAG;sBACF+D,IAAI,EAAEgB,cAAe;sBACrB0E,EAAE,EAAC,KAAK;sBACRC,EAAE,EAAC,KAAK;sBACRC,WAAW,EAAE,EAAG;sBAChBC,WAAW,EAAE,EAAG;sBAChBL,IAAI,EAAC,SAAS;sBACdM,YAAY,EAAE,CAAE;sBAChBV,OAAO,EAAC,OAAO;sBACf5C,KAAK;sBAAAlC,QAAA,EAEJU,cAAc,CAACzD,GAAG,CAAC,CAACgI,KAAK,EAAE/H,GAAG,kBAC7Bd,OAAA,CAACR,IAAI;wBAA+BsJ,IAAI,EAAED,KAAK,CAACzH;sBAAM,GAA3C,kBAAkBN,GAAG,EAAE;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAsB,CACzD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNjE,OAAA,CAACJ,MAAM;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVjE,OAAA,CAACL,OAAO;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cACtB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAA1C,EAAA,CA9dwBD,cAAc;AAAAgI,EAAA,GAAdhI,cAAc;AA+dtC,SAASqE,QAAQA,CAAC;EAAEC,IAAI;EAAEzE,KAAK;EAAE2E,KAAK;EAAE1E,KAAK;EAAE2E;AAAM,CAAC,EAAE;EACtD,oBACE/F,OAAA,CAAC5B,GAAG;IACFmG,EAAE,EAAE;MACFwC,UAAU,EAAE,2BAA2B3F,KAAK,oBAAoB;MAChE0F,SAAS,EAAE,gBAAgB1F,KAAK,IAAI;MACpCyF,YAAY,EAAE,MAAM;MACpBU,CAAC,EAAE,CAAC;MACJ/D,SAAS,EAAE,GAAG;MACdC,OAAO,EAAE,MAAM;MACf8F,aAAa,EAAE,QAAQ;MACvB7F,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxB6F,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE;QACTC,SAAS,EAAE,8BAA8B;QACzC3C,SAAS,EAAE,iBAAiB1F,KAAK;MACnC;IACF,CAAE;IAAAwC,QAAA,gBAEF5D,OAAA,CAAC7B,KAAK;MAAC6H,SAAS,EAAC,KAAK;MAACtC,UAAU,EAAC,QAAQ;MAAC8B,OAAO,EAAE,CAAE;MAAA5B,QAAA,gBACpD5D,OAAA,CAAC9B,UAAU;QAAC8G,OAAO,EAAC,IAAI;QAACC,UAAU,EAAE,GAAI;QAAC7D,KAAK,EAAEA,KAAM;QAAAwC,QAAA,EACpDzC;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbjE,OAAA,CAAC1B,MAAM;QAACiG,EAAE,EAAE;UAAEE,OAAO,EAAErD,KAAK;UAAEoD,KAAK,EAAE,EAAE;UAAEiD,MAAM,EAAE;QAAG,CAAE;QAAA7D,QAAA,EACnDgC;MAAI;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACRjE,OAAA,CAAC9B,UAAU;MAAC+H,EAAE,EAAE,CAAE;MAAC7E,KAAK,EAAEA,KAAM;MAAC6D,UAAU,EAAE,GAAI;MAACK,QAAQ,EAAE,EAAG;MAAA1B,QAAA,EAC5DkC;IAAK;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACZ8B,KAAK;EAAA;IAAAjC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAyF,GAAA,GApCS/D,QAAQ;AAqCjB,SAASsB,QAAQA,CAAC;EAAEnB,KAAK;EAAE1E,KAAK;EAAED;AAAM,CAAC,EAAE;EACzC,oBACEnB,OAAA,CAAC7B,KAAK;IAAC6H,SAAS,EAAC,KAAK;IAACtC,UAAU,EAAC,QAAQ;IAAC8B,OAAO,EAAE,CAAE;IAAA5B,QAAA,gBACpD5D,OAAA,CAAC1B,MAAM;MAACiG,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEiD,MAAM,EAAE,EAAE;QAAEhD,OAAO,EAAErD;MAAM,CAAE;MAAAwC,QAAA,EAAEkC,KAAK,CAAC,CAAC;IAAC;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,eAC1EjE,OAAA,CAAC9B,UAAU;MAACyJ,IAAI,EAAE,CAAE;MAAC1C,UAAU,EAAE,GAAI;MAAArB,QAAA,EAAEkC;IAAK;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAC1DjE,OAAA,CAAC9B,UAAU;MAAC+G,UAAU,EAAE,GAAI;MAAC7D,KAAK,EAAEA,KAAM;MAAAwC,QAAA,EAAEzC;IAAK;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D,CAAC;AAEZ;AAAC0F,GAAA,GARQ1C,QAAQ;AAAA,IAAAqC,EAAA,EAAAI,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}