{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\AnimerSeanceView.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Tabs, Tab, Paper, Chip, Button, Collapse, Stack, TextField, IconButton, Divider } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport ReactPlayer from \"react-player\";\nimport { Description as DescriptionIcon, Quiz as QuizIcon, Chat as ChatIcon, InsertDriveFile as InsertDriveFileIcon, AddPhotoAlternate as AddPhotoAlternateIcon, Movie as MovieIcon, Save as SaveIcon, ZoomInMap as ZoomInMapIcon, Feedback as FeedbackIcon } from \"@mui/icons-material\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport io from \"socket.io-client\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport { Avatar } from \"@mui/material\";\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport { useNavigate } from \"react-router-dom\";\nimport AddSeanceFeedback from './AddSeanceFeedback';\nimport FeedbackFormateur from '../../../components/FeedbackFormateur';\nimport SeanceFeedbackList from './seancefeedbacklist';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimerSeanceView = () => {\n  _s();\n  var _programDetails$progr;\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [seance, setSeance] = useState(null);\n  const [programDetails, setProgramDetails] = useState(null);\n  const [tab, setTab] = useState(0);\n  const [showContenus, setShowContenus] = useState(true);\n  const [sessionImages, setSessionImages] = useState([]);\n  const [sessionVideos, setSessionVideos] = useState([]);\n  const [zoomedImage, setZoomedImage] = useState(null);\n  const [expandedCourses, setExpandedCourses] = useState({});\n  const [sessionNotes, setSessionNotes] = useState(\"\");\n  const [saving, setSaving] = useState(false);\n  const [showFeedback, setShowFeedback] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [newMsg, setNewMsg] = useState(\"\");\n  const [showEmoji, setShowEmoji] = useState(false);\n  const [newFile, setNewFile] = useState(null);\n  const chatBottomRef = useRef();\n  const [socket, setSocket] = useState(null);\n\n  // Init socket.io\n  useEffect(() => {\n    const s = io(\"http://localhost:8000\");\n    setSocket(s);\n    s.emit(\"joinRoom\", {\n      seanceId: Number(seanceId)\n    });\n    s.on(\"newMessage\", msg => {\n      setChatMessages(prev => [...prev, msg]);\n    });\n    s.on(\"deleteMessage\", payload => {\n      setChatMessages(prev => prev.filter(m => m.id !== payload.id));\n    });\n    return () => {\n      s.disconnect();\n    };\n  }, [seanceId]);\n\n  // Load old messages\n  useEffect(() => {\n    if (!seanceId) return;\n    axios.get(`http://localhost:8000/chat-messages/${seanceId}`).then(res => setChatMessages(res.data)).catch(() => setChatMessages([]));\n  }, [seanceId]);\n\n  // Load seance data\n  useEffect(() => {\n    const fetchSeance = async () => {\n      try {\n        var _base$session;\n        const res = await axios.get(`http://localhost:8000/seance-formateur/${seanceId}`);\n        const base = res.data;\n        setSeance(base);\n        if (base !== null && base !== void 0 && (_base$session = base.session2) !== null && _base$session !== void 0 && _base$session.id) {\n          const detailRes = await axios.get(`http://localhost:8000/seance-formateur/details/${base.session2.id}`);\n          setProgramDetails(detailRes.data);\n        }\n      } catch (err) {\n        console.error(\"❌ Erreur chargement séance :\", err);\n      }\n    };\n    fetchSeance();\n  }, [seanceId]);\n\n  // Load media\n  useEffect(() => {\n    if (!seanceId) return;\n    axios.get(`http://localhost:8000/seance-formateur/${seanceId}/media`).then(res => {\n      setSessionImages(res.data.filter(m => m.type === \"IMAGE\"));\n      setSessionVideos(res.data.filter(m => m.type === \"VIDEO\"));\n    }).catch(err => {\n      console.error(\"Erreur chargement médias:\", err);\n    });\n  }, [seanceId]);\n  const uploadMedia = async (file, type) => {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    formData.append(\"type\", type);\n    const res = await axios.post(`http://localhost:8000/seance-formateur/${seanceId}/upload-media`, formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\"\n      }\n    });\n    return res.data;\n  };\n  const toggleCourseVisibility = courseId => {\n    setExpandedCourses(prev => ({\n      ...prev,\n      [courseId]: !prev[courseId]\n    }));\n  };\n  const handleTabChange = (e, newValue) => {\n    if (newValue !== 4 && newValue !== 5) setPrevTab(tab);\n    setTab(newValue);\n  };\n  const handleAddImage = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    try {\n      const media = await uploadMedia(file, \"IMAGE\");\n      setSessionImages(prev => [...prev, media]);\n    } catch (err) {\n      alert(t('uploadImageError'));\n    }\n  };\n  const handleAddVideo = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    try {\n      const media = await uploadMedia(file, \"VIDEO\");\n      setSessionVideos(prev => [...prev, media]);\n    } catch (err) {\n      alert(t('uploadVideoError'));\n    }\n  };\n  const handleSaveSession = async () => {\n    setSaving(true);\n    setTimeout(() => setSaving(false), 1000);\n    alert(t('seance.saveSuccess'));\n  };\n  const handlePublishContenu = async contenuId => {\n    if (!contenuId) return;\n    try {\n      await axios.patch(`http://localhost:8000/contenus/${contenuId}/publish`, {\n        published: true\n      });\n      const detailRes = await axios.get(`http://localhost:8000/seance-formateur/details/${seance.session2.id}`);\n      setProgramDetails(detailRes.data);\n    } catch {\n      alert(t('statusChangeError'));\n    }\n  };\n  const handleEmoji = e => {\n    setNewMsg(prev => prev + e.emoji);\n    setShowEmoji(false);\n  };\n  const handleChatSend = async () => {\n    if (!socket) return;\n    if (newFile) {\n      const formData = new FormData();\n      formData.append(\"file\", newFile);\n      formData.append(\"seanceId\", seanceId);\n      try {\n        const res = await axios.post(\"http://localhost:8000/chat-messages/upload-chat\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\"\n          }\n        });\n        socket.emit(\"sendMessage\", {\n          content: res.data.fileUrl,\n          type: res.data.fileType || \"file\",\n          seanceId: Number(seanceId),\n          senderId: user.id\n        });\n        setNewFile(null);\n      } catch {\n        alert(t('fileUploadError'));\n      }\n    } else if (newMsg.trim()) {\n      socket.emit(\"sendMessage\", {\n        content: newMsg,\n        type: \"text\",\n        seanceId: Number(seanceId),\n        senderId: user.id\n      });\n      setNewMsg(\"\");\n    }\n  };\n  const handleDeleteMsg = async msgId => {\n    try {\n      await axios.delete(`http://localhost:8000/chat-messages/${msgId}`, {\n        data: {\n          userId: user.id\n        }\n      });\n      setChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } catch (err) {\n      alert(t('seances.deleteMessageError'));\n    }\n  };\n  const renderProgramHierarchy = () => {\n    if (!programDetails) return /*#__PURE__*/_jsxDEV(Typography, {\n      children: t('loadingProgram')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 33\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        mb: 1,\n        children: [\"\\uD83D\\uDCD8 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [t('program'), \" : \", programDetails.program.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 44\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        ml: 2,\n        mt: 2,\n        children: programDetails.session2Modules.map(mod => /*#__PURE__*/_jsxDEV(Box, {\n          mt: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            fontWeight: \"bold\",\n            sx: {\n              color: \"#1976d2\"\n            },\n            children: [\"\\uD83D\\uDCE6 \", mod.module.title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            ml: 3,\n            children: mod.courses.map(course => /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                alignItems: \"center\",\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  sx: {\n                    color: \"#1e88e5\"\n                  },\n                  children: [\"\\uD83D\\uDCD8 \", course.course.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"outlined\",\n                  onClick: () => toggleCourseVisibility(course.id),\n                  children: expandedCourses[course.id] ? t('hide') : t('show')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n                in: expandedCourses[course.id],\n                children: course.contenus.map(ct => {\n                  var _ct$contenu$id, _ct$contenu, _ct$contenu3, _ct$contenu4, _ct$contenu6;\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    flexWrap: \"wrap\",\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(InsertDriveFileIcon, {\n                        sx: {\n                          fontSize: 22,\n                          color: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 35\n                      }, this),\n                      label: ct.contenu.title,\n                      variant: \"outlined\",\n                      onClick: () => {\n                        var _ct$contenu2;\n                        return ((_ct$contenu2 = ct.contenu) === null || _ct$contenu2 === void 0 ? void 0 : _ct$contenu2.fileUrl) && window.open(ct.contenu.fileUrl, \"_blank\");\n                      },\n                      sx: {\n                        cursor: (_ct$contenu3 = ct.contenu) !== null && _ct$contenu3 !== void 0 && _ct$contenu3.fileUrl ? \"pointer\" : \"default\",\n                        borderColor: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\",\n                        color: ct.contenu.published ? \"#2e7d32\" : \"#546e7a\",\n                        fontWeight: \"bold\",\n                        minWidth: 140,\n                        justifyContent: \"flex-start\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"small\",\n                      variant: \"outlined\",\n                      color: (_ct$contenu4 = ct.contenu) !== null && _ct$contenu4 !== void 0 && _ct$contenu4.published ? \"success\" : \"warning\",\n                      onClick: () => {\n                        var _ct$contenu5;\n                        return handlePublishContenu((_ct$contenu5 = ct.contenu) === null || _ct$contenu5 === void 0 ? void 0 : _ct$contenu5.id);\n                      },\n                      children: (_ct$contenu6 = ct.contenu) !== null && _ct$contenu6 !== void 0 && _ct$contenu6.published ? t('unpublish') : t('publish')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 27\n                    }, this)]\n                  }, (_ct$contenu$id = (_ct$contenu = ct.contenu) === null || _ct$contenu === void 0 ? void 0 : _ct$contenu.id) !== null && _ct$contenu$id !== void 0 ? _ct$contenu$id : uuidv4(), true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this)]\n            }, course.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, mod.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  };\n  if (!seance) return /*#__PURE__*/_jsxDEV(Typography, {\n    children: t('loadingSession')\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 0,\n        background: \"#f8fafc\",\n        minHeight: \"70vh\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        border: \"2px solid #bcbcbc\",\n        overflow: \"hidden\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        src: `https://localhost:8443/${seance.title || \"default-room\"}`,\n        allow: \"camera; microphone; fullscreen; display-capture\",\n        style: {\n          width: \"100%\",\n          height: \"70vh\",\n          border: \"none\"\n        },\n        title: t('jitsiMeeting')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `${t('program')} : ${(programDetails === null || programDetails === void 0 ? void 0 : (_programDetails$progr = programDetails.program) === null || _programDetails$progr === void 0 ? void 0 : _programDetails$progr.title) || \"\"}`,\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ZoomInMapIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 24\n          }, this),\n          onClick: () => setShowContenus(!showContenus),\n          variant: \"outlined\",\n          size: \"small\",\n          children: showContenus ? t('hideHierarchy') : t('showHierarchy')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 24\n          }, this),\n          onClick: () => setShowFeedback(!showFeedback),\n          variant: showFeedback ? \"outlined\" : \"contained\",\n          color: \"secondary\",\n          size: \"small\",\n          children: showFeedback ? t('hideFeedback') : t('showFeedback')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: showContenus,\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), renderProgramHierarchy()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      mt: 2,\n      children: [/*#__PURE__*/_jsxDEV(Tabs, {\n        orientation: \"vertical\",\n        value: tab,\n        onChange: handleTabChange,\n        sx: {\n          borderRight: 1,\n          borderColor: \"divider\",\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('sessionAdditions')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(QuizIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('quizComing')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(ChatIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('notesChat')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(InsertDriveFileIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('whiteboard'),\n          onClick: () => navigate(`/whiteboard/${seanceId}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedbackFormateur')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), showFeedback && /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 24\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedback')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedbackList')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        flex: 1,\n        pl: 3,\n        children: [tab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 1,\n            children: [t('sessionImages'), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              component: \"label\",\n              children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                hidden: true,\n                onChange: handleAddImage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            flexWrap: \"wrap\",\n            children: sessionImages.map(img => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: img.fileUrl,\n              alt: \"\",\n              style: {\n                maxHeight: 100,\n                margin: 2,\n                cursor: \"pointer\",\n                borderRadius: 8,\n                boxShadow: \"0 1px 6px #bbb\"\n              },\n              onClick: () => setZoomedImage(img.fileUrl)\n            }, img.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 2,\n            children: [t('sessionVideos'), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              component: \"label\",\n              children: [/*#__PURE__*/_jsxDEV(MovieIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"video/*\",\n                hidden: true,\n                onChange: handleAddVideo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            flexWrap: \"wrap\",\n            children: sessionVideos.map(vid => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 180\n              },\n              children: /*#__PURE__*/_jsxDEV(ReactPlayer, {\n                url: vid.fileUrl,\n                controls: true,\n                width: \"100%\",\n                height: 100\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 21\n              }, this)\n            }, vid.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 2,\n            children: t('sessionNotes')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            multiline: true,\n            minRows: 3,\n            placeholder: t('notesPlaceholder'),\n            value: sessionNotes,\n            onChange: e => setSessionNotes(e.target.value),\n            sx: {\n              my: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 34\n            }, this),\n            variant: \"contained\",\n            onClick: handleSaveSession,\n            disabled: saving,\n            children: saving ? t('saving') : t('saveSession')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this), tab === 1 && /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: [\"\\uD83E\\uDDEA \", t('quizFeature')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), tab === 2 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mb: 1,\n            children: [\"\\uD83D\\uDCAC \", t('sessionChat')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              maxHeight: 320,\n              minHeight: 150,\n              overflowY: \"auto\",\n              border: \"1px solid #ccc\",\n              borderRadius: 2,\n              background: \"#f9f9f9\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [chatMessages.map((msg, i) => {\n                var _msg$sender, _msg$sender2, _msg$sender2$profileP, _msg$sender3, _msg$sender4, _msg$sender5, _msg$sender5$name, _msg$sender5$name$, _msg$sender6, _msg$sender7, _msg$sender8;\n                return /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    p: 1,\n                    background: \"#fff\",\n                    display: \"flex\",\n                    alignItems: \"flex-start\",\n                    mb: 1,\n                    gap: 1\n                  },\n                  children: [(_msg$sender = msg.sender) !== null && _msg$sender !== void 0 && _msg$sender.profilePic ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: (_msg$sender2 = msg.sender) !== null && _msg$sender2 !== void 0 && (_msg$sender2$profileP = _msg$sender2.profilePic) !== null && _msg$sender2$profileP !== void 0 && _msg$sender2$profileP.startsWith('http') ? msg.sender.profilePic : `http://localhost:8000${((_msg$sender3 = msg.sender) === null || _msg$sender3 === void 0 ? void 0 : _msg$sender3.profilePic) || '/profile-pics/default.png'}`,\n                    alt: (_msg$sender4 = msg.sender) === null || _msg$sender4 === void 0 ? void 0 : _msg$sender4.name,\n                    style: {\n                      width: 32,\n                      height: 32,\n                      borderRadius: \"50%\",\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      width: 32,\n                      height: 32,\n                      marginRight: 1\n                    },\n                    children: ((_msg$sender5 = msg.sender) === null || _msg$sender5 === void 0 ? void 0 : (_msg$sender5$name = _msg$sender5.name) === null || _msg$sender5$name === void 0 ? void 0 : (_msg$sender5$name$ = _msg$sender5$name[0]) === null || _msg$sender5$name$ === void 0 ? void 0 : _msg$sender5$name$.toUpperCase()) || \"?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      color: \"primary\",\n                      children: [((_msg$sender6 = msg.sender) === null || _msg$sender6 === void 0 ? void 0 : _msg$sender6.name) || t('anonymous'), ((_msg$sender7 = msg.sender) === null || _msg$sender7 === void 0 ? void 0 : _msg$sender7.role) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: \"#888\",\n                          fontWeight: 400,\n                          marginLeft: 8,\n                          fontSize: 13\n                        },\n                        children: [\"\\xB7 \", msg.sender.role]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 29\n                      }, this), msg.createdAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: \"#888\",\n                          fontSize: 11,\n                          marginLeft: 8\n                        },\n                        children: new Date(msg.createdAt).toLocaleTimeString([], {\n                          hour: \"2-digit\",\n                          minute: \"2-digit\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 29\n                      }, this), ((_msg$sender8 = msg.sender) === null || _msg$sender8 === void 0 ? void 0 : _msg$sender8.id) === user.id && /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleDeleteMsg(msg.id),\n                        color: \"error\",\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 478,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 477,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 25\n                    }, this), msg.type === \"text\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 49\n                    }, this), msg.type === \"image\" && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: msg.content,\n                      alt: \"img\",\n                      style: {\n                        maxWidth: 180,\n                        borderRadius: 6,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 27\n                    }, this), msg.type === \"audio\" && /*#__PURE__*/_jsxDEV(\"audio\", {\n                      controls: true,\n                      src: msg.content,\n                      style: {\n                        maxWidth: 180,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 27\n                    }, this), msg.type === \"video\" && /*#__PURE__*/_jsxDEV(\"video\", {\n                      controls: true,\n                      src: msg.content,\n                      style: {\n                        maxWidth: 180,\n                        borderRadius: 6,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 27\n                    }, this), msg.type === \"file\" && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: msg.content,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      style: {\n                        display: \"block\",\n                        marginTop: 4\n                      },\n                      children: [\"\\uD83D\\uDCCE \", msg.content.split(\"/\").pop()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this)]\n                }, i, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: chatBottomRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              value: newMsg,\n              size: \"small\",\n              placeholder: t('writeMessage'),\n              onChange: e => setNewMsg(e.target.value),\n              onKeyDown: e => e.key === \"Enter\" && handleChatSend(),\n              sx: {\n                background: \"#fff\",\n                borderRadius: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setShowEmoji(v => !v),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"emoji\",\n                children: \"\\uD83D\\uDE00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              component: \"label\",\n              color: newFile ? \"success\" : \"primary\",\n              children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                hidden: true,\n                type: \"file\",\n                accept: \"image/*,video/*,audio/*,application/pdf\",\n                onChange: e => setNewFile(e.target.files[0])\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleChatSend,\n              variant: \"contained\",\n              disabled: !newMsg.trim() && !newFile,\n              children: t('send')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), showEmoji && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: \"absolute\",\n              zIndex: 11\n            },\n            children: /*#__PURE__*/_jsxDEV(EmojiPicker, {\n              onEmojiClick: handleEmoji,\n              autoFocusSearch: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 17\n          }, this), newFile && /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"primary\",\n            fontSize: 12,\n            ml: 1,\n            mt: 0.5,\n            children: [t('fileReady'), \": \", newFile.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this), tab === 4 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mb: 2,\n            children: t('feedbackFormateur')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FeedbackFormateur, {\n            seanceId: seanceId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this), showFeedback && tab === 5 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            mb: 2,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"\\uD83D\\uDCDD \", t('sessionFeedback')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AddSeanceFeedback, {\n            seanceId: seanceId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this), tab === 6 && /*#__PURE__*/_jsxDEV(SeanceFeedbackList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), zoomedImage && /*#__PURE__*/_jsxDEV(Box, {\n      onClick: () => setZoomedImage(null),\n      sx: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        zIndex: 2000,\n        width: \"100vw\",\n        height: \"100vh\",\n        background: \"rgba(0,0,0,0.88)\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        cursor: \"zoom-out\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: zoomedImage,\n        alt: \"\",\n        style: {\n          maxWidth: \"92vw\",\n          maxHeight: \"92vh\",\n          borderRadius: 12,\n          boxShadow: \"0 2px 24px #111\"\n        },\n        onClick: e => e.stopPropagation()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 311,\n    columnNumber: 5\n  }, this);\n};\n_s(AnimerSeanceView, \"jm5qjo6MWPaVSDphaURrdcHBvwU=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = AnimerSeanceView;\nexport default AnimerSeanceView;\nvar _c;\n$RefreshReg$(_c, \"AnimerSeanceView\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useParams", "Box", "Typography", "Tabs", "Tab", "Paper", "Chip", "<PERSON><PERSON>", "Collapse", "<PERSON><PERSON>", "TextField", "IconButton", "Divider", "useTranslation", "axios", "ReactPlayer", "Description", "DescriptionIcon", "Quiz", "QuizIcon", "Cha<PERSON>", "ChatIcon", "InsertDriveFile", "InsertDriveFileIcon", "AddPhotoAlternate", "AddPhotoAlternateIcon", "Movie", "MovieIcon", "Save", "SaveIcon", "ZoomInMap", "ZoomInMapIcon", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "v4", "uuidv4", "io", "EmojiPicker", "Avatar", "DeleteIcon", "useNavigate", "AddSeanceFeedback", "FeedbackFormateur", "SeanceFeedbackList", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "_programDetails$progr", "t", "id", "seanceId", "seance", "setSeance", "programDetails", "setProgramDetails", "tab", "setTab", "showContenus", "setShowContenus", "sessionImages", "setSessionImages", "sessionVideos", "setSessionVideos", "zoomedImage", "setZoomedImage", "expandedCourses", "setExpandedCourses", "sessionNotes", "setSessionNotes", "saving", "setSaving", "showFeedback", "setShowFeedback", "chatMessages", "setChatMessages", "newMsg", "setNewMsg", "showE<PERSON>ji", "setShowEmoji", "newFile", "setNewFile", "chatBottomRef", "socket", "setSocket", "s", "emit", "Number", "on", "msg", "prev", "payload", "filter", "m", "disconnect", "get", "then", "res", "data", "catch", "fetchSeance", "_base$session", "base", "session2", "detailRes", "err", "console", "error", "type", "uploadMedia", "file", "formData", "FormData", "append", "post", "headers", "toggleCourseVisibility", "courseId", "handleTabChange", "e", "newValue", "setPrevTab", "handleAddImage", "target", "files", "media", "alert", "handleAddVideo", "handleSaveSession", "setTimeout", "handlePublishContenu", "contenuId", "patch", "published", "handleEmoji", "emoji", "handleChatSend", "content", "fileUrl", "fileType", "senderId", "user", "trim", "handleDeleteMsg", "msgId", "delete", "userId", "renderProgramHierarchy", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mb", "program", "title", "ml", "mt", "session2Modules", "map", "mod", "fontWeight", "sx", "color", "module", "courses", "course", "direction", "alignItems", "spacing", "size", "onClick", "in", "contenus", "ct", "_ct$contenu$id", "_ct$contenu", "_ct$contenu3", "_ct$contenu4", "_ct$contenu6", "display", "gap", "flexWrap", "icon", "fontSize", "contenu", "label", "_ct$contenu2", "window", "open", "cursor", "borderColor", "min<PERSON><PERSON><PERSON>", "justifyContent", "_ct$contenu5", "p", "background", "minHeight", "border", "overflow", "src", "allow", "style", "width", "height", "startIcon", "my", "orientation", "value", "onChange", "borderRight", "iconPosition", "navigate", "flex", "pl", "component", "accept", "hidden", "img", "alt", "maxHeight", "margin", "borderRadius", "boxShadow", "vid", "url", "controls", "fullWidth", "multiline", "minRows", "placeholder", "disabled", "overflowY", "i", "_msg$sender", "_msg$sender2", "_msg$sender2$profileP", "_msg$sender3", "_msg$sender4", "_msg$sender5", "_msg$sender5$name", "_msg$sender5$name$", "_msg$sender6", "_msg$sender7", "_msg$sender8", "sender", "profilePic", "startsWith", "name", "marginRight", "toUpperCase", "role", "marginLeft", "createdAt", "Date", "toLocaleTimeString", "hour", "minute", "max<PERSON><PERSON><PERSON>", "marginTop", "href", "rel", "split", "pop", "ref", "onKeyDown", "key", "v", "position", "zIndex", "onEmojiClick", "autoFocusSearch", "top", "left", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/AnimerSeanceView.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Paper,\r\n  Chip,\r\n  Button,\r\n  Collapse,\r\n  Stack,\r\n  TextField,\r\n  IconButton,\r\n  Divider,\r\n} from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport ReactPlayer from \"react-player\";\r\nimport {\r\n  Description as DescriptionIcon,\r\n  Quiz as QuizIcon,\r\n  Chat as ChatIcon,\r\n  InsertDriveFile as InsertDriveFileIcon,\r\n  AddPhotoAlternate as AddPhotoAlternateIcon,\r\n  Movie as MovieIcon,\r\n  Save as SaveIcon,\r\n  ZoomInMap as ZoomInMapIcon,\r\n  Feedback as FeedbackIcon,\r\n} from \"@mui/icons-material\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport io from \"socket.io-client\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport { Avatar } from \"@mui/material\";\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AddSeanceFeedback from './AddSeanceFeedback';\r\nimport FeedbackFormateur from '../../../components/FeedbackFormateur';\r\nimport SeanceFeedbackList from './seancefeedbacklist';\r\n\r\nconst AnimerSeanceView = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [seance, setSeance] = useState(null);\r\n  const [programDetails, setProgramDetails] = useState(null);\r\n  const [tab, setTab] = useState(0);\r\n  const [showContenus, setShowContenus] = useState(true);\r\n  const [sessionImages, setSessionImages] = useState([]);\r\n  const [sessionVideos, setSessionVideos] = useState([]);\r\n  const [zoomedImage, setZoomedImage] = useState(null);\r\n  const [expandedCourses, setExpandedCourses] = useState({});\r\n  const [sessionNotes, setSessionNotes] = useState(\"\");\r\n  const [saving, setSaving] = useState(false);\r\n  const [showFeedback, setShowFeedback] = useState(false);\r\n  const [chatMessages, setChatMessages] = useState([]);\r\n  const [newMsg, setNewMsg] = useState(\"\");\r\n  const [showEmoji, setShowEmoji] = useState(false);\r\n  const [newFile, setNewFile] = useState(null);\r\n  const chatBottomRef = useRef();\r\n  const [socket, setSocket] = useState(null);\r\n\r\n  // Init socket.io\r\n  useEffect(() => {\r\n    const s = io(\"http://localhost:8000\");\r\n    setSocket(s);\r\n    s.emit(\"joinRoom\", { seanceId: Number(seanceId) });\r\n\r\n    s.on(\"newMessage\", (msg) => {\r\n      setChatMessages((prev) => [...prev, msg]);\r\n    });\r\n\r\n    s.on(\"deleteMessage\", (payload) => {\r\n      setChatMessages((prev) => prev.filter((m) => m.id !== payload.id));\r\n    });\r\n\r\n    return () => {\r\n      s.disconnect();\r\n    };\r\n  }, [seanceId]);\r\n\r\n  // Load old messages\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    axios.get(`http://localhost:8000/chat-messages/${seanceId}`)\r\n      .then((res) => setChatMessages(res.data))\r\n      .catch(() => setChatMessages([]));\r\n  }, [seanceId]);\r\n\r\n  // Load seance data\r\n  useEffect(() => {\r\n    const fetchSeance = async () => {\r\n      try {\r\n        const res = await axios.get(`http://localhost:8000/seance-formateur/${seanceId}`);\r\n        const base = res.data;\r\n        setSeance(base);\r\n\r\n        if (base?.session2?.id) {\r\n          const detailRes = await axios.get(\r\n            `http://localhost:8000/seance-formateur/details/${base.session2.id}`\r\n          );\r\n          setProgramDetails(detailRes.data);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"❌ Erreur chargement séance :\", err);\r\n      }\r\n    };\r\n    fetchSeance();\r\n  }, [seanceId]);\r\n\r\n  // Load media\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    axios.get(`http://localhost:8000/seance-formateur/${seanceId}/media`)\r\n      .then(res => {\r\n        setSessionImages(res.data.filter(m => m.type === \"IMAGE\"));\r\n        setSessionVideos(res.data.filter(m => m.type === \"VIDEO\"));\r\n      })\r\n      .catch(err => {\r\n        console.error(\"Erreur chargement médias:\", err);\r\n      });\r\n  }, [seanceId]);\r\n\r\n  const uploadMedia = async (file, type) => {\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n    formData.append(\"type\", type);\r\n    const res = await axios.post(\r\n      `http://localhost:8000/seance-formateur/${seanceId}/upload-media`,\r\n      formData,\r\n      { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n    );\r\n    return res.data;\r\n  };\r\n\r\n  const toggleCourseVisibility = (courseId) => {\r\n    setExpandedCourses((prev) => ({\r\n      ...prev,\r\n      [courseId]: !prev[courseId],\r\n    }));\r\n  };\r\n\r\n  const handleTabChange = (e, newValue) => {\r\n    if (newValue !== 4 && newValue !== 5) setPrevTab(tab);\r\n    setTab(newValue);\r\n  };\r\n\r\n  const handleAddImage = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    try {\r\n      const media = await uploadMedia(file, \"IMAGE\");\r\n      setSessionImages((prev) => [...prev, media]);\r\n    } catch (err) {\r\n      alert(t('uploadImageError'));\r\n    }\r\n  };\r\n\r\n  const handleAddVideo = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    try {\r\n      const media = await uploadMedia(file, \"VIDEO\");\r\n      setSessionVideos((prev) => [...prev, media]);\r\n    } catch (err) {\r\n      alert(t('uploadVideoError'));\r\n    }\r\n  };\r\n\r\n  const handleSaveSession = async () => {\r\n    setSaving(true);\r\n    setTimeout(() => setSaving(false), 1000);\r\n    alert(t('seance.saveSuccess'));\r\n  };\r\n\r\n  const handlePublishContenu = async (contenuId) => {\r\n    if (!contenuId) return;\r\n    try {\r\n      await axios.patch(`http://localhost:8000/contenus/${contenuId}/publish`, {\r\n        published: true,\r\n      });\r\n      const detailRes = await axios.get(\r\n        `http://localhost:8000/seance-formateur/details/${seance.session2.id}`\r\n      );\r\n      setProgramDetails(detailRes.data);\r\n    } catch {\r\n      alert(t('statusChangeError'));\r\n    }\r\n  };\r\n\r\n  const handleEmoji = (e) => {\r\n    setNewMsg((prev) => prev + e.emoji);\r\n    setShowEmoji(false);\r\n  };\r\n\r\n  const handleChatSend = async () => {\r\n    if (!socket) return;\r\n    if (newFile) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", newFile);\r\n      formData.append(\"seanceId\", seanceId);\r\n      try {\r\n        const res = await axios.post(\"http://localhost:8000/chat-messages/upload-chat\", formData, {\r\n          headers: { \"Content-Type\": \"multipart/form-data\" },\r\n        });\r\n\r\n        socket.emit(\"sendMessage\", {\r\n          content: res.data.fileUrl,\r\n          type: res.data.fileType || \"file\",\r\n          seanceId: Number(seanceId),\r\n          senderId: user.id,\r\n        });\r\n\r\n        setNewFile(null);\r\n      } catch {\r\n        alert(t('fileUploadError'));\r\n      }\r\n    } else if (newMsg.trim()) {\r\n      socket.emit(\"sendMessage\", {\r\n        content: newMsg,\r\n        type: \"text\",\r\n        seanceId: Number(seanceId),\r\n        senderId: user.id,\r\n      });\r\n\r\n      setNewMsg(\"\");\r\n    }\r\n  };\r\n\r\n  const handleDeleteMsg = async (msgId) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/chat-messages/${msgId}`, {\r\n        data: { userId: user.id },\r\n      });\r\n      setChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } catch (err) {\r\n      alert(t('seances.deleteMessageError'));\r\n    }\r\n  };\r\n\r\n  const renderProgramHierarchy = () => {\r\n    if (!programDetails) return <Typography>{t('loadingProgram')}</Typography>;\r\n\r\n    return (\r\n      <Box>\r\n        <Typography variant=\"h6\" mb={1}>📘 <strong>{t('program')} : {programDetails.program.title}</strong></Typography>\r\n\r\n        <Box ml={2} mt={2}>\r\n          {programDetails.session2Modules.map((mod) => (\r\n            <Box key={mod.id} mt={2}>\r\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" sx={{ color: \"#1976d2\" }}>\r\n                📦 {mod.module.title}\r\n              </Typography>\r\n\r\n              <Box ml={3}>\r\n                {mod.courses.map((course) => (\r\n                  <Box key={course.id} mt={1}>\r\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\r\n                      <Typography variant=\"body1\" fontWeight=\"bold\" sx={{ color: \"#1e88e5\" }}>\r\n                        📘 {course.course.title}\r\n                      </Typography>\r\n                      <Button\r\n                        size=\"small\"\r\n                        variant=\"outlined\"\r\n                        onClick={() => toggleCourseVisibility(course.id)}\r\n                      >\r\n                        {expandedCourses[course.id] ? t('hide') : t('show')}\r\n                      </Button>\r\n                    </Stack>\r\n\r\n                    <Collapse in={expandedCourses[course.id]}>\r\n                      {course.contenus.map((ct) => (\r\n                        <Box key={ct.contenu?.id ?? uuidv4()} display=\"flex\" alignItems=\"center\" gap={1} flexWrap=\"wrap\" mt={1}>\r\n                          <Chip\r\n                            icon={<InsertDriveFileIcon sx={{ fontSize: 22, color: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\" }} />}\r\n                            label={ct.contenu.title}\r\n                            variant=\"outlined\"\r\n                            onClick={() => ct.contenu?.fileUrl && window.open(ct.contenu.fileUrl, \"_blank\")}\r\n                            sx={{\r\n                              cursor: ct.contenu?.fileUrl ? \"pointer\" : \"default\",\r\n                              borderColor: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\",\r\n                              color: ct.contenu.published ? \"#2e7d32\" : \"#546e7a\",\r\n                              fontWeight: \"bold\",\r\n                              minWidth: 140,\r\n                              justifyContent: \"flex-start\",\r\n                            }}\r\n                          />\r\n                          <Button\r\n                            size=\"small\"\r\n                            variant=\"outlined\"\r\n                            color={ct.contenu?.published ? \"success\" : \"warning\"}\r\n                            onClick={() => handlePublishContenu(ct.contenu?.id)}\r\n                          >\r\n                            {ct.contenu?.published ? t('unpublish') : t('publish')}\r\n                          </Button>\r\n                        </Box>\r\n                      ))}\r\n                    </Collapse>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Box>\r\n          ))}\r\n        </Box>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  if (!seance) return <Typography>{t('loadingSession')}</Typography>;\r\n\r\n  return (\r\n    <Box p={2}>\r\n      {/* Meet */}\r\n      <Paper sx={{\r\n        mb: 3, p: 0, background: \"#f8fafc\", minHeight: \"70vh\",\r\n        display: \"flex\", alignItems: \"center\", justifyContent: \"center\",\r\n        border: \"2px solid #bcbcbc\", overflow: \"hidden\",\r\n      }}>\r\n        <iframe\r\n          src={`https://localhost:8443/${seance.title || \"default-room\"}`}\r\n          allow=\"camera; microphone; fullscreen; display-capture\"\r\n          style={{ width: \"100%\", height: \"70vh\", border: \"none\" }}\r\n          title={t('jitsiMeeting')}\r\n        />\r\n      </Paper>\r\n\r\n      {/* Programme */}\r\n      <Paper sx={{ p: 2, mb: 2 }}>\r\n        <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n          <Chip label={`${t('program')} : ${programDetails?.program?.title || \"\"}`} color=\"info\" />\r\n          <Button\r\n            startIcon={<ZoomInMapIcon />}\r\n            onClick={() => setShowContenus(!showContenus)}\r\n            variant=\"outlined\"\r\n            size=\"small\"\r\n          >\r\n            {showContenus ? t('hideHierarchy') : t('showHierarchy')}\r\n          </Button>\r\n          <Button\r\n            startIcon={<FeedbackIcon />}\r\n            onClick={() => setShowFeedback(!showFeedback)}\r\n            variant={showFeedback ? \"outlined\" : \"contained\"}\r\n            color=\"secondary\"\r\n            size=\"small\"\r\n          >\r\n            {showFeedback ? t('hideFeedback') : t('showFeedback')}\r\n          </Button>\r\n        </Stack>\r\n        <Collapse in={showContenus}>\r\n          <Divider sx={{ my: 2 }} />\r\n          {renderProgramHierarchy()}\r\n        </Collapse>\r\n      </Paper>\r\n\r\n      {/* Tabs */}\r\n      <Box display=\"flex\" mt={2}>\r\n        <Tabs orientation=\"vertical\" value={tab} onChange={handleTabChange} sx={{ borderRight: 1, borderColor: \"divider\", minWidth: 180 }}>\r\n          <Tab icon={<DescriptionIcon />} iconPosition=\"start\" label={t('sessionAdditions')} />\r\n          <Tab icon={<QuizIcon />} iconPosition=\"start\" label={t('quizComing')} />\r\n          <Tab icon={<ChatIcon />} iconPosition=\"start\" label={t('notesChat')} />\r\n          <Tab icon={<InsertDriveFileIcon />} iconPosition=\"start\" label={t('whiteboard')} onClick={() => navigate(`/whiteboard/${seanceId}`)} />\r\n          <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedbackFormateur')} />\r\n          {showFeedback && (\r\n            <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedback')} />\r\n          )}\r\n          <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedbackList')} />\r\n        </Tabs>\r\n\r\n        <Box flex={1} pl={3}>\r\n          {/* Onglet 1 - Session Additions */}\r\n          {tab === 0 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mt={1}>\r\n                {t('sessionImages')}\r\n                <IconButton color=\"primary\" component=\"label\">\r\n                  <AddPhotoAlternateIcon />\r\n                  <input type=\"file\" accept=\"image/*\" hidden onChange={handleAddImage} />\r\n                </IconButton>\r\n              </Typography>\r\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\r\n                {sessionImages.map((img) => (\r\n                  <img\r\n                    key={img.id}\r\n                    src={img.fileUrl}\r\n                    alt=\"\"\r\n                    style={{ maxHeight: 100, margin: 2, cursor: \"pointer\", borderRadius: 8, boxShadow: \"0 1px 6px #bbb\" }}\r\n                    onClick={() => setZoomedImage(img.fileUrl)}\r\n                  />\r\n                ))}\r\n              </Stack>\r\n\r\n              <Typography variant=\"h6\" mt={2}>\r\n                {t('sessionVideos')}\r\n                <IconButton color=\"primary\" component=\"label\">\r\n                  <MovieIcon />\r\n                  <input type=\"file\" accept=\"video/*\" hidden onChange={handleAddVideo} />\r\n                </IconButton>\r\n              </Typography>\r\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\r\n                {sessionVideos.map((vid) => (\r\n                  <Box key={vid.id} sx={{ width: 180 }}>\r\n                    <ReactPlayer url={vid.fileUrl} controls width=\"100%\" height={100} />\r\n                  </Box>\r\n                ))}\r\n              </Stack>\r\n\r\n              <Typography variant=\"h6\" mt={2}>{t('sessionNotes')}</Typography>\r\n              <TextField\r\n                fullWidth multiline minRows={3}\r\n                placeholder={t('notesPlaceholder')}\r\n                value={sessionNotes}\r\n                onChange={(e) => setSessionNotes(e.target.value)}\r\n                sx={{ my: 1 }}\r\n              />\r\n              <Button startIcon={<SaveIcon />} variant=\"contained\" onClick={handleSaveSession} disabled={saving}>\r\n                {saving ? t('saving') : t('saveSession')}\r\n              </Button>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 2 - Quiz */}\r\n          {tab === 1 && (\r\n            <Typography color=\"text.secondary\">🧪 {t('quizFeature')}</Typography>\r\n          )}\r\n\r\n          {/* Onglet 3 - Chat */}\r\n          {tab === 2 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mb={1}>💬 {t('sessionChat')}</Typography>\r\n              <Paper sx={{\r\n                p: 2, mb: 2, maxHeight: 320, minHeight: 150, overflowY: \"auto\",\r\n                border: \"1px solid #ccc\", borderRadius: 2, background: \"#f9f9f9\"\r\n              }}>\r\n                <Stack spacing={1}>\r\n                  {chatMessages.map((msg, i) => (\r\n                    <Paper\r\n                      key={i}\r\n                      sx={{\r\n                        p: 1,\r\n                        background: \"#fff\",\r\n                        display: \"flex\",\r\n                        alignItems: \"flex-start\",\r\n                        mb: 1,\r\n                        gap: 1,\r\n                      }}\r\n                    >\r\n                      {msg.sender?.profilePic\r\n                        ? (\r\n                          <img\r\n                            src={\r\n                              msg.sender?.profilePic?.startsWith('http')\r\n                                ? msg.sender.profilePic\r\n                                : `http://localhost:8000${msg.sender?.profilePic || '/profile-pics/default.png'}`\r\n                            }\r\n                            alt={msg.sender?.name}\r\n                            style={{ width: 32, height: 32, borderRadius: \"50%\", marginRight: 8 }}\r\n                          />\r\n                        ) : (\r\n                          <Avatar sx={{ width: 32, height: 32, marginRight: 1 }}>\r\n                            {msg.sender?.name?.[0]?.toUpperCase() || \"?\"}\r\n                          </Avatar>\r\n                        )\r\n                      }\r\n\r\n                      <Box>\r\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\" color=\"primary\">\r\n                          {msg.sender?.name || t('anonymous')}\r\n                          {msg.sender?.role && (\r\n                            <span style={{ color: \"#888\", fontWeight: 400, marginLeft: 8, fontSize: 13 }}>\r\n                              · {msg.sender.role}\r\n                            </span>\r\n                          )}\r\n                          {msg.createdAt && (\r\n                            <span style={{ color: \"#888\", fontSize: 11, marginLeft: 8 }}>\r\n                              {new Date(msg.createdAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" })}\r\n                            </span>\r\n                          )}{msg.sender?.id === user.id && (\r\n                            <IconButton size=\"small\" onClick={() => handleDeleteMsg(msg.id)} color=\"error\">\r\n                              <DeleteIcon fontSize=\"small\" />\r\n                            </IconButton>\r\n                          )}\r\n                        </Typography>\r\n\r\n                        {/* Message Content */}\r\n                        {msg.type === \"text\" && <span>{msg.content}</span>}\r\n                        {msg.type === \"image\" && (\r\n                          <img src={msg.content} alt=\"img\" style={{ maxWidth: 180, borderRadius: 6, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"audio\" && (\r\n                          <audio controls src={msg.content} style={{ maxWidth: 180, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"video\" && (\r\n                          <video controls src={msg.content} style={{ maxWidth: 180, borderRadius: 6, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"file\" && (\r\n                          <a href={msg.content} target=\"_blank\" rel=\"noopener noreferrer\" style={{ display: \"block\", marginTop: 4 }}>\r\n                            📎 {msg.content.split(\"/\").pop()}\r\n                          </a>\r\n                        )}\r\n                      </Box>\r\n                    </Paper>\r\n                  ))}\r\n                  <div ref={chatBottomRef} />\r\n                </Stack>\r\n              </Paper>\r\n              <Stack direction=\"row\" spacing={1} alignItems=\"center\">\r\n                <TextField\r\n                  fullWidth\r\n                  value={newMsg}\r\n                  size=\"small\"\r\n                  placeholder={t('writeMessage')}\r\n                  onChange={(e) => setNewMsg(e.target.value)}\r\n                  onKeyDown={(e) => e.key === \"Enter\" && handleChatSend()}\r\n                  sx={{ background: \"#fff\", borderRadius: 1 }}\r\n                />\r\n                <IconButton onClick={() => setShowEmoji((v) => !v)}>\r\n                  <span role=\"img\" aria-label=\"emoji\">😀</span>\r\n                </IconButton>\r\n                <IconButton component=\"label\" color={newFile ? \"success\" : \"primary\"}>\r\n                  <AddPhotoAlternateIcon />\r\n                  <input\r\n                    hidden\r\n                    type=\"file\"\r\n                    accept=\"image/*,video/*,audio/*,application/pdf\"\r\n                    onChange={(e) => setNewFile(e.target.files[0])}\r\n                  />\r\n                </IconButton>\r\n                <Button onClick={handleChatSend} variant=\"contained\" disabled={!newMsg.trim() && !newFile}>\r\n                  {t('send')}\r\n                </Button>\r\n              </Stack>\r\n              {showEmoji && (\r\n                <Box sx={{ position: \"absolute\", zIndex: 11 }}>\r\n                  <EmojiPicker onEmojiClick={handleEmoji} autoFocusSearch={false} />\r\n                </Box>\r\n              )}\r\n              {newFile && (\r\n                <Typography color=\"primary\" fontSize={12} ml={1} mt={0.5}>\r\n                  {t('fileReady')}: {newFile.name}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 4 - Feedback Formateur */}\r\n          {tab === 4 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mb={2}>{t('feedbackFormateur')}</Typography>\r\n              <FeedbackFormateur seanceId={seanceId} />\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 5 - Feedback (dynamique) */}\r\n          {showFeedback && tab === 5 && (\r\n            <Box>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2} mb={2}>\r\n                <Typography variant=\"h6\">📝 {t('sessionFeedback')}</Typography>\r\n              </Stack>\r\n              <AddSeanceFeedback seanceId={seanceId} />\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 6 - Feedback List */}\r\n          {tab === 6 && (\r\n            <SeanceFeedbackList />\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Image zoom */}\r\n      {zoomedImage && (\r\n        <Box\r\n          onClick={() => setZoomedImage(null)}\r\n          sx={{\r\n            position: \"fixed\", top: 0, left: 0, zIndex: 2000, width: \"100vw\", height: \"100vh\",\r\n            background: \"rgba(0,0,0,0.88)\", display: \"flex\", alignItems: \"center\",\r\n            justifyContent: \"center\", cursor: \"zoom-out\",\r\n          }}\r\n        >\r\n          <img\r\n            src={zoomedImage}\r\n            alt=\"\"\r\n            style={{ maxWidth: \"92vw\", maxHeight: \"92vh\", borderRadius: 12, boxShadow: \"0 2px 24px #111\" }}\r\n            onClick={(e) => e.stopPropagation()}\r\n          />\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AnimerSeanceView;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,cAAc;AACtC,SACEC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,eAAe,IAAIC,mBAAmB,EACtCC,iBAAiB,IAAIC,qBAAqB,EAC1CC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC7B,MAAM;IAAEC;EAAE,CAAC,GAAGpC,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEqC,EAAE,EAAEC;EAAS,CAAC,GAAGnD,SAAS,CAAC,CAAC;EACpC,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0D,GAAG,EAAEC,MAAM,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,MAAM,EAAEC,SAAS,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8E,MAAM,EAAEC,SAAS,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkF,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoF,aAAa,GAAGnF,MAAM,CAAC,CAAC;EAC9B,MAAM,CAACoF,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;;EAE1C;EACAD,SAAS,CAAC,MAAM;IACd,MAAMwF,CAAC,GAAGjD,EAAE,CAAC,uBAAuB,CAAC;IACrCgD,SAAS,CAACC,CAAC,CAAC;IACZA,CAAC,CAACC,IAAI,CAAC,UAAU,EAAE;MAAEnC,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ;IAAE,CAAC,CAAC;IAElDkC,CAAC,CAACG,EAAE,CAAC,YAAY,EAAGC,GAAG,IAAK;MAC1Bd,eAAe,CAAEe,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEFJ,CAAC,CAACG,EAAE,CAAC,eAAe,EAAGG,OAAO,IAAK;MACjChB,eAAe,CAAEe,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,EAAE,KAAKyC,OAAO,CAACzC,EAAE,CAAC,CAAC;IACpE,CAAC,CAAC;IAEF,OAAO,MAAM;MACXmC,CAAC,CAACS,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAAC3C,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,QAAQ,EAAE;IACfrC,KAAK,CAACiF,GAAG,CAAC,uCAAuC5C,QAAQ,EAAE,CAAC,CACzD6C,IAAI,CAAEC,GAAG,IAAKtB,eAAe,CAACsB,GAAG,CAACC,IAAI,CAAC,CAAC,CACxCC,KAAK,CAAC,MAAMxB,eAAe,CAAC,EAAE,CAAC,CAAC;EACrC,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,MAAMuG,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QAAA,IAAAC,aAAA;QACF,MAAMJ,GAAG,GAAG,MAAMnF,KAAK,CAACiF,GAAG,CAAC,0CAA0C5C,QAAQ,EAAE,CAAC;QACjF,MAAMmD,IAAI,GAAGL,GAAG,CAACC,IAAI;QACrB7C,SAAS,CAACiD,IAAI,CAAC;QAEf,IAAIA,IAAI,aAAJA,IAAI,gBAAAD,aAAA,GAAJC,IAAI,CAAEC,QAAQ,cAAAF,aAAA,eAAdA,aAAA,CAAgBnD,EAAE,EAAE;UACtB,MAAMsD,SAAS,GAAG,MAAM1F,KAAK,CAACiF,GAAG,CAC/B,kDAAkDO,IAAI,CAACC,QAAQ,CAACrD,EAAE,EACpE,CAAC;UACDK,iBAAiB,CAACiD,SAAS,CAACN,IAAI,CAAC;QACnC;MACF,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,GAAG,CAAC;MACpD;IACF,CAAC;IACDL,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACjD,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,QAAQ,EAAE;IACfrC,KAAK,CAACiF,GAAG,CAAC,0CAA0C5C,QAAQ,QAAQ,CAAC,CAClE6C,IAAI,CAACC,GAAG,IAAI;MACXpC,gBAAgB,CAACoC,GAAG,CAACC,IAAI,CAACN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACe,IAAI,KAAK,OAAO,CAAC,CAAC;MAC1D7C,gBAAgB,CAACkC,GAAG,CAACC,IAAI,CAACN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACe,IAAI,KAAK,OAAO,CAAC,CAAC;IAC5D,CAAC,CAAC,CACDT,KAAK,CAACM,GAAG,IAAI;MACZC,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEF,GAAG,CAAC;IACjD,CAAC,CAAC;EACN,CAAC,EAAE,CAACtD,QAAQ,CAAC,CAAC;EAEd,MAAM0D,WAAW,GAAG,MAAAA,CAAOC,IAAI,EAAEF,IAAI,KAAK;IACxC,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7BC,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;IAC7B,MAAMX,GAAG,GAAG,MAAMnF,KAAK,CAACoG,IAAI,CAC1B,0CAA0C/D,QAAQ,eAAe,EACjE4D,QAAQ,EACR;MAAEI,OAAO,EAAE;QAAE,cAAc,EAAE;MAAsB;IAAE,CACvD,CAAC;IACD,OAAOlB,GAAG,CAACC,IAAI;EACjB,CAAC;EAED,MAAMkB,sBAAsB,GAAIC,QAAQ,IAAK;IAC3ClD,kBAAkB,CAAEuB,IAAI,KAAM;MAC5B,GAAGA,IAAI;MACP,CAAC2B,QAAQ,GAAG,CAAC3B,IAAI,CAAC2B,QAAQ;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvC,IAAIA,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,EAAEC,UAAU,CAACjE,GAAG,CAAC;IACrDC,MAAM,CAAC+D,QAAQ,CAAC;EAClB,CAAC;EAED,MAAME,cAAc,GAAG,MAAOH,CAAC,IAAK;IAClC,MAAMT,IAAI,GAAGS,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACd,IAAI,EAAE;IACX,IAAI;MACF,MAAMe,KAAK,GAAG,MAAMhB,WAAW,CAACC,IAAI,EAAE,OAAO,CAAC;MAC9CjD,gBAAgB,CAAE6B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEmC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOpB,GAAG,EAAE;MACZqB,KAAK,CAAC7E,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAM8E,cAAc,GAAG,MAAOR,CAAC,IAAK;IAClC,MAAMT,IAAI,GAAGS,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACd,IAAI,EAAE;IACX,IAAI;MACF,MAAMe,KAAK,GAAG,MAAMhB,WAAW,CAACC,IAAI,EAAE,OAAO,CAAC;MAC9C/C,gBAAgB,CAAE2B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEmC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOpB,GAAG,EAAE;MACZqB,KAAK,CAAC7E,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+E,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCzD,SAAS,CAAC,IAAI,CAAC;IACf0D,UAAU,CAAC,MAAM1D,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACxCuD,KAAK,CAAC7E,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAChC,CAAC;EAED,MAAMiF,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI;MACF,MAAMrH,KAAK,CAACsH,KAAK,CAAC,kCAAkCD,SAAS,UAAU,EAAE;QACvEE,SAAS,EAAE;MACb,CAAC,CAAC;MACF,MAAM7B,SAAS,GAAG,MAAM1F,KAAK,CAACiF,GAAG,CAC/B,kDAAkD3C,MAAM,CAACmD,QAAQ,CAACrD,EAAE,EACtE,CAAC;MACDK,iBAAiB,CAACiD,SAAS,CAACN,IAAI,CAAC;IACnC,CAAC,CAAC,MAAM;MACN4B,KAAK,CAAC7E,CAAC,CAAC,mBAAmB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqF,WAAW,GAAIf,CAAC,IAAK;IACzB1C,SAAS,CAAEa,IAAI,IAAKA,IAAI,GAAG6B,CAAC,CAACgB,KAAK,CAAC;IACnCxD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMyD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACrD,MAAM,EAAE;IACb,IAAIH,OAAO,EAAE;MACX,MAAM+B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjC,OAAO,CAAC;MAChC+B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE9D,QAAQ,CAAC;MACrC,IAAI;QACF,MAAM8C,GAAG,GAAG,MAAMnF,KAAK,CAACoG,IAAI,CAAC,iDAAiD,EAAEH,QAAQ,EAAE;UACxFI,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QACnD,CAAC,CAAC;QAEFhC,MAAM,CAACG,IAAI,CAAC,aAAa,EAAE;UACzBmD,OAAO,EAAExC,GAAG,CAACC,IAAI,CAACwC,OAAO;UACzB9B,IAAI,EAAEX,GAAG,CAACC,IAAI,CAACyC,QAAQ,IAAI,MAAM;UACjCxF,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ,CAAC;UAC1ByF,QAAQ,EAAEC,IAAI,CAAC3F;QACjB,CAAC,CAAC;QAEF+B,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,MAAM;QACN6C,KAAK,CAAC7E,CAAC,CAAC,iBAAiB,CAAC,CAAC;MAC7B;IACF,CAAC,MAAM,IAAI2B,MAAM,CAACkE,IAAI,CAAC,CAAC,EAAE;MACxB3D,MAAM,CAACG,IAAI,CAAC,aAAa,EAAE;QACzBmD,OAAO,EAAE7D,MAAM;QACfgC,IAAI,EAAE,MAAM;QACZzD,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ,CAAC;QAC1ByF,QAAQ,EAAEC,IAAI,CAAC3F;MACjB,CAAC,CAAC;MAEF2B,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMkE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI;MACF,MAAMlI,KAAK,CAACmI,MAAM,CAAC,uCAAuCD,KAAK,EAAE,EAAE;QACjE9C,IAAI,EAAE;UAAEgD,MAAM,EAAEL,IAAI,CAAC3F;QAAG;MAC1B,CAAC,CAAC;MACFyB,eAAe,CAAEe,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,EAAE,KAAK8F,KAAK,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAOvC,GAAG,EAAE;MACZqB,KAAK,CAAC7E,CAAC,CAAC,4BAA4B,CAAC,CAAC;IACxC;EACF,CAAC;EAED,MAAMkG,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC7F,cAAc,EAAE,oBAAOT,OAAA,CAAC3C,UAAU;MAAAkJ,QAAA,EAAEnG,CAAC,CAAC,gBAAgB;IAAC;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;IAE1E,oBACE3G,OAAA,CAAC5C,GAAG;MAAAmJ,QAAA,gBACFvG,OAAA,CAAC3C,UAAU;QAACuJ,OAAO,EAAC,IAAI;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,GAAC,eAAG,eAAAvG,OAAA;UAAAuG,QAAA,GAASnG,CAAC,CAAC,SAAS,CAAC,EAAC,KAAG,EAACK,cAAc,CAACqG,OAAO,CAACC,KAAK;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEhH3G,OAAA,CAAC5C,GAAG;QAAC4J,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,EACf9F,cAAc,CAACyG,eAAe,CAACC,GAAG,CAAEC,GAAG,iBACtCpH,OAAA,CAAC5C,GAAG;UAAc6J,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACtBvG,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,WAAW;YAACS,UAAU,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAhB,QAAA,GAAC,eACvE,EAACa,GAAG,CAACI,MAAM,CAACT,KAAK;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEb3G,OAAA,CAAC5C,GAAG;YAAC4J,EAAE,EAAE,CAAE;YAAAT,QAAA,EACRa,GAAG,CAACK,OAAO,CAACN,GAAG,CAAEO,MAAM,iBACtB1H,OAAA,CAAC5C,GAAG;cAAiB6J,EAAE,EAAE,CAAE;cAAAV,QAAA,gBACzBvG,OAAA,CAACpC,KAAK;gBAAC+J,SAAS,EAAC,KAAK;gBAACC,UAAU,EAAC,QAAQ;gBAACC,OAAO,EAAE,CAAE;gBAAAtB,QAAA,gBACpDvG,OAAA,CAAC3C,UAAU;kBAACuJ,OAAO,EAAC,OAAO;kBAACS,UAAU,EAAC,MAAM;kBAACC,EAAE,EAAE;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAhB,QAAA,GAAC,eACnE,EAACmB,MAAM,CAACA,MAAM,CAACX,KAAK;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACb3G,OAAA,CAACtC,MAAM;kBACLoK,IAAI,EAAC,OAAO;kBACZlB,OAAO,EAAC,UAAU;kBAClBmB,OAAO,EAAEA,CAAA,KAAMxD,sBAAsB,CAACmD,MAAM,CAACrH,EAAE,CAAE;kBAAAkG,QAAA,EAEhDlF,eAAe,CAACqG,MAAM,CAACrH,EAAE,CAAC,GAAGD,CAAC,CAAC,MAAM,CAAC,GAAGA,CAAC,CAAC,MAAM;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAER3G,OAAA,CAACrC,QAAQ;gBAACqK,EAAE,EAAE3G,eAAe,CAACqG,MAAM,CAACrH,EAAE,CAAE;gBAAAkG,QAAA,EACtCmB,MAAM,CAACO,QAAQ,CAACd,GAAG,CAAEe,EAAE;kBAAA,IAAAC,cAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;kBAAA,oBACtBvI,OAAA,CAAC5C,GAAG;oBAAkCoL,OAAO,EAAC,MAAM;oBAACZ,UAAU,EAAC,QAAQ;oBAACa,GAAG,EAAE,CAAE;oBAACC,QAAQ,EAAC,MAAM;oBAACzB,EAAE,EAAE,CAAE;oBAAAV,QAAA,gBACrGvG,OAAA,CAACvC,IAAI;sBACHkL,IAAI,eAAE3I,OAAA,CAACtB,mBAAmB;wBAAC4I,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,EAAE;0BAAErB,KAAK,EAAEW,EAAE,CAACW,OAAO,CAACrD,SAAS,GAAG,SAAS,GAAG;wBAAU;sBAAE;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzGmC,KAAK,EAAEZ,EAAE,CAACW,OAAO,CAAC9B,KAAM;sBACxBH,OAAO,EAAC,UAAU;sBAClBmB,OAAO,EAAEA,CAAA;wBAAA,IAAAgB,YAAA;wBAAA,OAAM,EAAAA,YAAA,GAAAb,EAAE,CAACW,OAAO,cAAAE,YAAA,uBAAVA,YAAA,CAAYlD,OAAO,KAAImD,MAAM,CAACC,IAAI,CAACf,EAAE,CAACW,OAAO,CAAChD,OAAO,EAAE,QAAQ,CAAC;sBAAA,CAAC;sBAChFyB,EAAE,EAAE;wBACF4B,MAAM,EAAE,CAAAb,YAAA,GAAAH,EAAE,CAACW,OAAO,cAAAR,YAAA,eAAVA,YAAA,CAAYxC,OAAO,GAAG,SAAS,GAAG,SAAS;wBACnDsD,WAAW,EAAEjB,EAAE,CAACW,OAAO,CAACrD,SAAS,GAAG,SAAS,GAAG,SAAS;wBACzD+B,KAAK,EAAEW,EAAE,CAACW,OAAO,CAACrD,SAAS,GAAG,SAAS,GAAG,SAAS;wBACnD6B,UAAU,EAAE,MAAM;wBAClB+B,QAAQ,EAAE,GAAG;wBACbC,cAAc,EAAE;sBAClB;oBAAE;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF3G,OAAA,CAACtC,MAAM;sBACLoK,IAAI,EAAC,OAAO;sBACZlB,OAAO,EAAC,UAAU;sBAClBW,KAAK,EAAE,CAAAe,YAAA,GAAAJ,EAAE,CAACW,OAAO,cAAAP,YAAA,eAAVA,YAAA,CAAY9C,SAAS,GAAG,SAAS,GAAG,SAAU;sBACrDuC,OAAO,EAAEA,CAAA;wBAAA,IAAAuB,YAAA;wBAAA,OAAMjE,oBAAoB,EAAAiE,YAAA,GAACpB,EAAE,CAACW,OAAO,cAAAS,YAAA,uBAAVA,YAAA,CAAYjJ,EAAE,CAAC;sBAAA,CAAC;sBAAAkG,QAAA,EAEnD,CAAAgC,YAAA,GAAAL,EAAE,CAACW,OAAO,cAAAN,YAAA,eAAVA,YAAA,CAAY/C,SAAS,GAAGpF,CAAC,CAAC,WAAW,CAAC,GAAGA,CAAC,CAAC,SAAS;oBAAC;sBAAAoG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA,IAAAwB,cAAA,IAAAC,WAAA,GAtBDF,EAAE,CAACW,OAAO,cAAAT,WAAA,uBAAVA,WAAA,CAAY/H,EAAE,cAAA8H,cAAA,cAAAA,cAAA,GAAI7I,MAAM,CAAC,CAAC;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuB/B,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,GAzCHe,MAAM,CAACrH,EAAE;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAnDES,GAAG,CAAC/G,EAAE;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAI,CAACpG,MAAM,EAAE,oBAAOP,OAAA,CAAC3C,UAAU;IAAAkJ,QAAA,EAAEnG,CAAC,CAAC,gBAAgB;EAAC;IAAAoG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAa,CAAC;EAElE,oBACE3G,OAAA,CAAC5C,GAAG;IAACmM,CAAC,EAAE,CAAE;IAAAhD,QAAA,gBAERvG,OAAA,CAACxC,KAAK;MAAC8J,EAAE,EAAE;QACTT,EAAE,EAAE,CAAC;QAAE0C,CAAC,EAAE,CAAC;QAAEC,UAAU,EAAE,SAAS;QAAEC,SAAS,EAAE,MAAM;QACrDjB,OAAO,EAAE,MAAM;QAAEZ,UAAU,EAAE,QAAQ;QAAEyB,cAAc,EAAE,QAAQ;QAC/DK,MAAM,EAAE,mBAAmB;QAAEC,QAAQ,EAAE;MACzC,CAAE;MAAApD,QAAA,eACAvG,OAAA;QACE4J,GAAG,EAAE,0BAA0BrJ,MAAM,CAACwG,KAAK,IAAI,cAAc,EAAG;QAChE8C,KAAK,EAAC,iDAAiD;QACvDC,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEN,MAAM,EAAE;QAAO,CAAE;QACzD3C,KAAK,EAAE3G,CAAC,CAAC,cAAc;MAAE;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGR3G,OAAA,CAACxC,KAAK;MAAC8J,EAAE,EAAE;QAAEiC,CAAC,EAAE,CAAC;QAAE1C,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzBvG,OAAA,CAACpC,KAAK;QAAC+J,SAAS,EAAC,KAAK;QAACC,UAAU,EAAC,QAAQ;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,gBACpDvG,OAAA,CAACvC,IAAI;UAACqL,KAAK,EAAE,GAAG1I,CAAC,CAAC,SAAS,CAAC,MAAM,CAAAK,cAAc,aAAdA,cAAc,wBAAAN,qBAAA,GAAdM,cAAc,CAAEqG,OAAO,cAAA3G,qBAAA,uBAAvBA,qBAAA,CAAyB4G,KAAK,KAAI,EAAE,EAAG;UAACQ,KAAK,EAAC;QAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzF3G,OAAA,CAACtC,MAAM;UACLuM,SAAS,eAAEjK,OAAA,CAACd,aAAa;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BoB,OAAO,EAAEA,CAAA,KAAMjH,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9C+F,OAAO,EAAC,UAAU;UAClBkB,IAAI,EAAC,OAAO;UAAAvB,QAAA,EAEX1F,YAAY,GAAGT,CAAC,CAAC,eAAe,CAAC,GAAGA,CAAC,CAAC,eAAe;QAAC;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACT3G,OAAA,CAACtC,MAAM;UACLuM,SAAS,eAAEjK,OAAA,CAACZ,YAAY;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BoB,OAAO,EAAEA,CAAA,KAAMnG,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CiF,OAAO,EAAEjF,YAAY,GAAG,UAAU,GAAG,WAAY;UACjD4F,KAAK,EAAC,WAAW;UACjBO,IAAI,EAAC,OAAO;UAAAvB,QAAA,EAEX5E,YAAY,GAAGvB,CAAC,CAAC,cAAc,CAAC,GAAGA,CAAC,CAAC,cAAc;QAAC;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACR3G,OAAA,CAACrC,QAAQ;QAACqK,EAAE,EAAEnH,YAAa;QAAA0F,QAAA,gBACzBvG,OAAA,CAACjC,OAAO;UAACuJ,EAAE,EAAE;YAAE4C,EAAE,EAAE;UAAE;QAAE;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzBL,sBAAsB,CAAC,CAAC;MAAA;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGR3G,OAAA,CAAC5C,GAAG;MAACoL,OAAO,EAAC,MAAM;MAACvB,EAAE,EAAE,CAAE;MAAAV,QAAA,gBACxBvG,OAAA,CAAC1C,IAAI;QAAC6M,WAAW,EAAC,UAAU;QAACC,KAAK,EAAEzJ,GAAI;QAAC0J,QAAQ,EAAE5F,eAAgB;QAAC6C,EAAE,EAAE;UAAEgD,WAAW,EAAE,CAAC;UAAEnB,WAAW,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAA7C,QAAA,gBAChIvG,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAAC5B,eAAe;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,kBAAkB;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrF3G,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAAC1B,QAAQ;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,YAAY;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxE3G,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAACxB,QAAQ;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,WAAW;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvE3G,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAACtB,mBAAmB;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,YAAY,CAAE;UAAC2H,OAAO,EAAEA,CAAA,KAAMyC,QAAQ,CAAC,eAAelK,QAAQ,EAAE;QAAE;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvI3G,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAACZ,YAAY;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,mBAAmB;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClFhF,YAAY,iBACX3B,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAACZ,YAAY;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,UAAU;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC1E,eACD3G,OAAA,CAACzC,GAAG;UAACoL,IAAI,eAAE3I,OAAA,CAACZ,YAAY;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE1I,CAAC,CAAC,cAAc;QAAE;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eAEP3G,OAAA,CAAC5C,GAAG;QAACqN,IAAI,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAnE,QAAA,GAEjB5F,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAmJ,QAAA,gBACFvG,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,GAC5BnG,CAAC,CAAC,eAAe,CAAC,eACnBJ,OAAA,CAAClC,UAAU;cAACyJ,KAAK,EAAC,SAAS;cAACoD,SAAS,EAAC,OAAO;cAAApE,QAAA,gBAC3CvG,OAAA,CAACpB,qBAAqB;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB3G,OAAA;gBAAO+D,IAAI,EAAC,MAAM;gBAAC6G,MAAM,EAAC,SAAS;gBAACC,MAAM;gBAACR,QAAQ,EAAExF;cAAe;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACb3G,OAAA,CAACpC,KAAK;YAAC+J,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACa,QAAQ,EAAC,MAAM;YAAAnC,QAAA,EAC/CxF,aAAa,CAACoG,GAAG,CAAE2D,GAAG,iBACrB9K,OAAA;cAEE4J,GAAG,EAAEkB,GAAG,CAACjF,OAAQ;cACjBkF,GAAG,EAAC,EAAE;cACNjB,KAAK,EAAE;gBAAEkB,SAAS,EAAE,GAAG;gBAAEC,MAAM,EAAE,CAAC;gBAAE/B,MAAM,EAAE,SAAS;gBAAEgC,YAAY,EAAE,CAAC;gBAAEC,SAAS,EAAE;cAAiB,CAAE;cACtGpD,OAAO,EAAEA,CAAA,KAAM3G,cAAc,CAAC0J,GAAG,CAACjF,OAAO;YAAE,GAJtCiF,GAAG,CAACzK,EAAE;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKZ,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAER3G,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,GAC5BnG,CAAC,CAAC,eAAe,CAAC,eACnBJ,OAAA,CAAClC,UAAU;cAACyJ,KAAK,EAAC,SAAS;cAACoD,SAAS,EAAC,OAAO;cAAApE,QAAA,gBAC3CvG,OAAA,CAAClB,SAAS;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACb3G,OAAA;gBAAO+D,IAAI,EAAC,MAAM;gBAAC6G,MAAM,EAAC,SAAS;gBAACC,MAAM;gBAACR,QAAQ,EAAEnF;cAAe;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACb3G,OAAA,CAACpC,KAAK;YAAC+J,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACa,QAAQ,EAAC,MAAM;YAAAnC,QAAA,EAC/CtF,aAAa,CAACkG,GAAG,CAAEiE,GAAG,iBACrBpL,OAAA,CAAC5C,GAAG;cAAckK,EAAE,EAAE;gBAAEyC,KAAK,EAAE;cAAI,CAAE;cAAAxD,QAAA,eACnCvG,OAAA,CAAC9B,WAAW;gBAACmN,GAAG,EAAED,GAAG,CAACvF,OAAQ;gBAACyF,QAAQ;gBAACvB,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE;cAAI;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAD5DyE,GAAG,CAAC/K,EAAE;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAER3G,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,EAAEnG,CAAC,CAAC,cAAc;UAAC;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChE3G,OAAA,CAACnC,SAAS;YACR0N,SAAS;YAACC,SAAS;YAACC,OAAO,EAAE,CAAE;YAC/BC,WAAW,EAAEtL,CAAC,CAAC,kBAAkB,CAAE;YACnCgK,KAAK,EAAE7I,YAAa;YACpB8I,QAAQ,EAAG3F,CAAC,IAAKlD,eAAe,CAACkD,CAAC,CAACI,MAAM,CAACsF,KAAK,CAAE;YACjD9C,EAAE,EAAE;cAAE4C,EAAE,EAAE;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF3G,OAAA,CAACtC,MAAM;YAACuM,SAAS,eAAEjK,OAAA,CAAChB,QAAQ;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAACmB,OAAO,EAAE5C,iBAAkB;YAACwG,QAAQ,EAAElK,MAAO;YAAA8E,QAAA,EAC/F9E,MAAM,GAAGrB,CAAC,CAAC,QAAQ,CAAC,GAAGA,CAAC,CAAC,aAAa;UAAC;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGAhG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC3C,UAAU;UAACkK,KAAK,EAAC,gBAAgB;UAAAhB,QAAA,GAAC,eAAG,EAACnG,CAAC,CAAC,aAAa,CAAC;QAAA;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CACrE,EAGAhG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAmJ,QAAA,gBACFvG,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,GAAC,eAAG,EAACnG,CAAC,CAAC,aAAa,CAAC;UAAA;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClE3G,OAAA,CAACxC,KAAK;YAAC8J,EAAE,EAAE;cACTiC,CAAC,EAAE,CAAC;cAAE1C,EAAE,EAAE,CAAC;cAAEmE,SAAS,EAAE,GAAG;cAAEvB,SAAS,EAAE,GAAG;cAAEmC,SAAS,EAAE,MAAM;cAC9DlC,MAAM,EAAE,gBAAgB;cAAEwB,YAAY,EAAE,CAAC;cAAE1B,UAAU,EAAE;YACzD,CAAE;YAAAjD,QAAA,eACAvG,OAAA,CAACpC,KAAK;cAACiK,OAAO,EAAE,CAAE;cAAAtB,QAAA,GACf1E,YAAY,CAACsF,GAAG,CAAC,CAACvE,GAAG,EAAEiJ,CAAC;gBAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;gBAAA,oBACvBxM,OAAA,CAACxC,KAAK;kBAEJ8J,EAAE,EAAE;oBACFiC,CAAC,EAAE,CAAC;oBACJC,UAAU,EAAE,MAAM;oBAClBhB,OAAO,EAAE,MAAM;oBACfZ,UAAU,EAAE,YAAY;oBACxBf,EAAE,EAAE,CAAC;oBACL4B,GAAG,EAAE;kBACP,CAAE;kBAAAlC,QAAA,GAED,CAAAuF,WAAA,GAAAlJ,GAAG,CAAC6J,MAAM,cAAAX,WAAA,eAAVA,WAAA,CAAYY,UAAU,gBAEnB1M,OAAA;oBACE4J,GAAG,EACD,CAAAmC,YAAA,GAAAnJ,GAAG,CAAC6J,MAAM,cAAAV,YAAA,gBAAAC,qBAAA,GAAVD,YAAA,CAAYW,UAAU,cAAAV,qBAAA,eAAtBA,qBAAA,CAAwBW,UAAU,CAAC,MAAM,CAAC,GACtC/J,GAAG,CAAC6J,MAAM,CAACC,UAAU,GACrB,wBAAwB,EAAAT,YAAA,GAAArJ,GAAG,CAAC6J,MAAM,cAAAR,YAAA,uBAAVA,YAAA,CAAYS,UAAU,KAAI,2BAA2B,EAClF;oBACD3B,GAAG,GAAAmB,YAAA,GAAEtJ,GAAG,CAAC6J,MAAM,cAAAP,YAAA,uBAAVA,YAAA,CAAYU,IAAK;oBACtB9C,KAAK,EAAE;sBAAEC,KAAK,EAAE,EAAE;sBAAEC,MAAM,EAAE,EAAE;sBAAEkB,YAAY,EAAE,KAAK;sBAAE2B,WAAW,EAAE;oBAAE;kBAAE;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,gBAEF3G,OAAA,CAACP,MAAM;oBAAC6H,EAAE,EAAE;sBAAEyC,KAAK,EAAE,EAAE;sBAAEC,MAAM,EAAE,EAAE;sBAAE6C,WAAW,EAAE;oBAAE,CAAE;oBAAAtG,QAAA,EACnD,EAAA4F,YAAA,GAAAvJ,GAAG,CAAC6J,MAAM,cAAAN,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYS,IAAI,cAAAR,iBAAA,wBAAAC,kBAAA,GAAhBD,iBAAA,CAAmB,CAAC,CAAC,cAAAC,kBAAA,uBAArBA,kBAAA,CAAuBS,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAtG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACT,eAGH3G,OAAA,CAAC5C,GAAG;oBAAAmJ,QAAA,gBACFvG,OAAA,CAAC3C,UAAU;sBAACuJ,OAAO,EAAC,WAAW;sBAACS,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAhB,QAAA,GAC9D,EAAA+F,YAAA,GAAA1J,GAAG,CAAC6J,MAAM,cAAAH,YAAA,uBAAVA,YAAA,CAAYM,IAAI,KAAIxM,CAAC,CAAC,WAAW,CAAC,EAClC,EAAAmM,YAAA,GAAA3J,GAAG,CAAC6J,MAAM,cAAAF,YAAA,uBAAVA,YAAA,CAAYQ,IAAI,kBACf/M,OAAA;wBAAM8J,KAAK,EAAE;0BAAEvC,KAAK,EAAE,MAAM;0BAAEF,UAAU,EAAE,GAAG;0BAAE2F,UAAU,EAAE,CAAC;0BAAEpE,QAAQ,EAAE;wBAAG,CAAE;wBAAArC,QAAA,GAAC,OAC1E,EAAC3D,GAAG,CAAC6J,MAAM,CAACM,IAAI;sBAAA;wBAAAvG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CACP,EACA/D,GAAG,CAACqK,SAAS,iBACZjN,OAAA;wBAAM8J,KAAK,EAAE;0BAAEvC,KAAK,EAAE,MAAM;0BAAEqB,QAAQ,EAAE,EAAE;0BAAEoE,UAAU,EAAE;wBAAE,CAAE;wBAAAzG,QAAA,EACzD,IAAI2G,IAAI,CAACtK,GAAG,CAACqK,SAAS,CAAC,CAACE,kBAAkB,CAAC,EAAE,EAAE;0BAAEC,IAAI,EAAE,SAAS;0BAAEC,MAAM,EAAE;wBAAU,CAAC;sBAAC;wBAAA7G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CACP,EAAE,EAAA6F,YAAA,GAAA5J,GAAG,CAAC6J,MAAM,cAAAD,YAAA,uBAAVA,YAAA,CAAYnM,EAAE,MAAK2F,IAAI,CAAC3F,EAAE,iBAC3BL,OAAA,CAAClC,UAAU;wBAACgK,IAAI,EAAC,OAAO;wBAACC,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAACtD,GAAG,CAACvC,EAAE,CAAE;wBAACkH,KAAK,EAAC,OAAO;wBAAAhB,QAAA,eAC5EvG,OAAA,CAACN,UAAU;0BAACkJ,QAAQ,EAAC;wBAAO;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC,EAGZ/D,GAAG,CAACmB,IAAI,KAAK,MAAM,iBAAI/D,OAAA;sBAAAuG,QAAA,EAAO3D,GAAG,CAACgD;oBAAO;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACjD/D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnB/D,OAAA;sBAAK4J,GAAG,EAAEhH,GAAG,CAACgD,OAAQ;sBAACmF,GAAG,EAAC,KAAK;sBAACjB,KAAK,EAAE;wBAAEwD,QAAQ,EAAE,GAAG;wBAAEpC,YAAY,EAAE,CAAC;wBAAEqC,SAAS,EAAE;sBAAE;oBAAE;sBAAA/G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC5F,EACA/D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnB/D,OAAA;sBAAOsL,QAAQ;sBAAC1B,GAAG,EAAEhH,GAAG,CAACgD,OAAQ;sBAACkE,KAAK,EAAE;wBAAEwD,QAAQ,EAAE,GAAG;wBAAEC,SAAS,EAAE;sBAAE;oBAAE;sBAAA/G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC5E,EACA/D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnB/D,OAAA;sBAAOsL,QAAQ;sBAAC1B,GAAG,EAAEhH,GAAG,CAACgD,OAAQ;sBAACkE,KAAK,EAAE;wBAAEwD,QAAQ,EAAE,GAAG;wBAAEpC,YAAY,EAAE,CAAC;wBAAEqC,SAAS,EAAE;sBAAE;oBAAE;sBAAA/G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC7F,EACA/D,GAAG,CAACmB,IAAI,KAAK,MAAM,iBAClB/D,OAAA;sBAAGwN,IAAI,EAAE5K,GAAG,CAACgD,OAAQ;sBAACd,MAAM,EAAC,QAAQ;sBAAC2I,GAAG,EAAC,qBAAqB;sBAAC3D,KAAK,EAAE;wBAAEtB,OAAO,EAAE,OAAO;wBAAE+E,SAAS,EAAE;sBAAE,CAAE;sBAAAhH,QAAA,GAAC,eACtG,EAAC3D,GAAG,CAACgD,OAAO,CAAC8H,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;oBAAA;sBAAAnH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA/DDkF,CAAC;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgED,CAAC;cAAA,CACT,CAAC,eACF3G,OAAA;gBAAK4N,GAAG,EAAEvL;cAAc;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACR3G,OAAA,CAACpC,KAAK;YAAC+J,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACD,UAAU,EAAC,QAAQ;YAAArB,QAAA,gBACpDvG,OAAA,CAACnC,SAAS;cACR0N,SAAS;cACTnB,KAAK,EAAErI,MAAO;cACd+F,IAAI,EAAC,OAAO;cACZ4D,WAAW,EAAEtL,CAAC,CAAC,cAAc,CAAE;cAC/BiK,QAAQ,EAAG3F,CAAC,IAAK1C,SAAS,CAAC0C,CAAC,CAACI,MAAM,CAACsF,KAAK,CAAE;cAC3CyD,SAAS,EAAGnJ,CAAC,IAAKA,CAAC,CAACoJ,GAAG,KAAK,OAAO,IAAInI,cAAc,CAAC,CAAE;cACxD2B,EAAE,EAAE;gBAAEkC,UAAU,EAAE,MAAM;gBAAE0B,YAAY,EAAE;cAAE;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACF3G,OAAA,CAAClC,UAAU;cAACiK,OAAO,EAAEA,CAAA,KAAM7F,YAAY,CAAE6L,CAAC,IAAK,CAACA,CAAC,CAAE;cAAAxH,QAAA,eACjDvG,OAAA;gBAAM+M,IAAI,EAAC,KAAK;gBAAC,cAAW,OAAO;gBAAAxG,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACb3G,OAAA,CAAClC,UAAU;cAAC6M,SAAS,EAAC,OAAO;cAACpD,KAAK,EAAEpF,OAAO,GAAG,SAAS,GAAG,SAAU;cAAAoE,QAAA,gBACnEvG,OAAA,CAACpB,qBAAqB;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB3G,OAAA;gBACE6K,MAAM;gBACN9G,IAAI,EAAC,MAAM;gBACX6G,MAAM,EAAC,yCAAyC;gBAChDP,QAAQ,EAAG3F,CAAC,IAAKtC,UAAU,CAACsC,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACb3G,OAAA,CAACtC,MAAM;cAACqK,OAAO,EAAEpC,cAAe;cAACiB,OAAO,EAAC,WAAW;cAAC+E,QAAQ,EAAE,CAAC5J,MAAM,CAACkE,IAAI,CAAC,CAAC,IAAI,CAAC9D,OAAQ;cAAAoE,QAAA,EACvFnG,CAAC,CAAC,MAAM;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACP1E,SAAS,iBACRjC,OAAA,CAAC5C,GAAG;YAACkK,EAAE,EAAE;cAAE0G,QAAQ,EAAE,UAAU;cAAEC,MAAM,EAAE;YAAG,CAAE;YAAA1H,QAAA,eAC5CvG,OAAA,CAACR,WAAW;cAAC0O,YAAY,EAAEzI,WAAY;cAAC0I,eAAe,EAAE;YAAM;cAAA3H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACN,EACAxE,OAAO,iBACNnC,OAAA,CAAC3C,UAAU;YAACkK,KAAK,EAAC,SAAS;YAACqB,QAAQ,EAAE,EAAG;YAAC5B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,GAAI;YAAAV,QAAA,GACtDnG,CAAC,CAAC,WAAW,CAAC,EAAC,IAAE,EAAC+B,OAAO,CAACyK,IAAI;UAAA;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAhG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAmJ,QAAA,gBACFvG,OAAA,CAAC3C,UAAU;YAACuJ,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,EAAEnG,CAAC,CAAC,mBAAmB;UAAC;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrE3G,OAAA,CAACH,iBAAiB;YAACS,QAAQ,EAAEA;UAAS;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACN,EAGAhF,YAAY,IAAIhB,GAAG,KAAK,CAAC,iBACxBX,OAAA,CAAC5C,GAAG;UAAAmJ,QAAA,gBACFvG,OAAA,CAACpC,KAAK;YAAC+J,SAAS,EAAC,KAAK;YAACC,UAAU,EAAC,QAAQ;YAACC,OAAO,EAAE,CAAE;YAAChB,EAAE,EAAE,CAAE;YAAAN,QAAA,eAC3DvG,OAAA,CAAC3C,UAAU;cAACuJ,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,eAAG,EAACnG,CAAC,CAAC,iBAAiB,CAAC;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACR3G,OAAA,CAACJ,iBAAiB;YAACU,QAAQ,EAAEA;UAAS;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACN,EAGAhG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAACF,kBAAkB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACtB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxF,WAAW,iBACVnB,OAAA,CAAC5C,GAAG;MACF2K,OAAO,EAAEA,CAAA,KAAM3G,cAAc,CAAC,IAAI,CAAE;MACpCkG,EAAE,EAAE;QACF0G,QAAQ,EAAE,OAAO;QAAEI,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEJ,MAAM,EAAE,IAAI;QAAElE,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE,OAAO;QACjFR,UAAU,EAAE,kBAAkB;QAAEhB,OAAO,EAAE,MAAM;QAAEZ,UAAU,EAAE,QAAQ;QACrEyB,cAAc,EAAE,QAAQ;QAAEH,MAAM,EAAE;MACpC,CAAE;MAAA3C,QAAA,eAEFvG,OAAA;QACE4J,GAAG,EAAEzI,WAAY;QACjB4J,GAAG,EAAC,EAAE;QACNjB,KAAK,EAAE;UAAEwD,QAAQ,EAAE,MAAM;UAAEtC,SAAS,EAAE,MAAM;UAAEE,YAAY,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAkB,CAAE;QAC/FpD,OAAO,EAAGrD,CAAC,IAAKA,CAAC,CAAC4J,eAAe,CAAC;MAAE;QAAA9H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzG,EAAA,CApiBID,gBAAgB;EAAA,QACNjC,cAAc,EACHb,SAAS;AAAA;AAAAoR,EAAA,GAF9BtO,gBAAgB;AAsiBtB,eAAeA,gBAAgB;AAAC,IAAAsO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}