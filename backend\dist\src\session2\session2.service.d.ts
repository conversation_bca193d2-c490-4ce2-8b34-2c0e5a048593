import { PrismaService } from 'nestjs-prisma';
import { $Enums } from '@prisma/client';
export declare class Session2Service {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: any, file?: Express.Multer.File): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: $Enums.Session2Status;
    }>;
    findAll(): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: $Enums.Session2Status;
    }[]>;
}
