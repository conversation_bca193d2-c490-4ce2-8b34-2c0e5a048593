import { PrismaService } from 'nestjs-prisma';
import { $Enums } from '@prisma/client';
export declare class Session2Service {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: any, file?: Express.Multer.File): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: $Enums.Session2Status;
    }>;
    findAll(): Promise<{
        averageFeedbackRating: number | null;
        program: {
            id: number;
            name: string;
            createdAt: Date;
            published: boolean;
        };
        session2Modules: ({
            module: {
                id: number;
                name: string;
                periodUnit: $Enums.PeriodUnit;
                duration: number;
            };
            courses: ({
                course: {
                    id: number;
                    title: string;
                };
                contenus: ({
                    contenu: {
                        id: number;
                        published: boolean;
                        title: string;
                        fileUrl: string | null;
                        fileType: $Enums.FileType | null;
                        type: $Enums.ContenuType;
                        coursAssocie: string | null;
                    };
                } & {
                    id: number;
                    session2CourseId: number;
                    contenuId: number;
                })[];
            } & {
                id: number;
                session2ModuleId: number;
                courseId: number;
            })[];
        } & {
            id: number;
            session2Id: number;
            moduleId: number;
        })[];
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: $Enums.Session2Status;
    }[]>;
    addUserToSession(session2Id: number, email: string): Promise<{
        message: string;
    }>;
    getUsersForSession(session2Id: number): Promise<{
        id: number;
        name: string | null;
        role: $Enums.Role;
        email: string;
        profilePic: string | null;
    }[]>;
    removeUserFromSession(session2Id: number, userId: number): Promise<import(".prisma/client").Prisma.BatchPayload>;
    updateStatus(id: number, status: string): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: $Enums.Session2Status;
    }>;
}
