{"version": 3, "file": "session2.service.js", "sourceRoot": "", "sources": ["../../../src/session2/session2.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,iDAA8C;AAC9C,2CAAwE;AAKjE,IAAM,eAAe,GAArB,MAAM,eAAe;IACG;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,MAAM,CAAC,IAAS,EAAE,IAA0B;QAChD,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAE1D,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QAE/F,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBAChE,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;gBACvC,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,MAAM,EAAE,IAAI;4BACZ,OAAO,EAAE;gCACP,OAAO,EAAE;oCACP,MAAM,EAAE,IAAI;oCACZ,QAAQ,EAAE;wCACR,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;qCAC3B;iCACF;6BACF;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC;YACjF,IAAI,gBAAgB,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,gBAAgB,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;YAC5F,CAAC;YAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,+DAA+D,EAAE,SAAS,CAAC,CAAC;gBAC1F,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACjD,IAAI,EAAE;oBACJ,IAAI;oBACJ,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;oBAC5B,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;oBACtD,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;oBAChD,QAAQ,EAAE,IAAI;wBACZ,CAAC,CAAC,0CAA0C,IAAI,CAAC,QAAQ,EAAE;wBAC3D,CAAC,CAAC,SAAS;iBACd;aACF,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE3E,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YACpE,KAAK,MAAM,GAAG,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACrE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;oBACpD,IAAI,EAAE;wBACJ,UAAU,EAAE,QAAQ,CAAC,EAAE;wBACvB,QAAQ,EAAE,GAAG,CAAC,QAAQ;qBACvB;iBACF,CAAC,CAAC;gBAEH,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;oBACjC,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACxE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;wBACvD,IAAI,EAAE;4BACJ,gBAAgB,EAAE,KAAK,CAAC,EAAE;4BAC1B,QAAQ,EAAE,MAAM,CAAC,QAAQ;yBAC1B;qBACF,CAAC,CAAC;oBAEH,KAAK,MAAM,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;wBACjC,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;wBACtE,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;4BACvC,IAAI,EAAE;gCACJ,gBAAgB,EAAE,QAAQ,CAAC,EAAE;gCAC7B,SAAS,EAAE,EAAE,CAAC,SAAS;6BACxB;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;YACnF,OAAO,EAAE,OAAO,EAAE,oDAAoD,EAAE,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACjC,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACnD,OAAO,EAAE;gBACP,OAAO,EAAE,IAAI;gBACb,eAAe,EAAE;oBACf,OAAO,EAAE;wBACP,MAAM,EAAE,IAAI;wBACZ,OAAO,EAAE;4BACP,OAAO,EAAE;gCACP,MAAM,EAAE,IAAI;gCACZ,QAAQ,EAAE;oCACR,OAAO,EAAE;wCACP,OAAO,EAAE,IAAI;qCACd;iCACF;6BACF;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAGH,MAAM,2BAA2B,GAAG,MAAM,OAAO,CAAC,GAAG,CACnD,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAC3D,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;gBAChC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;aACzB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,EAAE,qBAAqB,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAE1F,IAAI,aAAa,GAAkB,IAAI,CAAC;YACxC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAClF,aAAa,GAAG,UAAU,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxE,OAAO,CAAC,GAAG,CAAC,uCAAuC,OAAO,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;YACnF,CAAC;YAED,OAAO;gBACL,GAAG,OAAO;gBACV,qBAAqB,EAAE,aAAa;aACrC,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;QAGF,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,KAAa;QACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACrE,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QAElE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACvD,KAAK,EAAE,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE;SAC9D,CAAC,CAAC;QACH,IAAI,MAAM;YAAE,MAAM,IAAI,4BAAmB,CAAC,kCAAkC,CAAC,CAAC;QAE9E,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACpC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE;SACtC,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC1D,CAAC;IAEH,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QAGzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACvD,KAAK,EAAE,EAAE,UAAU,EAAE;YACrB,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,UAAU,EAAE,IAAI;wBAChB,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAGD,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,MAAc;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACzC,KAAK,EAAE;gBACL,UAAU;gBACV,MAAM;aACP;SACF,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,MAAc;QAC3C,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,4BAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACjC,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,MAA+B,EAAE,EAAE;SAC3D,CAAC,CAAC;IACL,CAAC;CACA,CAAA;AA9MY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAE0B,6BAAa;GADvC,eAAe,CA8M3B"}