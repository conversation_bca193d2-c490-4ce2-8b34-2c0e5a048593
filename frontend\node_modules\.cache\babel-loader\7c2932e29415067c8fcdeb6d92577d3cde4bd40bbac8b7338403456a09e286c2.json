{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\EditQuizForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Button, TextField, Typography, IconButton, Paper, Stack, Select, MenuItem, FormControl, InputLabel, CircularProgress } from \"@mui/material\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport ClearIcon from \"@mui/icons-material/Clear\";\nimport axios from \"axios\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditQuizForm = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const params = useParams();\n  const navigate = useNavigate();\n  const {\n    contenuId\n  } = params;\n  const [loading, setLoading] = useState(true);\n  const [timeLimitMinutes, setTimeLimitMinutes] = useState(5);\n  const [questions, setQuestions] = useState([]);\n\n  // Charger le quiz existant\n  useEffect(() => {\n    const loadQuiz = async () => {\n      try {\n        const response = await axios.get(`http://localhost:8000/quizzes/by-contenu/${contenuId}`);\n        const quiz = response.data;\n        if (quiz) {\n          setTimeLimitMinutes(Math.floor(quiz.timeLimit / 60));\n          setQuestions(quiz.questions || []);\n        }\n      } catch (error) {\n        console.error('Erreur lors du chargement du quiz:', error);\n        alert(t('quiz.loadError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadQuiz();\n  }, [contenuId, t]);\n  const addQuestion = () => {\n    setQuestions([...questions, {\n      text: \"\",\n      type: \"MCQ\",\n      score: 1,\n      imageUrl: \"\",\n      correctText: \"\",\n      choices: [{\n        text: \"\",\n        isCorrect: false\n      }]\n    }]);\n  };\n  const updateQuestion = (index, field, value) => {\n    const updated = [...questions];\n    updated[index][field] = value;\n    if (field === \"type\") {\n      if (value === \"TRUE_FALSE\") {\n        updated[index].choices = [{\n          text: t('quiz.true'),\n          isCorrect: false\n        }, {\n          text: t('quiz.false'),\n          isCorrect: false\n        }];\n      } else if (value === \"IMAGE_CHOICE\") {\n        updated[index].choices = [{\n          text: \"\",\n          imageUrl: \"\",\n          isCorrect: false\n        }, {\n          text: \"\",\n          imageUrl: \"\",\n          isCorrect: false\n        }];\n      } else {\n        updated[index].choices = [{\n          text: \"\",\n          isCorrect: false\n        }];\n      }\n    }\n    setQuestions(updated);\n  };\n  const updateChoiceText = (qIndex, cIndex, text) => {\n    const updated = [...questions];\n    updated[qIndex].choices[cIndex].text = text;\n    setQuestions(updated);\n  };\n  const updateChoiceImage = async (qIndex, cIndex) => {\n    const input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.accept = \"image/*\";\n    input.onchange = async e => {\n      const file = e.target.files[0];\n      if (!file) return;\n      const formData = new FormData();\n      formData.append(\"file\", file);\n      try {\n        const res = await axios.post(\"http://localhost:8000/quizzes/upload-question-image\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\"\n          }\n        });\n        const updated = [...questions];\n        updated[qIndex].choices[cIndex].imageUrl = res.data.imageUrl;\n        setQuestions(updated);\n      } catch (err) {\n        alert(t('quiz.imageUploadError'));\n      }\n    };\n    input.click();\n  };\n  const updateChoiceCorrect = (qIndex, cIndex) => {\n    const updated = [...questions];\n    updated[qIndex].choices = updated[qIndex].choices.map((c, i) => ({\n      ...c,\n      isCorrect: i === cIndex\n    }));\n    setQuestions(updated);\n  };\n  const addChoice = qIndex => {\n    const updated = [...questions];\n    updated[qIndex].choices.push({\n      text: \"\",\n      isCorrect: false\n    });\n    setQuestions(updated);\n  };\n  const removeChoice = (qIndex, cIndex) => {\n    const updated = [...questions];\n    updated[qIndex].choices.splice(cIndex, 1);\n    setQuestions(updated);\n  };\n  const removeQuestion = index => {\n    const updated = [...questions];\n    updated.splice(index, 1);\n    setQuestions(updated);\n  };\n  const handleImageUpload = async qIndex => {\n    const input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.accept = \"image/*\";\n    input.onchange = async e => {\n      const file = e.target.files[0];\n      if (!file) return;\n      const formData = new FormData();\n      formData.append(\"file\", file);\n      try {\n        const res = await axios.post(\"http://localhost:8000/quizzes/upload-question-image\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\"\n          }\n        });\n        const updated = [...questions];\n        updated[qIndex].imageUrl = res.data.imageUrl;\n        setQuestions(updated);\n      } catch (err) {\n        alert(t('quiz.imageUploadError'));\n      }\n    };\n    input.click();\n  };\n  const handleSubmit = async () => {\n    // ✅ Validate first\n    for (const [i, q] of questions.entries()) {\n      if (!q.text.trim()) {\n        alert(`❌ ${t('quiz.questionRequired')} ${i + 1}.`);\n        return;\n      }\n      if (q.type !== \"FILL_BLANK\" && !q.choices.some(c => c.isCorrect)) {\n        alert(`❌ ${t('quiz.correctAnswerRequired')} ${i + 1}.`);\n        return;\n      }\n      if (q.type !== \"FILL_BLANK\" && q.choices.some(c => !c.text.trim() && !c.imageUrl)) {\n        alert(`❌ ${t('quiz.choiceTextRequired')} ${i + 1}.`);\n        return;\n      }\n      if (q.type === \"FILL_BLANK\" && !q.correctText.trim()) {\n        alert(`❌ ${t('quiz.fillBlankAnswerRequired')} ${i + 1}.`);\n        return;\n      }\n    }\n\n    // ✅ Submit after validation\n    try {\n      await axios.patch(`http://localhost:8000/quizzes/by-contenu/${contenuId}`, {\n        contenuId: parseInt(contenuId),\n        timeLimit: timeLimitMinutes * 60,\n        questions\n      });\n      alert(t('quiz.quizUpdatedSuccess'));\n      navigate(\"/contenus\");\n    } catch (err) {\n      console.error(t('quiz.submitError'), err);\n      alert(t('quiz.submitError'));\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: [\"\\u270F\\uFE0F \", t('quiz.editQuiz')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          type: \"number\",\n          label: t('quiz.timeLimit'),\n          value: timeLimitMinutes,\n          onChange: e => setTimeLimitMinutes(parseInt(e.target.value) || 0),\n          sx: {\n            width: 150\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: t('quiz.minutes')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), questions.map((question, qIndex) => /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [t('quiz.question'), \" \", qIndex + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => removeQuestion(qIndex),\n          color: \"error\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: t('quiz.questionText'),\n          value: question.text,\n          onChange: e => updateQuestion(qIndex, \"text\", e.target.value),\n          multiline: true,\n          rows: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: t('quiz.questionType')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: question.type,\n            onChange: e => updateQuestion(qIndex, \"type\", e.target.value),\n            label: t('quiz.questionType'),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"MCQ\",\n              children: t('quiz.multipleChoice')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"TRUE_FALSE\",\n              children: t('quiz.trueFalse')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"FILL_BLANK\",\n              children: t('quiz.fillBlank')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"IMAGE_CHOICE\",\n              children: t('quiz.imageChoice')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), question.type === \"FILL_BLANK\" ? /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: t('quiz.correctAnswer'),\n          value: question.correctText,\n          onChange: e => updateQuestion(qIndex, \"correctText\", e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [t('quiz.choices'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this), question.choices.map((choice, cIndex) => /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            alignItems: \"center\",\n            mb: 1,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: choice.isCorrect ? \"contained\" : \"outlined\",\n              onClick: () => updateChoiceCorrect(qIndex, cIndex),\n              size: \"small\",\n              children: choice.isCorrect ? \"✓\" : \"○\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              value: choice.text,\n              onChange: e => updateChoiceText(qIndex, cIndex, e.target.value),\n              placeholder: t('quiz.choiceText'),\n              sx: {\n                flexGrow: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 21\n            }, this), question.type === \"IMAGE_CHOICE\" && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: () => updateChoiceImage(qIndex, cIndex),\n              size: \"small\",\n              children: choice.imageUrl ? t('quiz.changeImage') : t('quiz.addImage')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 23\n            }, this), question.choices.length > 1 && /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => removeChoice(qIndex, cIndex),\n              color: \"error\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 23\n            }, this)]\n          }, cIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 19\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => addChoice(qIndex),\n            size: \"small\",\n            sx: {\n              mt: 1\n            },\n            children: [\"+ \", t('quiz.addChoice')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this), question.type !== \"IMAGE_CHOICE\" && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => handleImageUpload(qIndex),\n          size: \"small\",\n          children: question.imageUrl ? t('quiz.changeQuestionImage') : t('quiz.addQuestionImage')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)]\n    }, qIndex, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: addQuestion,\n        children: [\"+ \", t('quiz.addQuestion')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: () => navigate(\"/contenus\"),\n        children: t('common.cancel')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"primary\",\n      onClick: handleSubmit,\n      disabled: questions.length === 0,\n      size: \"large\",\n      children: [\"\\uD83D\\uDCBE \", t('quiz.updateQuiz')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n};\n_s(EditQuizForm, \"Wq8D6sNjl0MYMH+wre72+JiuwAU=\", false, function () {\n  return [useTranslation, useParams, useNavigate];\n});\n_c = EditQuizForm;\nexport default EditQuizForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuizForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "TextField", "Typography", "IconButton", "Paper", "<PERSON><PERSON>", "Select", "MenuItem", "FormControl", "InputLabel", "CircularProgress", "DeleteIcon", "ClearIcon", "axios", "useParams", "useNavigate", "useTranslation", "jsxDEV", "_jsxDEV", "EditQuizForm", "_s", "t", "params", "navigate", "contenuId", "loading", "setLoading", "timeLimitMinutes", "setTimeLimitMinutes", "questions", "setQuestions", "loadQuiz", "response", "get", "quiz", "data", "Math", "floor", "timeLimit", "error", "console", "alert", "addQuestion", "text", "type", "score", "imageUrl", "correctText", "choices", "isCorrect", "updateQuestion", "index", "field", "value", "updated", "updateChoiceText", "qIndex", "cIndex", "updateChoiceImage", "input", "document", "createElement", "accept", "onchange", "e", "file", "target", "files", "formData", "FormData", "append", "res", "post", "headers", "err", "click", "updateChoiceCorrect", "map", "c", "i", "addChoice", "push", "removeChoice", "splice", "removeQuestion", "handleImageUpload", "handleSubmit", "q", "entries", "trim", "some", "patch", "parseInt", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "variant", "gutterBottom", "mb", "direction", "spacing", "label", "onChange", "width", "color", "question", "onClick", "size", "fullWidth", "multiline", "rows", "choice", "placeholder", "flexGrow", "length", "mt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/EditQuizForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  TextField,\r\n  Typography,\r\n  IconButton,\r\n  Paper,\r\n  Stack,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\nimport axios from \"axios\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst EditQuizForm = () => {\r\n  const { t } = useTranslation();\r\n  const params = useParams();\r\n  const navigate = useNavigate();\r\n  const { contenuId } = params;\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [timeLimitMinutes, setTimeLimitMinutes] = useState(5);\r\n  const [questions, setQuestions] = useState([]);\r\n\r\n  // Charger le quiz existant\r\n  useEffect(() => {\r\n    const loadQuiz = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:8000/quizzes/by-contenu/${contenuId}`);\r\n        const quiz = response.data;\r\n        \r\n        if (quiz) {\r\n          setTimeLimitMinutes(Math.floor(quiz.timeLimit / 60));\r\n          setQuestions(quiz.questions || []);\r\n        }\r\n      } catch (error) {\r\n        console.error('Erreur lors du chargement du quiz:', error);\r\n        alert(t('quiz.loadError'));\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadQuiz();\r\n  }, [contenuId, t]);\r\n\r\n  const addQuestion = () => {\r\n    setQuestions([\r\n      ...questions,\r\n      {\r\n        text: \"\",\r\n        type: \"MCQ\",\r\n        score: 1,\r\n        imageUrl: \"\",\r\n        correctText: \"\",\r\n        choices: [{ text: \"\", isCorrect: false }],\r\n      },\r\n    ]);\r\n  };\r\n\r\n  const updateQuestion = (index, field, value) => {\r\n    const updated = [...questions];\r\n    updated[index][field] = value;\r\n\r\n    if (field === \"type\") {\r\n      if (value === \"TRUE_FALSE\") {\r\n        updated[index].choices = [\r\n          { text: t('quiz.true'), isCorrect: false },\r\n          { text: t('quiz.false'), isCorrect: false },\r\n        ];\r\n      } else if (value === \"IMAGE_CHOICE\") {\r\n        updated[index].choices = [\r\n          { text: \"\", imageUrl: \"\", isCorrect: false },\r\n          { text: \"\", imageUrl: \"\", isCorrect: false },\r\n        ];\r\n      } else {\r\n        updated[index].choices = [{ text: \"\", isCorrect: false }];\r\n      }\r\n    }\r\n\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const updateChoiceText = (qIndex, cIndex, text) => {\r\n    const updated = [...questions];\r\n    updated[qIndex].choices[cIndex].text = text;\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const updateChoiceImage = async (qIndex, cIndex) => {\r\n    const input = document.createElement(\"input\");\r\n    input.type = \"file\";\r\n    input.accept = \"image/*\";\r\n    input.onchange = async (e) => {\r\n      const file = e.target.files[0];\r\n      if (!file) return;\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      try {\r\n        const res = await axios.post(\r\n          \"http://localhost:8000/quizzes/upload-question-image\",\r\n          formData,\r\n          { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n        );\r\n        const updated = [...questions];\r\n        updated[qIndex].choices[cIndex].imageUrl = res.data.imageUrl;\r\n        setQuestions(updated);\r\n      } catch (err) {\r\n        alert(t('quiz.imageUploadError'));\r\n      }\r\n    };\r\n    input.click();\r\n  };\r\n\r\n  const updateChoiceCorrect = (qIndex, cIndex) => {\r\n    const updated = [...questions];\r\n    updated[qIndex].choices = updated[qIndex].choices.map((c, i) => ({\r\n      ...c,\r\n      isCorrect: i === cIndex,\r\n    }));\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const addChoice = (qIndex) => {\r\n    const updated = [...questions];\r\n    updated[qIndex].choices.push({ text: \"\", isCorrect: false });\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const removeChoice = (qIndex, cIndex) => {\r\n    const updated = [...questions];\r\n    updated[qIndex].choices.splice(cIndex, 1);\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const removeQuestion = (index) => {\r\n    const updated = [...questions];\r\n    updated.splice(index, 1);\r\n    setQuestions(updated);\r\n  };\r\n\r\n  const handleImageUpload = async (qIndex) => {\r\n    const input = document.createElement(\"input\");\r\n    input.type = \"file\";\r\n    input.accept = \"image/*\";\r\n    input.onchange = async (e) => {\r\n      const file = e.target.files[0];\r\n      if (!file) return;\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      try {\r\n        const res = await axios.post(\r\n          \"http://localhost:8000/quizzes/upload-question-image\",\r\n          formData,\r\n          { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n        );\r\n        const updated = [...questions];\r\n        updated[qIndex].imageUrl = res.data.imageUrl;\r\n        setQuestions(updated);\r\n      } catch (err) {\r\n        alert(t('quiz.imageUploadError'));\r\n      }\r\n    };\r\n    input.click();\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    // ✅ Validate first\r\n    for (const [i, q] of questions.entries()) {\r\n      if (!q.text.trim()) {\r\n        alert(`❌ ${t('quiz.questionRequired')} ${i + 1}.`);\r\n        return;\r\n      }\r\n\r\n      if (q.type !== \"FILL_BLANK\" && !q.choices.some((c) => c.isCorrect)) {\r\n        alert(`❌ ${t('quiz.correctAnswerRequired')} ${i + 1}.`);\r\n        return;\r\n      }\r\n\r\n      if (q.type !== \"FILL_BLANK\" && q.choices.some((c) => !c.text.trim() && !c.imageUrl)) {\r\n        alert(`❌ ${t('quiz.choiceTextRequired')} ${i + 1}.`);\r\n        return;\r\n      }\r\n\r\n      if (q.type === \"FILL_BLANK\" && !q.correctText.trim()) {\r\n        alert(`❌ ${t('quiz.fillBlankAnswerRequired')} ${i + 1}.`);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // ✅ Submit after validation\r\n    try {\r\n      await axios.patch(`http://localhost:8000/quizzes/by-contenu/${contenuId}`, {\r\n        contenuId: parseInt(contenuId),\r\n        timeLimit: timeLimitMinutes * 60,\r\n        questions,\r\n      });\r\n\r\n      alert(t('quiz.quizUpdatedSuccess'));\r\n      navigate(\"/contenus\");\r\n    } catch (err) {\r\n      console.error(t('quiz.submitError'), err);\r\n      alert(t('quiz.submitError'));\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ p: 3 }}>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        ✏️ {t('quiz.editQuiz')}\r\n      </Typography>\r\n\r\n      <Paper sx={{ p: 3, mb: 3 }}>\r\n        <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n          <TextField\r\n            type=\"number\"\r\n            label={t('quiz.timeLimit')}\r\n            value={timeLimitMinutes}\r\n            onChange={(e) => setTimeLimitMinutes(parseInt(e.target.value) || 0)}\r\n            sx={{ width: 150 }}\r\n          />\r\n          <Typography variant=\"body2\" color=\"text.secondary\">\r\n            {t('quiz.minutes')}\r\n          </Typography>\r\n        </Stack>\r\n      </Paper>\r\n\r\n      {questions.map((question, qIndex) => (\r\n        <Paper key={qIndex} sx={{ p: 3, mb: 3 }}>\r\n          <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n            <Typography variant=\"h6\">\r\n              {t('quiz.question')} {qIndex + 1}\r\n            </Typography>\r\n            <IconButton\r\n              onClick={() => removeQuestion(qIndex)}\r\n              color=\"error\"\r\n              size=\"small\"\r\n            >\r\n              <DeleteIcon />\r\n            </IconButton>\r\n          </Stack>\r\n\r\n          <Stack spacing={2}>\r\n            <TextField\r\n              fullWidth\r\n              label={t('quiz.questionText')}\r\n              value={question.text}\r\n              onChange={(e) => updateQuestion(qIndex, \"text\", e.target.value)}\r\n              multiline\r\n              rows={2}\r\n            />\r\n\r\n            <FormControl fullWidth>\r\n              <InputLabel>{t('quiz.questionType')}</InputLabel>\r\n              <Select\r\n                value={question.type}\r\n                onChange={(e) => updateQuestion(qIndex, \"type\", e.target.value)}\r\n                label={t('quiz.questionType')}\r\n              >\r\n                <MenuItem value=\"MCQ\">{t('quiz.multipleChoice')}</MenuItem>\r\n                <MenuItem value=\"TRUE_FALSE\">{t('quiz.trueFalse')}</MenuItem>\r\n                <MenuItem value=\"FILL_BLANK\">{t('quiz.fillBlank')}</MenuItem>\r\n                <MenuItem value=\"IMAGE_CHOICE\">{t('quiz.imageChoice')}</MenuItem>\r\n              </Select>\r\n            </FormControl>\r\n\r\n            {question.type === \"FILL_BLANK\" ? (\r\n              <TextField\r\n                fullWidth\r\n                label={t('quiz.correctAnswer')}\r\n                value={question.correctText}\r\n                onChange={(e) => updateQuestion(qIndex, \"correctText\", e.target.value)}\r\n              />\r\n            ) : (\r\n              <Box>\r\n                <Typography variant=\"subtitle1\" gutterBottom>\r\n                  {t('quiz.choices')}:\r\n                </Typography>\r\n                {question.choices.map((choice, cIndex) => (\r\n                  <Stack key={cIndex} direction=\"row\" spacing={1} alignItems=\"center\" mb={1}>\r\n                    <Button\r\n                      variant={choice.isCorrect ? \"contained\" : \"outlined\"}\r\n                      onClick={() => updateChoiceCorrect(qIndex, cIndex)}\r\n                      size=\"small\"\r\n                    >\r\n                      {choice.isCorrect ? \"✓\" : \"○\"}\r\n                    </Button>\r\n                    <TextField\r\n                      value={choice.text}\r\n                      onChange={(e) => updateChoiceText(qIndex, cIndex, e.target.value)}\r\n                      placeholder={t('quiz.choiceText')}\r\n                      sx={{ flexGrow: 1 }}\r\n                    />\r\n                    {question.type === \"IMAGE_CHOICE\" && (\r\n                      <Button\r\n                        variant=\"outlined\"\r\n                        onClick={() => updateChoiceImage(qIndex, cIndex)}\r\n                        size=\"small\"\r\n                      >\r\n                        {choice.imageUrl ? t('quiz.changeImage') : t('quiz.addImage')}\r\n                      </Button>\r\n                    )}\r\n                    {question.choices.length > 1 && (\r\n                      <IconButton\r\n                        onClick={() => removeChoice(qIndex, cIndex)}\r\n                        color=\"error\"\r\n                        size=\"small\"\r\n                      >\r\n                        <ClearIcon />\r\n                      </IconButton>\r\n                    )}\r\n                  </Stack>\r\n                ))}\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => addChoice(qIndex)}\r\n                  size=\"small\"\r\n                  sx={{ mt: 1 }}\r\n                >\r\n                  + {t('quiz.addChoice')}\r\n                </Button>\r\n              </Box>\r\n            )}\r\n\r\n            {question.type !== \"IMAGE_CHOICE\" && (\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={() => handleImageUpload(qIndex)}\r\n                size=\"small\"\r\n              >\r\n                {question.imageUrl ? t('quiz.changeQuestionImage') : t('quiz.addQuestionImage')}\r\n              </Button>\r\n            )}\r\n          </Stack>\r\n        </Paper>\r\n      ))}\r\n\r\n      <Stack direction=\"row\" spacing={2} sx={{ mb: 3 }}>\r\n        <Button variant=\"contained\" onClick={addQuestion}>\r\n          + {t('quiz.addQuestion')}\r\n        </Button>\r\n        <Button variant=\"outlined\" onClick={() => navigate(\"/contenus\")}>\r\n          {t('common.cancel')}\r\n        </Button>\r\n      </Stack>\r\n\r\n      <Button\r\n        variant=\"contained\"\r\n        color=\"primary\"\r\n        onClick={handleSubmit}\r\n        disabled={questions.length === 0}\r\n        size=\"large\"\r\n      >\r\n        💾 {t('quiz.updateQuiz')}\r\n      </Button>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default EditQuizForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,gBAAgB,QACX,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAE,CAAC,GAAGL,cAAc,CAAC,CAAC;EAC9B,MAAMM,MAAM,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAU,CAAC,GAAGF,MAAM;EAE5B,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,4CAA4CT,SAAS,EAAE,CAAC;QACzF,MAAMU,IAAI,GAAGF,QAAQ,CAACG,IAAI;QAE1B,IAAID,IAAI,EAAE;UACRN,mBAAmB,CAACQ,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,SAAS,GAAG,EAAE,CAAC,CAAC;UACpDR,YAAY,CAACI,IAAI,CAACL,SAAS,IAAI,EAAE,CAAC;QACpC;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DE,KAAK,CAACpB,CAAC,CAAC,gBAAgB,CAAC,CAAC;MAC5B,CAAC,SAAS;QACRK,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACP,SAAS,EAAEH,CAAC,CAAC,CAAC;EAElB,MAAMqB,WAAW,GAAGA,CAAA,KAAM;IACxBZ,YAAY,CAAC,CACX,GAAGD,SAAS,EACZ;MACEc,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,CAAC;QAAEL,IAAI,EAAE,EAAE;QAAEM,SAAS,EAAE;MAAM,CAAC;IAC1C,CAAC,CACF,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAC9C,MAAMC,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGC,KAAK;IAE7B,IAAID,KAAK,KAAK,MAAM,EAAE;MACpB,IAAIC,KAAK,KAAK,YAAY,EAAE;QAC1BC,OAAO,CAACH,KAAK,CAAC,CAACH,OAAO,GAAG,CACvB;UAAEL,IAAI,EAAEtB,CAAC,CAAC,WAAW,CAAC;UAAE4B,SAAS,EAAE;QAAM,CAAC,EAC1C;UAAEN,IAAI,EAAEtB,CAAC,CAAC,YAAY,CAAC;UAAE4B,SAAS,EAAE;QAAM,CAAC,CAC5C;MACH,CAAC,MAAM,IAAII,KAAK,KAAK,cAAc,EAAE;QACnCC,OAAO,CAACH,KAAK,CAAC,CAACH,OAAO,GAAG,CACvB;UAAEL,IAAI,EAAE,EAAE;UAAEG,QAAQ,EAAE,EAAE;UAAEG,SAAS,EAAE;QAAM,CAAC,EAC5C;UAAEN,IAAI,EAAE,EAAE;UAAEG,QAAQ,EAAE,EAAE;UAAEG,SAAS,EAAE;QAAM,CAAC,CAC7C;MACH,CAAC,MAAM;QACLK,OAAO,CAACH,KAAK,CAAC,CAACH,OAAO,GAAG,CAAC;UAAEL,IAAI,EAAE,EAAE;UAAEM,SAAS,EAAE;QAAM,CAAC,CAAC;MAC3D;IACF;IAEAnB,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEd,IAAI,KAAK;IACjD,MAAMW,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,CAACS,MAAM,CAAC,CAACd,IAAI,GAAGA,IAAI;IAC3Cb,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAMI,iBAAiB,GAAG,MAAAA,CAAOF,MAAM,EAAEC,MAAM,KAAK;IAClD,MAAME,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACf,IAAI,GAAG,MAAM;IACnBe,KAAK,CAACG,MAAM,GAAG,SAAS;IACxBH,KAAK,CAACI,QAAQ,GAAG,MAAOC,CAAC,IAAK;MAC5B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACF,IAAI,EAAE;MACX,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAC7B,IAAI;QACF,MAAMM,GAAG,GAAG,MAAM1D,KAAK,CAAC2D,IAAI,CAC1B,qDAAqD,EACrDJ,QAAQ,EACR;UAAEK,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QAAE,CACvD,CAAC;QACD,MAAMnB,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;QAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,CAACS,MAAM,CAAC,CAACX,QAAQ,GAAGyB,GAAG,CAACpC,IAAI,CAACW,QAAQ;QAC5DhB,YAAY,CAACwB,OAAO,CAAC;MACvB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZjC,KAAK,CAACpB,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACnC;IACF,CAAC;IACDsC,KAAK,CAACgB,KAAK,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACpB,MAAM,EAAEC,MAAM,KAAK;IAC9C,MAAMH,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,GAAGM,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,CAAC6B,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC/D,GAAGD,CAAC;MACJ7B,SAAS,EAAE8B,CAAC,KAAKtB;IACnB,CAAC,CAAC,CAAC;IACH3B,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAM0B,SAAS,GAAIxB,MAAM,IAAK;IAC5B,MAAMF,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,CAACiC,IAAI,CAAC;MAAEtC,IAAI,EAAE,EAAE;MAAEM,SAAS,EAAE;IAAM,CAAC,CAAC;IAC5DnB,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAM4B,YAAY,GAAGA,CAAC1B,MAAM,EAAEC,MAAM,KAAK;IACvC,MAAMH,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACR,OAAO,CAACmC,MAAM,CAAC1B,MAAM,EAAE,CAAC,CAAC;IACzC3B,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAM8B,cAAc,GAAIjC,KAAK,IAAK;IAChC,MAAMG,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;IAC9ByB,OAAO,CAAC6B,MAAM,CAAChC,KAAK,EAAE,CAAC,CAAC;IACxBrB,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAM+B,iBAAiB,GAAG,MAAO7B,MAAM,IAAK;IAC1C,MAAMG,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACf,IAAI,GAAG,MAAM;IACnBe,KAAK,CAACG,MAAM,GAAG,SAAS;IACxBH,KAAK,CAACI,QAAQ,GAAG,MAAOC,CAAC,IAAK;MAC5B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACF,IAAI,EAAE;MACX,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAC7B,IAAI;QACF,MAAMM,GAAG,GAAG,MAAM1D,KAAK,CAAC2D,IAAI,CAC1B,qDAAqD,EACrDJ,QAAQ,EACR;UAAEK,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QAAE,CACvD,CAAC;QACD,MAAMnB,OAAO,GAAG,CAAC,GAAGzB,SAAS,CAAC;QAC9ByB,OAAO,CAACE,MAAM,CAAC,CAACV,QAAQ,GAAGyB,GAAG,CAACpC,IAAI,CAACW,QAAQ;QAC5ChB,YAAY,CAACwB,OAAO,CAAC;MACvB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZjC,KAAK,CAACpB,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACnC;IACF,CAAC;IACDsC,KAAK,CAACgB,KAAK,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA,KAAK,MAAM,CAACP,CAAC,EAAEQ,CAAC,CAAC,IAAI1D,SAAS,CAAC2D,OAAO,CAAC,CAAC,EAAE;MACxC,IAAI,CAACD,CAAC,CAAC5C,IAAI,CAAC8C,IAAI,CAAC,CAAC,EAAE;QAClBhD,KAAK,CAAC,KAAKpB,CAAC,CAAC,uBAAuB,CAAC,IAAI0D,CAAC,GAAG,CAAC,GAAG,CAAC;QAClD;MACF;MAEA,IAAIQ,CAAC,CAAC3C,IAAI,KAAK,YAAY,IAAI,CAAC2C,CAAC,CAACvC,OAAO,CAAC0C,IAAI,CAAEZ,CAAC,IAAKA,CAAC,CAAC7B,SAAS,CAAC,EAAE;QAClER,KAAK,CAAC,KAAKpB,CAAC,CAAC,4BAA4B,CAAC,IAAI0D,CAAC,GAAG,CAAC,GAAG,CAAC;QACvD;MACF;MAEA,IAAIQ,CAAC,CAAC3C,IAAI,KAAK,YAAY,IAAI2C,CAAC,CAACvC,OAAO,CAAC0C,IAAI,CAAEZ,CAAC,IAAK,CAACA,CAAC,CAACnC,IAAI,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAACX,CAAC,CAAChC,QAAQ,CAAC,EAAE;QACnFL,KAAK,CAAC,KAAKpB,CAAC,CAAC,yBAAyB,CAAC,IAAI0D,CAAC,GAAG,CAAC,GAAG,CAAC;QACpD;MACF;MAEA,IAAIQ,CAAC,CAAC3C,IAAI,KAAK,YAAY,IAAI,CAAC2C,CAAC,CAACxC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;QACpDhD,KAAK,CAAC,KAAKpB,CAAC,CAAC,8BAA8B,CAAC,IAAI0D,CAAC,GAAG,CAAC,GAAG,CAAC;QACzD;MACF;IACF;;IAEA;IACA,IAAI;MACF,MAAMlE,KAAK,CAAC8E,KAAK,CAAC,4CAA4CnE,SAAS,EAAE,EAAE;QACzEA,SAAS,EAAEoE,QAAQ,CAACpE,SAAS,CAAC;QAC9Bc,SAAS,EAAEX,gBAAgB,GAAG,EAAE;QAChCE;MACF,CAAC,CAAC;MAEFY,KAAK,CAACpB,CAAC,CAAC,yBAAyB,CAAC,CAAC;MACnCE,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,CAAC,OAAOmD,GAAG,EAAE;MACZlC,OAAO,CAACD,KAAK,CAAClB,CAAC,CAAC,kBAAkB,CAAC,EAAEqD,GAAG,CAAC;MACzCjC,KAAK,CAACpB,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,IAAII,OAAO,EAAE;IACX,oBACEP,OAAA,CAACnB,GAAG;MAAC8F,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E/E,OAAA,CAACR,gBAAgB;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEnF,OAAA,CAACnB,GAAG;IAACuG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAChB/E,OAAA,CAAChB,UAAU;MAACsG,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAR,QAAA,GAAC,eACjC,EAAC5E,CAAC,CAAC,eAAe,CAAC;IAAA;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEbnF,OAAA,CAACd,KAAK;MAACkG,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,eACzB/E,OAAA,CAACb,KAAK;QAACsG,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACb,UAAU,EAAC,QAAQ;QAAAE,QAAA,gBACpD/E,OAAA,CAACjB,SAAS;UACR2C,IAAI,EAAC,QAAQ;UACbiE,KAAK,EAAExF,CAAC,CAAC,gBAAgB,CAAE;UAC3BgC,KAAK,EAAE1B,gBAAiB;UACxBmF,QAAQ,EAAG9C,CAAC,IAAKpC,mBAAmB,CAACgE,QAAQ,CAAC5B,CAAC,CAACE,MAAM,CAACb,KAAK,CAAC,IAAI,CAAC,CAAE;UACpEiD,EAAE,EAAE;YAAES,KAAK,EAAE;UAAI;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFnF,OAAA,CAAChB,UAAU;UAACsG,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAAAf,QAAA,EAC/C5E,CAAC,CAAC,cAAc;QAAC;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEPxE,SAAS,CAACgD,GAAG,CAAC,CAACoC,QAAQ,EAAEzD,MAAM,kBAC9BtC,OAAA,CAACd,KAAK;MAAckG,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACtC/E,OAAA,CAACb,KAAK;QAACsG,SAAS,EAAC,KAAK;QAACb,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACW,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAC9E/E,OAAA,CAAChB,UAAU;UAACsG,OAAO,EAAC,IAAI;UAAAP,QAAA,GACrB5E,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC,EAACmC,MAAM,GAAG,CAAC;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACbnF,OAAA,CAACf,UAAU;UACT+G,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC5B,MAAM,CAAE;UACtCwD,KAAK,EAAC,OAAO;UACbG,IAAI,EAAC,OAAO;UAAAlB,QAAA,eAEZ/E,OAAA,CAACP,UAAU;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAERnF,OAAA,CAACb,KAAK;QAACuG,OAAO,EAAE,CAAE;QAAAX,QAAA,gBAChB/E,OAAA,CAACjB,SAAS;UACRmH,SAAS;UACTP,KAAK,EAAExF,CAAC,CAAC,mBAAmB,CAAE;UAC9BgC,KAAK,EAAE4D,QAAQ,CAACtE,IAAK;UACrBmE,QAAQ,EAAG9C,CAAC,IAAKd,cAAc,CAACM,MAAM,EAAE,MAAM,EAAEQ,CAAC,CAACE,MAAM,CAACb,KAAK,CAAE;UAChEgE,SAAS;UACTC,IAAI,EAAE;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEFnF,OAAA,CAACV,WAAW;UAAC4G,SAAS;UAAAnB,QAAA,gBACpB/E,OAAA,CAACT,UAAU;YAAAwF,QAAA,EAAE5E,CAAC,CAAC,mBAAmB;UAAC;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACjDnF,OAAA,CAACZ,MAAM;YACL+C,KAAK,EAAE4D,QAAQ,CAACrE,IAAK;YACrBkE,QAAQ,EAAG9C,CAAC,IAAKd,cAAc,CAACM,MAAM,EAAE,MAAM,EAAEQ,CAAC,CAACE,MAAM,CAACb,KAAK,CAAE;YAChEwD,KAAK,EAAExF,CAAC,CAAC,mBAAmB,CAAE;YAAA4E,QAAA,gBAE9B/E,OAAA,CAACX,QAAQ;cAAC8C,KAAK,EAAC,KAAK;cAAA4C,QAAA,EAAE5E,CAAC,CAAC,qBAAqB;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3DnF,OAAA,CAACX,QAAQ;cAAC8C,KAAK,EAAC,YAAY;cAAA4C,QAAA,EAAE5E,CAAC,CAAC,gBAAgB;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7DnF,OAAA,CAACX,QAAQ;cAAC8C,KAAK,EAAC,YAAY;cAAA4C,QAAA,EAAE5E,CAAC,CAAC,gBAAgB;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7DnF,OAAA,CAACX,QAAQ;cAAC8C,KAAK,EAAC,cAAc;cAAA4C,QAAA,EAAE5E,CAAC,CAAC,kBAAkB;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEbY,QAAQ,CAACrE,IAAI,KAAK,YAAY,gBAC7B1B,OAAA,CAACjB,SAAS;UACRmH,SAAS;UACTP,KAAK,EAAExF,CAAC,CAAC,oBAAoB,CAAE;UAC/BgC,KAAK,EAAE4D,QAAQ,CAAClE,WAAY;UAC5B+D,QAAQ,EAAG9C,CAAC,IAAKd,cAAc,CAACM,MAAM,EAAE,aAAa,EAAEQ,CAAC,CAACE,MAAM,CAACb,KAAK;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,gBAEFnF,OAAA,CAACnB,GAAG;UAAAkG,QAAA,gBACF/E,OAAA,CAAChB,UAAU;YAACsG,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,GACzC5E,CAAC,CAAC,cAAc,CAAC,EAAC,GACrB;UAAA;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZY,QAAQ,CAACjE,OAAO,CAAC6B,GAAG,CAAC,CAAC0C,MAAM,EAAE9D,MAAM,kBACnCvC,OAAA,CAACb,KAAK;YAAcsG,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACb,UAAU,EAAC,QAAQ;YAACW,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACxE/E,OAAA,CAAClB,MAAM;cACLwG,OAAO,EAAEe,MAAM,CAACtE,SAAS,GAAG,WAAW,GAAG,UAAW;cACrDiE,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACpB,MAAM,EAAEC,MAAM,CAAE;cACnD0D,IAAI,EAAC,OAAO;cAAAlB,QAAA,EAEXsB,MAAM,CAACtE,SAAS,GAAG,GAAG,GAAG;YAAG;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTnF,OAAA,CAACjB,SAAS;cACRoD,KAAK,EAAEkE,MAAM,CAAC5E,IAAK;cACnBmE,QAAQ,EAAG9C,CAAC,IAAKT,gBAAgB,CAACC,MAAM,EAAEC,MAAM,EAAEO,CAAC,CAACE,MAAM,CAACb,KAAK,CAAE;cAClEmE,WAAW,EAAEnG,CAAC,CAAC,iBAAiB,CAAE;cAClCiF,EAAE,EAAE;gBAAEmB,QAAQ,EAAE;cAAE;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,EACDY,QAAQ,CAACrE,IAAI,KAAK,cAAc,iBAC/B1B,OAAA,CAAClB,MAAM;cACLwG,OAAO,EAAC,UAAU;cAClBU,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAACF,MAAM,EAAEC,MAAM,CAAE;cACjD0D,IAAI,EAAC,OAAO;cAAAlB,QAAA,EAEXsB,MAAM,CAACzE,QAAQ,GAAGzB,CAAC,CAAC,kBAAkB,CAAC,GAAGA,CAAC,CAAC,eAAe;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACT,EACAY,QAAQ,CAACjE,OAAO,CAAC0E,MAAM,GAAG,CAAC,iBAC1BxG,OAAA,CAACf,UAAU;cACT+G,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAC1B,MAAM,EAAEC,MAAM,CAAE;cAC5CuD,KAAK,EAAC,OAAO;cACbG,IAAI,EAAC,OAAO;cAAAlB,QAAA,eAEZ/E,OAAA,CAACN,SAAS;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb;UAAA,GA/BS5C,MAAM;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCX,CACR,CAAC,eACFnF,OAAA,CAAClB,MAAM;YACLwG,OAAO,EAAC,UAAU;YAClBU,OAAO,EAAEA,CAAA,KAAMlC,SAAS,CAACxB,MAAM,CAAE;YACjC2D,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,GACf,IACG,EAAC5E,CAAC,CAAC,gBAAgB,CAAC;UAAA;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEAY,QAAQ,CAACrE,IAAI,KAAK,cAAc,iBAC/B1B,OAAA,CAAClB,MAAM;UACLwG,OAAO,EAAC,UAAU;UAClBU,OAAO,EAAEA,CAAA,KAAM7B,iBAAiB,CAAC7B,MAAM,CAAE;UACzC2D,IAAI,EAAC,OAAO;UAAAlB,QAAA,EAEXgB,QAAQ,CAACnE,QAAQ,GAAGzB,CAAC,CAAC,0BAA0B,CAAC,GAAGA,CAAC,CAAC,uBAAuB;QAAC;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA,GAzGE7C,MAAM;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA0GX,CACR,CAAC,eAEFnF,OAAA,CAACb,KAAK;MAACsG,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACN,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBAC/C/E,OAAA,CAAClB,MAAM;QAACwG,OAAO,EAAC,WAAW;QAACU,OAAO,EAAExE,WAAY;QAAAuD,QAAA,GAAC,IAC9C,EAAC5E,CAAC,CAAC,kBAAkB,CAAC;MAAA;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACTnF,OAAA,CAAClB,MAAM;QAACwG,OAAO,EAAC,UAAU;QAACU,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,WAAW,CAAE;QAAA0E,QAAA,EAC7D5E,CAAC,CAAC,eAAe;MAAC;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAERnF,OAAA,CAAClB,MAAM;MACLwG,OAAO,EAAC,WAAW;MACnBQ,KAAK,EAAC,SAAS;MACfE,OAAO,EAAE5B,YAAa;MACtBsC,QAAQ,EAAE/F,SAAS,CAAC6F,MAAM,KAAK,CAAE;MACjCP,IAAI,EAAC,OAAO;MAAAlB,QAAA,GACb,eACI,EAAC5E,CAAC,CAAC,iBAAiB,CAAC;IAAA;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjF,EAAA,CA/VID,YAAY;EAAA,QACFH,cAAc,EACbF,SAAS,EACPC,WAAW;AAAA;AAAA8G,EAAA,GAHxB1G,YAAY;AAiWlB,eAAeA,YAAY;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}