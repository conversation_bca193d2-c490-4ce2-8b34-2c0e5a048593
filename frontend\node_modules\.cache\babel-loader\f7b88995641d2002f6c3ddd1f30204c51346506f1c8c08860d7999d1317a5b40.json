{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\EtablissementDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Grid, Card, CardContent, List, ListItem, ListItemText, Chip, Stack, TextField, MenuItem, Divider } from \"@mui/material\";\nimport GroupIcon from \"@mui/icons-material/Group\";\nimport StarIcon from \"@mui/icons-material/Star\";\nimport RedeemIcon from \"@mui/icons-material/Redeem\";\nimport EmojiEventsIcon from \"@mui/icons-material/EmojiEvents\";\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE = \"http://localhost:8000\";\nexport default function EtablissementDashboardPage() {\n  _s();\n  // Liste des sessions (sessions2) récupérées du backend\n  const [sessions, setSessions] = useState([]);\n  const [selectedSession, setSelectedSession] = useState(\"Tous\");\n\n  // Étudiants à afficher (MOCK, à remplacer par requête API filtrée par session)\n  const [students] = useState([{\n    id: 1,\n    name: \"Majd Bouhmid\",\n    sessionId: 6,\n    // ID session2, à matcher\n    program: \"Informatique\",\n    sessions: [{\n      name: \"React Avancé\",\n      status: \"Terminée\",\n      grade: 18\n    }, {\n      name: \"NestJS\",\n      status: \"En cours\",\n      grade: 15\n    }],\n    moyenne: 16.5,\n    rewards: [{\n      name: \"Bon d'achat\",\n      reason: \"Top 3\",\n      date: \"2025-06-01\"\n    }]\n  }, {\n    id: 2,\n    name: \"Sarah Dridi\",\n    sessionId: 8,\n    program: \"Maths\",\n    sessions: [{\n      name: \"Statistiques\",\n      status: \"Terminée\",\n      grade: 14\n    }, {\n      name: \"Analyse\",\n      status: \"Terminée\",\n      grade: 15\n    }],\n    moyenne: 14.5,\n    rewards: []\n  }]);\n\n  // Récupérer les sessions2 depuis le backend\n  useEffect(() => {\n    // TODO: remplace par ton endpoint réel\n    axios.get(`${API_BASE}/sessions2`).then(res => {\n      setSessions([{\n        id: \"Tous\",\n        name: \"Toutes les sessions\"\n      }, ...res.data]);\n    }).catch(() => {\n      // fallback mock\n      setSessions([{\n        id: \"Tous\",\n        name: \"Toutes les sessions\"\n      }, {\n        id: 6,\n        name: \"React Avancé\"\n      }, {\n        id: 8,\n        name: \"Statistiques\"\n      }]);\n    });\n  }, []);\n\n  // Filtrer étudiants par session sélectionnée\n  const filteredStudents = selectedSession === \"Tous\" ? students : students.filter(s => s.sessionId === Number(selectedSession));\n\n  // Top 3 par moyenne\n  const topStudents = [...filteredStudents].sort((a, b) => b.moyenne - a.moyenne).slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      fontWeight: 700,\n      mb: 4,\n      color: \"#3d5afe\",\n      children: \"\\uD83C\\uDFEB Tableau de bord \\xC9tablissement\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      direction: {\n        xs: \"column\",\n        sm: \"row\"\n      },\n      spacing: 2,\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Session\",\n        select: true,\n        value: selectedSession,\n        onChange: e => setSelectedSession(e.target.value),\n        sx: {\n          minWidth: 220\n        },\n        children: sessions.map(sess => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: sess.id,\n          children: sess.name\n        }, sess.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      mb: 3,\n      maxWidth: 1440,\n      margin: \"auto\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            borderRadius: 3,\n            bgcolor: \"#e3f2fd\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              alignItems: \"center\",\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: 700,\n                color: \"#1976d2\",\n                children: filteredStudents.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(GroupIcon, {\n                sx: {\n                  color: \"#1976d2\",\n                  fontSize: 44\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              mt: 2,\n              color: \"#1565c0\",\n              fontWeight: 500,\n              fontSize: 18,\n              children: \"\\xC9tudiants inscrits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            borderRadius: 3,\n            bgcolor: \"#f3e5f5\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              alignItems: \"center\",\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: 700,\n                color: \"#8e24aa\",\n                children: filteredStudents.length ? (filteredStudents.reduce((sum, s) => sum + (s.moyenne || 0), 0) / filteredStudents.length).toFixed(2) : \"—\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(StarIcon, {\n                sx: {\n                  color: \"#8e24aa\",\n                  fontSize: 44\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              mt: 2,\n              color: \"#6a1b9a\",\n              fontWeight: 500,\n              fontSize: 18,\n              children: \"Moyenne g\\xE9n\\xE9rale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            borderRadius: 3,\n            bgcolor: \"#fce4ec\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              alignItems: \"center\",\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: 700,\n                color: \"#d81b60\",\n                children: filteredStudents.reduce((n, s) => n + (s.rewards.length || 0), 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(RedeemIcon, {\n                sx: {\n                  color: \"#d81b60\",\n                  fontSize: 44\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              mt: 2,\n              color: \"#ad1457\",\n              fontWeight: 500,\n              fontSize: 18,\n              children: \"Cadeaux/rewards\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            borderRadius: 3,\n            bgcolor: \"#fffde7\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              alignItems: \"center\",\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: 700,\n                color: \"#fbc02d\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(EmojiEventsIcon, {\n                sx: {\n                  color: \"#fbc02d\",\n                  fontSize: 44\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              mt: 2,\n              color: \"#f9a825\",\n              fontWeight: 500,\n              fontSize: 18,\n              children: \"Top 3 \\xE9tudiants\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        borderRadius: 3,\n        mt: 2,\n        maxWidth: 1200,\n        mx: \"auto\"\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          mb: 2,\n          children: [\"Liste des \\xE9tudiants (\", filteredStudents.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), filteredStudents.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"Aucun \\xE9tudiant trouv\\xE9.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          children: filteredStudents.map(student => {\n            var _student$moyenne;\n            return /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(ListItem, {\n                alignItems: \"flex-start\",\n                divider: true,\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Typography, {\n                    fontWeight: 600,\n                    children: [student.name, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: \"#789262\"\n                      },\n                      children: [\"(\", student.program, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 42\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 25\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      fontSize: 14,\n                      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Moyenne:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 29\n                      }, this), \" \", (_student$moyenne = student.moyenne) !== null && _student$moyenne !== void 0 ? _student$moyenne : \"—\", \"\\xA0|\\xA0\", /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Sessions:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 29\n                      }, this), \" \", student.sessions.map(s => /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${s.name} (${s.status})`,\n                        color: s.status === \"Terminée\" ? \"success\" : s.status === \"En cours\" ? \"primary\" : \"default\",\n                        size: \"small\",\n                        sx: {\n                          mr: 0.5\n                        }\n                      }, s.name, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 31\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 27\n                    }, this), student.rewards.length > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                      fontSize: 14,\n                      color: \"#c2185b\",\n                      children: [\"\\uD83C\\uDF81 Cadeaux:\", \" \", student.rewards.map(r => /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [r.name, \" \", /*#__PURE__*/_jsxDEV(\"i\", {\n                          children: [\"(\", r.reason, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 225,\n                          columnNumber: 44\n                        }, this), \" le \", r.date]\n                      }, r.name + r.date, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 224,\n                        columnNumber: 33\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, student.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        borderRadius: 3,\n        mt: 4,\n        maxWidth: 700,\n        mx: \"auto\"\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(EmojiEventsIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), \" Top 3 \\xE9tudiants (moyenne la plus \\xE9lev\\xE9e)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), topStudents.map((s, idx) => /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              fontWeight: 700,\n              fontSize: 22,\n              children: [\"#\", idx + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontWeight: 700,\n              fontSize: 18,\n              children: [s.name, \" (\", s.program, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Moy: ${s.moyenne}`,\n              color: \"success\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), s.rewards.length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              label: `🎁 ${s.rewards[0].name}`,\n              color: \"secondary\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, s.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n}\n_s(EtablissementDashboardPage, \"3h6jucT1a4TlKiOREZYloiOwyuk=\");\n_c = EtablissementDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"EtablissementDashboardPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "Chip", "<PERSON><PERSON>", "TextField", "MenuItem", "Divider", "GroupIcon", "StarIcon", "RedeemIcon", "EmojiEventsIcon", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE", "EtablissementDashboardPage", "_s", "sessions", "setSessions", "selectedSession", "setSelectedSession", "students", "id", "name", "sessionId", "program", "status", "grade", "moyenne", "rewards", "reason", "date", "get", "then", "res", "data", "catch", "filteredStudents", "filter", "s", "Number", "topStudents", "sort", "a", "b", "slice", "p", "children", "variant", "fontWeight", "mb", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "direction", "xs", "sm", "spacing", "label", "select", "value", "onChange", "e", "target", "sx", "min<PERSON><PERSON><PERSON>", "map", "sess", "container", "max<PERSON><PERSON><PERSON>", "margin", "item", "md", "borderRadius", "bgcolor", "alignItems", "length", "fontSize", "mt", "reduce", "sum", "toFixed", "n", "mx", "dense", "student", "_student$moyenne", "divider", "primary", "style", "secondary", "size", "mr", "r", "idx", "my", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/EtablissementDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  Chip,\r\n  Stack,\r\n  TextField,\r\n  MenuItem,\r\n  Divider,\r\n} from \"@mui/material\";\r\nimport GroupIcon from \"@mui/icons-material/Group\";\r\nimport StarIcon from \"@mui/icons-material/Star\";\r\nimport RedeemIcon from \"@mui/icons-material/Redeem\";\r\nimport EmojiEventsIcon from \"@mui/icons-material/EmojiEvents\";\r\nimport axios from \"axios\";\r\n\r\nconst API_BASE = \"http://localhost:8000\";\r\n\r\nexport default function EtablissementDashboardPage() {\r\n  // Liste des sessions (sessions2) récupérées du backend\r\n  const [sessions, setSessions] = useState([]);\r\n  const [selectedSession, setSelectedSession] = useState(\"Tous\");\r\n\r\n  // Étudiants à afficher (MOCK, à remplacer par requête API filtrée par session)\r\n  const [students] = useState([\r\n    {\r\n      id: 1,\r\n      name: \"<PERSON><PERSON>\",\r\n      sessionId: 6, // ID session2, à matcher\r\n      program: \"Informatique\",\r\n      sessions: [\r\n        { name: \"React Avancé\", status: \"Terminée\", grade: 18 },\r\n        { name: \"NestJS\", status: \"En cours\", grade: 15 },\r\n      ],\r\n      moyenne: 16.5,\r\n      rewards: [{ name: \"Bon d'achat\", reason: \"Top 3\", date: \"2025-06-01\" }],\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"Sarah Dridi\",\r\n      sessionId: 8,\r\n      program: \"Maths\",\r\n      sessions: [\r\n        { name: \"Statistiques\", status: \"Terminée\", grade: 14 },\r\n        { name: \"Analyse\", status: \"Terminée\", grade: 15 },\r\n      ],\r\n      moyenne: 14.5,\r\n      rewards: [],\r\n    },\r\n  ]);\r\n\r\n  // Récupérer les sessions2 depuis le backend\r\n  useEffect(() => {\r\n    // TODO: remplace par ton endpoint réel\r\n    axios\r\n      .get(`${API_BASE}/sessions2`)\r\n      .then((res) => {\r\n        setSessions([{ id: \"Tous\", name: \"Toutes les sessions\" }, ...res.data]);\r\n      })\r\n      .catch(() => {\r\n        // fallback mock\r\n        setSessions([\r\n          { id: \"Tous\", name: \"Toutes les sessions\" },\r\n          { id: 6, name: \"React Avancé\" },\r\n          { id: 8, name: \"Statistiques\" },\r\n        ]);\r\n      });\r\n  }, []);\r\n\r\n  // Filtrer étudiants par session sélectionnée\r\n  const filteredStudents =\r\n    selectedSession === \"Tous\"\r\n      ? students\r\n      : students.filter((s) => s.sessionId === Number(selectedSession));\r\n\r\n  // Top 3 par moyenne\r\n  const topStudents = [...filteredStudents]\r\n    .sort((a, b) => b.moyenne - a.moyenne)\r\n    .slice(0, 3);\r\n\r\n  return (\r\n    <Box p={3}>\r\n      <Typography variant=\"h4\" fontWeight={700} mb={4} color=\"#3d5afe\">\r\n        🏫 Tableau de bord Établissement\r\n      </Typography>\r\n\r\n      {/* Filtre par Session */}\r\n      <Stack direction={{ xs: \"column\", sm: \"row\" }} spacing={2} mb={3}>\r\n        <TextField\r\n          label=\"Session\"\r\n          select\r\n          value={selectedSession}\r\n          onChange={(e) => setSelectedSession(e.target.value)}\r\n          sx={{ minWidth: 220 }}\r\n        >\r\n          {sessions.map((sess) => (\r\n            <MenuItem key={sess.id} value={sess.id}>\r\n              {sess.name}\r\n            </MenuItem>\r\n          ))}\r\n        </TextField>\r\n      </Stack>\r\n\r\n      {/* Statistiques */}\r\n      <Grid container spacing={4} mb={3} maxWidth={1440} margin=\"auto\">\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <Card sx={{ borderRadius: 3, bgcolor: \"#e3f2fd\" }}>\r\n            <CardContent>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n                <Typography variant=\"h3\" fontWeight={700} color=\"#1976d2\">\r\n                  {filteredStudents.length}\r\n                </Typography>\r\n                <GroupIcon sx={{ color: \"#1976d2\", fontSize: 44 }} />\r\n              </Stack>\r\n              <Typography mt={2} color=\"#1565c0\" fontWeight={500} fontSize={18}>\r\n                Étudiants inscrits\r\n              </Typography>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <Card sx={{ borderRadius: 3, bgcolor: \"#f3e5f5\" }}>\r\n            <CardContent>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n                <Typography variant=\"h3\" fontWeight={700} color=\"#8e24aa\">\r\n                  {filteredStudents.length\r\n                    ? (\r\n                        filteredStudents.reduce((sum, s) => sum + (s.moyenne || 0), 0) /\r\n                        filteredStudents.length\r\n                      ).toFixed(2)\r\n                    : \"—\"}\r\n                </Typography>\r\n                <StarIcon sx={{ color: \"#8e24aa\", fontSize: 44 }} />\r\n              </Stack>\r\n              <Typography mt={2} color=\"#6a1b9a\" fontWeight={500} fontSize={18}>\r\n                Moyenne générale\r\n              </Typography>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <Card sx={{ borderRadius: 3, bgcolor: \"#fce4ec\" }}>\r\n            <CardContent>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n                <Typography variant=\"h3\" fontWeight={700} color=\"#d81b60\">\r\n                  {filteredStudents.reduce((n, s) => n + (s.rewards.length || 0), 0)}\r\n                </Typography>\r\n                <RedeemIcon sx={{ color: \"#d81b60\", fontSize: 44 }} />\r\n              </Stack>\r\n              <Typography mt={2} color=\"#ad1457\" fontWeight={500} fontSize={18}>\r\n                Cadeaux/rewards\r\n              </Typography>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <Card sx={{ borderRadius: 3, bgcolor: \"#fffde7\" }}>\r\n            <CardContent>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n                <Typography variant=\"h3\" fontWeight={700} color=\"#fbc02d\">\r\n                  3\r\n                </Typography>\r\n                <EmojiEventsIcon sx={{ color: \"#fbc02d\", fontSize: 44 }} />\r\n              </Stack>\r\n              <Typography mt={2} color=\"#f9a825\" fontWeight={500} fontSize={18}>\r\n                Top 3 étudiants\r\n              </Typography>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* Liste étudiants */}\r\n      <Card sx={{ borderRadius: 3, mt: 2, maxWidth: 1200, mx: \"auto\" }}>\r\n        <CardContent>\r\n          <Typography variant=\"h6\" mb={2}>\r\n            Liste des étudiants ({filteredStudents.length})\r\n          </Typography>\r\n          {filteredStudents.length === 0 ? (\r\n            <Typography color=\"text.secondary\">Aucun étudiant trouvé.</Typography>\r\n          ) : (\r\n            <List dense>\r\n              {filteredStudents.map((student) => (\r\n                <Box key={student.id}>\r\n                  <ListItem alignItems=\"flex-start\" divider>\r\n                    <ListItemText\r\n                      primary={\r\n                        <Typography fontWeight={600}>\r\n                          {student.name} <span style={{ color: \"#789262\" }}>({student.program})</span>\r\n                        </Typography>\r\n                      }\r\n                      secondary={\r\n                        <>\r\n                          <Typography fontSize={14}>\r\n                            <b>Moyenne:</b> {student.moyenne ?? \"—\"}\r\n                            &nbsp;|&nbsp;\r\n                            <b>Sessions:</b>{\" \"}\r\n                            {student.sessions.map((s) => (\r\n                              <Chip\r\n                                key={s.name}\r\n                                label={`${s.name} (${s.status})`}\r\n                                color={\r\n                                  s.status === \"Terminée\"\r\n                                    ? \"success\"\r\n                                    : s.status === \"En cours\"\r\n                                    ? \"primary\"\r\n                                    : \"default\"\r\n                                }\r\n                                size=\"small\"\r\n                                sx={{ mr: 0.5 }}\r\n                              />\r\n                            ))}\r\n                          </Typography>\r\n                          {student.rewards.length > 0 && (\r\n                            <Typography fontSize={14} color=\"#c2185b\">\r\n                              🎁 Cadeaux:{\" \"}\r\n                              {student.rewards.map((r) => (\r\n                                <span key={r.name + r.date}>\r\n                                  {r.name} <i>({r.reason})</i> le {r.date}\r\n                                </span>\r\n                              ))}\r\n                            </Typography>\r\n                          )}\r\n                        </>\r\n                      }\r\n                    />\r\n                  </ListItem>\r\n                </Box>\r\n              ))}\r\n            </List>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Top 3 étudiants */}\r\n      <Card sx={{ borderRadius: 3, mt: 4, maxWidth: 700, mx: \"auto\" }}>\r\n        <CardContent>\r\n          <Typography variant=\"h6\" mb={2}>\r\n            <EmojiEventsIcon color=\"warning\" /> Top 3 étudiants (moyenne la plus élevée)\r\n          </Typography>\r\n          {topStudents.map((s, idx) => (\r\n            <Box key={s.id}>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n                <Typography fontWeight={700} fontSize={22}>\r\n                  #{idx + 1}\r\n                </Typography>\r\n                <Typography fontWeight={700} fontSize={18}>\r\n                  {s.name} ({s.program})\r\n                </Typography>\r\n                <Chip label={`Moy: ${s.moyenne}`} color=\"success\" size=\"small\" />\r\n                {s.rewards.length > 0 && (\r\n                  <Chip\r\n                    label={`🎁 ${s.rewards[0].name}`}\r\n                    color=\"secondary\"\r\n                    size=\"small\"\r\n                  />\r\n                )}\r\n              </Stack>\r\n              <Divider sx={{ my: 1 }} />\r\n            </Box>\r\n          ))}\r\n        </CardContent>\r\n      </Card>\r\n    </Box>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,eAAe,SAASC,0BAA0BA,CAAA,EAAG;EAAAC,EAAA;EACnD;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,MAAM,CAAC;;EAE9D;EACA,MAAM,CAAC8B,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,CAC1B;IACE+B,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,CAAC;IAAE;IACdC,OAAO,EAAE,cAAc;IACvBR,QAAQ,EAAE,CACR;MAAEM,IAAI,EAAE,cAAc;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAC,EACvD;MAAEJ,IAAI,EAAE,QAAQ;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAC,CAClD;IACDC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,CAAC;MAAEN,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAa,CAAC;EACxE,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,OAAO;IAChBR,QAAQ,EAAE,CACR;MAAEM,IAAI,EAAE,cAAc;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAC,EACvD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAC,CACnD;IACDC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX,CAAC,CACF,CAAC;;EAEF;EACAvC,SAAS,CAAC,MAAM;IACd;IACAmB,KAAK,CACFuB,GAAG,CAAC,GAAGlB,QAAQ,YAAY,CAAC,CAC5BmB,IAAI,CAAEC,GAAG,IAAK;MACbhB,WAAW,CAAC,CAAC;QAAEI,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAsB,CAAC,EAAE,GAAGW,GAAG,CAACC,IAAI,CAAC,CAAC;IACzE,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM;MACX;MACAlB,WAAW,CAAC,CACV;QAAEI,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAsB,CAAC,EAC3C;QAAED,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAe,CAAC,EAC/B;QAAED,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAe,CAAC,CAChC,CAAC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,gBAAgB,GACpBlB,eAAe,KAAK,MAAM,GACtBE,QAAQ,GACRA,QAAQ,CAACiB,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACf,SAAS,KAAKgB,MAAM,CAACrB,eAAe,CAAC,CAAC;;EAErE;EACA,MAAMsB,WAAW,GAAG,CAAC,GAAGJ,gBAAgB,CAAC,CACtCK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChB,OAAO,GAAGe,CAAC,CAACf,OAAO,CAAC,CACrCiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEd,oBACElC,OAAA,CAACnB,GAAG;IAACsD,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRpC,OAAA,CAAClB,UAAU;MAACuD,OAAO,EAAC,IAAI;MAACC,UAAU,EAAE,GAAI;MAACC,EAAE,EAAE,CAAE;MAACC,KAAK,EAAC,SAAS;MAAAJ,QAAA,EAAC;IAEjE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb5C,OAAA,CAACV,KAAK;MAACuD,SAAS,EAAE;QAAEC,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAM,CAAE;MAACC,OAAO,EAAE,CAAE;MAACT,EAAE,EAAE,CAAE;MAAAH,QAAA,eAC/DpC,OAAA,CAACT,SAAS;QACR0D,KAAK,EAAC,SAAS;QACfC,MAAM;QACNC,KAAK,EAAE3C,eAAgB;QACvB4C,QAAQ,EAAGC,CAAC,IAAK5C,kBAAkB,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACpDI,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAApB,QAAA,EAErB9B,QAAQ,CAACmD,GAAG,CAAEC,IAAI,iBACjB1D,OAAA,CAACR,QAAQ;UAAe2D,KAAK,EAAEO,IAAI,CAAC/C,EAAG;UAAAyB,QAAA,EACpCsB,IAAI,CAAC9C;QAAI,GADG8C,IAAI,CAAC/C,EAAE;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEZ,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGR5C,OAAA,CAACjB,IAAI;MAAC4E,SAAS;MAACX,OAAO,EAAE,CAAE;MAACT,EAAE,EAAE,CAAE;MAACqB,QAAQ,EAAE,IAAK;MAACC,MAAM,EAAC,MAAM;MAAAzB,QAAA,gBAC9DpC,OAAA,CAACjB,IAAI;QAAC+E,IAAI;QAAChB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACgB,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BpC,OAAA,CAAChB,IAAI;UAACuE,EAAE,EAAE;YAAES,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAA7B,QAAA,eAChDpC,OAAA,CAACf,WAAW;YAAAmD,QAAA,gBACVpC,OAAA,CAACV,KAAK;cAACuD,SAAS,EAAC,KAAK;cAACqB,UAAU,EAAC,QAAQ;cAAClB,OAAO,EAAE,CAAE;cAAAZ,QAAA,gBACpDpC,OAAA,CAAClB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAE,GAAI;gBAACE,KAAK,EAAC,SAAS;gBAAAJ,QAAA,EACtDV,gBAAgB,CAACyC;cAAM;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACb5C,OAAA,CAACN,SAAS;gBAAC6D,EAAE,EAAE;kBAAEf,KAAK,EAAE,SAAS;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACR5C,OAAA,CAAClB,UAAU;cAACuF,EAAE,EAAE,CAAE;cAAC7B,KAAK,EAAC,SAAS;cAACF,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,EAAC;YAElE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5C,OAAA,CAACjB,IAAI;QAAC+E,IAAI;QAAChB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACgB,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BpC,OAAA,CAAChB,IAAI;UAACuE,EAAE,EAAE;YAAES,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAA7B,QAAA,eAChDpC,OAAA,CAACf,WAAW;YAAAmD,QAAA,gBACVpC,OAAA,CAACV,KAAK;cAACuD,SAAS,EAAC,KAAK;cAACqB,UAAU,EAAC,QAAQ;cAAClB,OAAO,EAAE,CAAE;cAAAZ,QAAA,gBACpDpC,OAAA,CAAClB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAE,GAAI;gBAACE,KAAK,EAAC,SAAS;gBAAAJ,QAAA,EACtDV,gBAAgB,CAACyC,MAAM,GACpB,CACEzC,gBAAgB,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAE3C,CAAC,KAAK2C,GAAG,IAAI3C,CAAC,CAACX,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAC9DS,gBAAgB,CAACyC,MAAM,EACvBK,OAAO,CAAC,CAAC,CAAC,GACZ;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACb5C,OAAA,CAACL,QAAQ;gBAAC4D,EAAE,EAAE;kBAAEf,KAAK,EAAE,SAAS;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACR5C,OAAA,CAAClB,UAAU;cAACuF,EAAE,EAAE,CAAE;cAAC7B,KAAK,EAAC,SAAS;cAACF,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,EAAC;YAElE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5C,OAAA,CAACjB,IAAI;QAAC+E,IAAI;QAAChB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACgB,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BpC,OAAA,CAAChB,IAAI;UAACuE,EAAE,EAAE;YAAES,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAA7B,QAAA,eAChDpC,OAAA,CAACf,WAAW;YAAAmD,QAAA,gBACVpC,OAAA,CAACV,KAAK;cAACuD,SAAS,EAAC,KAAK;cAACqB,UAAU,EAAC,QAAQ;cAAClB,OAAO,EAAE,CAAE;cAAAZ,QAAA,gBACpDpC,OAAA,CAAClB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAE,GAAI;gBAACE,KAAK,EAAC,SAAS;gBAAAJ,QAAA,EACtDV,gBAAgB,CAAC4C,MAAM,CAAC,CAACG,CAAC,EAAE7C,CAAC,KAAK6C,CAAC,IAAI7C,CAAC,CAACV,OAAO,CAACiD,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACb5C,OAAA,CAACJ,UAAU;gBAAC2D,EAAE,EAAE;kBAAEf,KAAK,EAAE,SAAS;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACR5C,OAAA,CAAClB,UAAU;cAACuF,EAAE,EAAE,CAAE;cAAC7B,KAAK,EAAC,SAAS;cAACF,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,EAAC;YAElE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5C,OAAA,CAACjB,IAAI;QAAC+E,IAAI;QAAChB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACgB,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9BpC,OAAA,CAAChB,IAAI;UAACuE,EAAE,EAAE;YAAES,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAA7B,QAAA,eAChDpC,OAAA,CAACf,WAAW;YAAAmD,QAAA,gBACVpC,OAAA,CAACV,KAAK;cAACuD,SAAS,EAAC,KAAK;cAACqB,UAAU,EAAC,QAAQ;cAAClB,OAAO,EAAE,CAAE;cAAAZ,QAAA,gBACpDpC,OAAA,CAAClB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAE,GAAI;gBAACE,KAAK,EAAC,SAAS;gBAAAJ,QAAA,EAAC;cAE1D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAACH,eAAe;gBAAC0D,EAAE,EAAE;kBAAEf,KAAK,EAAE,SAAS;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACR5C,OAAA,CAAClB,UAAU;cAACuF,EAAE,EAAE,CAAE;cAAC7B,KAAK,EAAC,SAAS;cAACF,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,EAAC;YAElE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5C,OAAA,CAAChB,IAAI;MAACuE,EAAE,EAAE;QAAES,YAAY,EAAE,CAAC;QAAEK,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,IAAI;QAAEc,EAAE,EAAE;MAAO,CAAE;MAAAtC,QAAA,eAC/DpC,OAAA,CAACf,WAAW;QAAAmD,QAAA,gBACVpC,OAAA,CAAClB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACE,EAAE,EAAE,CAAE;UAAAH,QAAA,GAAC,0BACT,EAACV,gBAAgB,CAACyC,MAAM,EAAC,GAChD;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZlB,gBAAgB,CAACyC,MAAM,KAAK,CAAC,gBAC5BnE,OAAA,CAAClB,UAAU;UAAC0D,KAAK,EAAC,gBAAgB;UAAAJ,QAAA,EAAC;QAAsB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEtE5C,OAAA,CAACd,IAAI;UAACyF,KAAK;UAAAvC,QAAA,EACRV,gBAAgB,CAAC+B,GAAG,CAAEmB,OAAO;YAAA,IAAAC,gBAAA;YAAA,oBAC5B7E,OAAA,CAACnB,GAAG;cAAAuD,QAAA,eACFpC,OAAA,CAACb,QAAQ;gBAAC+E,UAAU,EAAC,YAAY;gBAACY,OAAO;gBAAA1C,QAAA,eACvCpC,OAAA,CAACZ,YAAY;kBACX2F,OAAO,eACL/E,OAAA,CAAClB,UAAU;oBAACwD,UAAU,EAAE,GAAI;oBAAAF,QAAA,GACzBwC,OAAO,CAAChE,IAAI,EAAC,GAAC,eAAAZ,OAAA;sBAAMgF,KAAK,EAAE;wBAAExC,KAAK,EAAE;sBAAU,CAAE;sBAAAJ,QAAA,GAAC,GAAC,EAACwC,OAAO,CAAC9D,OAAO,EAAC,GAAC;oBAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CACb;kBACDqC,SAAS,eACPjF,OAAA,CAAAE,SAAA;oBAAAkC,QAAA,gBACEpC,OAAA,CAAClB,UAAU;sBAACsF,QAAQ,EAAE,EAAG;sBAAAhC,QAAA,gBACvBpC,OAAA;wBAAAoC,QAAA,EAAG;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,KAAC,GAAAiC,gBAAA,GAACD,OAAO,CAAC3D,OAAO,cAAA4D,gBAAA,cAAAA,gBAAA,GAAI,GAAG,EAAC,WAExC,eAAA7E,OAAA;wBAAAoC,QAAA,EAAG;sBAAS;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EAAC,GAAG,EACnBgC,OAAO,CAACtE,QAAQ,CAACmD,GAAG,CAAE7B,CAAC,iBACtB5B,OAAA,CAACX,IAAI;wBAEH4D,KAAK,EAAE,GAAGrB,CAAC,CAAChB,IAAI,KAAKgB,CAAC,CAACb,MAAM,GAAI;wBACjCyB,KAAK,EACHZ,CAAC,CAACb,MAAM,KAAK,UAAU,GACnB,SAAS,GACTa,CAAC,CAACb,MAAM,KAAK,UAAU,GACvB,SAAS,GACT,SACL;wBACDmE,IAAI,EAAC,OAAO;wBACZ3B,EAAE,EAAE;0BAAE4B,EAAE,EAAE;wBAAI;sBAAE,GAVXvD,CAAC,CAAChB,IAAI;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWZ,CACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,EACZgC,OAAO,CAAC1D,OAAO,CAACiD,MAAM,GAAG,CAAC,iBACzBnE,OAAA,CAAClB,UAAU;sBAACsF,QAAQ,EAAE,EAAG;sBAAC5B,KAAK,EAAC,SAAS;sBAAAJ,QAAA,GAAC,uBAC7B,EAAC,GAAG,EACdwC,OAAO,CAAC1D,OAAO,CAACuC,GAAG,CAAE2B,CAAC,iBACrBpF,OAAA;wBAAAoC,QAAA,GACGgD,CAAC,CAACxE,IAAI,EAAC,GAAC,eAAAZ,OAAA;0BAAAoC,QAAA,GAAG,GAAC,EAACgD,CAAC,CAACjE,MAAM,EAAC,GAAC;wBAAA;0BAAAsB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,QAAI,EAACwC,CAAC,CAAChE,IAAI;sBAAA,GAD9BgE,CAAC,CAACxE,IAAI,GAAGwE,CAAC,CAAChE,IAAI;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEpB,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CACb;kBAAA,eACD;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC,GA3CHgC,OAAO,CAACjE,EAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4Cf,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP5C,OAAA,CAAChB,IAAI;MAACuE,EAAE,EAAE;QAAES,YAAY,EAAE,CAAC;QAAEK,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,GAAG;QAAEc,EAAE,EAAE;MAAO,CAAE;MAAAtC,QAAA,eAC9DpC,OAAA,CAACf,WAAW;QAAAmD,QAAA,gBACVpC,OAAA,CAAClB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACE,EAAE,EAAE,CAAE;UAAAH,QAAA,gBAC7BpC,OAAA,CAACH,eAAe;YAAC2C,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sDACrC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZd,WAAW,CAAC2B,GAAG,CAAC,CAAC7B,CAAC,EAAEyD,GAAG,kBACtBrF,OAAA,CAACnB,GAAG;UAAAuD,QAAA,gBACFpC,OAAA,CAACV,KAAK;YAACuD,SAAS,EAAC,KAAK;YAACqB,UAAU,EAAC,QAAQ;YAAClB,OAAO,EAAE,CAAE;YAAAZ,QAAA,gBACpDpC,OAAA,CAAClB,UAAU;cAACwD,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,GAAC,GACxC,EAACiD,GAAG,GAAG,CAAC;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACb5C,OAAA,CAAClB,UAAU;cAACwD,UAAU,EAAE,GAAI;cAAC8B,QAAQ,EAAE,EAAG;cAAAhC,QAAA,GACvCR,CAAC,CAAChB,IAAI,EAAC,IAAE,EAACgB,CAAC,CAACd,OAAO,EAAC,GACvB;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5C,OAAA,CAACX,IAAI;cAAC4D,KAAK,EAAE,QAAQrB,CAAC,CAACX,OAAO,EAAG;cAACuB,KAAK,EAAC,SAAS;cAAC0C,IAAI,EAAC;YAAO;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChEhB,CAAC,CAACV,OAAO,CAACiD,MAAM,GAAG,CAAC,iBACnBnE,OAAA,CAACX,IAAI;cACH4D,KAAK,EAAE,MAAMrB,CAAC,CAACV,OAAO,CAAC,CAAC,CAAC,CAACN,IAAI,EAAG;cACjC4B,KAAK,EAAC,WAAW;cACjB0C,IAAI,EAAC;YAAO;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACR5C,OAAA,CAACP,OAAO;YAAC8D,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAjBlBhB,CAAC,CAACjB,EAAE;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACvC,EAAA,CAvPuBD,0BAA0B;AAAAmF,EAAA,GAA1BnF,0BAA0B;AAAA,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}