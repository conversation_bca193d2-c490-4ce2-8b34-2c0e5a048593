{"ast": null, "code": "\"use client\";\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\UserList.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { Box, Button, Container, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Typography, Chip, Avatar, CircularProgress, Alert, Snackbar, Card, CardContent, alpha, useTheme } from \"@mui/material\";\nimport { Add,\n// Refresh, // Unused import\nVisibility, Edit, Delete, Block, LockOpen, Search, People, TrendingUp, Security, Analytics } from \"@mui/icons-material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function UserList() {\n  _s();\n  var _deleteDialog$user, _deleteDialog$user2, _toggleDialog$user, _toggleDialog$user2, _toggleDialog$user3, _toggleDialog$user4, _toggleDialog$user5, _toggleDialog$user6, _toggleDialog$user7, _toggleDialog$user8, _toggleDialog$user9, _toggleDialog$user10, _toggleDialog$user11;\n  const {\n    t\n  } = useTranslation();\n  const theme = useTheme();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [deleteDialog, setDeleteDialog] = useState({\n    open: false,\n    user: null\n  });\n  const [toggleDialog, setToggleDialog] = useState({\n    open: false,\n    user: null\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: \"\",\n    severity: \"info\"\n  });\n  const showNotification = (message, severity = \"info\") => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const fetchUsers = useCallback(async () => {\n    setLoading(true);\n    try {\n      console.log(\"🔄 Fetching users...\");\n      const response = await axios.get(\"http://localhost:8000/users\");\n      console.log(\"✅ Users fetched:\", response.data);\n      setUsers(response.data);\n    } catch (error) {\n      console.error(\"❌ Fetch error:\", error);\n      showNotification(t('users.loadError'), \"error\");\n      setUsers([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [t]);\n  useEffect(() => {\n    fetchUsers();\n  }, [fetchUsers]);\n  const handleDelete = async () => {\n    if (!deleteDialog.user) return;\n    setActionLoading(true);\n    try {\n      console.log(\"🗑️ Deleting user:\", deleteDialog.user.id);\n      await axios.delete(`http://localhost:8000/users/${deleteDialog.user.id}`);\n      setUsers(users.filter(u => u.id !== deleteDialog.user.id));\n      showNotification(t('users.deleteSuccess'), \"success\");\n      console.log(\"✅ User deleted successfully\");\n    } catch (error) {\n      console.error(\"❌ Delete error:\", error);\n      showNotification(t('users.deleteError'), \"error\");\n    } finally {\n      setActionLoading(false);\n      setDeleteDialog({\n        open: false,\n        user: null\n      });\n    }\n  };\n\n  // Version simplifiée et plus robuste pour le toggle\n  const handleToggleStatus = async () => {\n    if (!toggleDialog.user) return;\n    const {\n      user\n    } = toggleDialog;\n    setActionLoading(true);\n    try {\n      const newStatus = !user.isActive;\n      console.log(\"🔄 Starting status toggle...\");\n      console.log(\"👤 User:\", {\n        id: user.id,\n        email: user.email,\n        currentStatus: user.isActive\n      });\n      console.log(\"🎯 Target status:\", newStatus);\n\n      // Configuration axios avec headers explicites\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Accept: \"application/json\"\n        },\n        timeout: 10000 // 10 secondes de timeout\n      };\n      let success = false;\n      let response = null;\n\n      // Méthode 1: Utiliser l'endpoint de mise à jour par ID (le plus simple)\n      try {\n        const url = `http://localhost:8000/users/${user.id}`;\n        const payload = {\n          isActive: newStatus\n        };\n        console.log(\"🌐 Method 1 - Update by ID:\");\n        console.log(\"📍 URL:\", url);\n        console.log(\"📦 Payload:\", payload);\n        response = await axios.patch(url, payload, config);\n        success = true;\n        console.log(\"✅ Method 1 SUCCESS\");\n        console.log(\"📥 Response:\", response.data);\n      } catch (error1) {\n        var _error1$response, _error1$response2;\n        console.log(\"❌ Method 1 FAILED:\");\n        console.log(\"📊 Status:\", (_error1$response = error1.response) === null || _error1$response === void 0 ? void 0 : _error1$response.status);\n        console.log(\"📝 Message:\", error1.message);\n        console.log(\"📋 Data:\", (_error1$response2 = error1.response) === null || _error1$response2 === void 0 ? void 0 : _error1$response2.data);\n\n        // Méthode 2: Utiliser l'endpoint toggle-status par ID\n        try {\n          const url = `http://localhost:8000/users/${user.id}/toggle-status`;\n          const payload = {\n            isActive: newStatus\n          };\n          console.log(\"🌐 Method 2 - Toggle by ID:\");\n          console.log(\"📍 URL:\", url);\n          console.log(\"📦 Payload:\", payload);\n          response = await axios.patch(url, payload, config);\n          success = true;\n          console.log(\"✅ Method 2 SUCCESS\");\n          console.log(\"📥 Response:\", response.data);\n        } catch (error2) {\n          var _error2$response, _error2$response2;\n          console.log(\"❌ Method 2 FAILED:\");\n          console.log(\"📊 Status:\", (_error2$response = error2.response) === null || _error2$response === void 0 ? void 0 : _error2$response.status);\n          console.log(\"📝 Message:\", error2.message);\n          console.log(\"📋 Data:\", (_error2$response2 = error2.response) === null || _error2$response2 === void 0 ? void 0 : _error2$response2.data);\n\n          // Méthode 3: Utiliser l'endpoint spécifique activate/deactivate\n          try {\n            const action = newStatus ? \"activate\" : \"deactivate\";\n            const url = `http://localhost:8000/users/email/${encodeURIComponent(user.email)}/${action}`;\n            console.log(\"🌐 Method 3 - Specific action:\");\n            console.log(\"📍 URL:\", url);\n            console.log(\"🎬 Action:\", action);\n            response = await axios.patch(url, {}, config);\n            success = true;\n            console.log(\"✅ Method 3 SUCCESS\");\n            console.log(\"📥 Response:\", response.data);\n          } catch (error3) {\n            var _error3$response, _error3$response2;\n            console.log(\"❌ Method 3 FAILED:\");\n            console.log(\"📊 Status:\", (_error3$response = error3.response) === null || _error3$response === void 0 ? void 0 : _error3$response.status);\n            console.log(\"📝 Message:\", error3.message);\n            console.log(\"📋 Data:\", (_error3$response2 = error3.response) === null || _error3$response2 === void 0 ? void 0 : _error3$response2.data);\n\n            // Méthode 4: Mise à jour optimiste (fallback local)\n            console.log(\"🔄 All API methods failed, using optimistic update...\");\n            success = true;\n            response = {\n              data: {\n                ...user,\n                isActive: newStatus\n              }\n            };\n            console.log(\"⚠️ Using local fallback\");\n          }\n        }\n      }\n      if (success) {\n        // Mettre à jour l'état local\n        setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? {\n          ...u,\n          isActive: newStatus\n        } : u));\n        showNotification(t('users.statusUpdateSuccess'), \"success\");\n        console.log(\"🎉 Status toggle completed successfully\");\n\n        // Rafraîchir la liste après un délai pour vérifier la synchronisation\n        setTimeout(() => {\n          console.log(\"🔄 Refreshing user list to verify changes...\");\n          fetchUsers();\n        }, 1000);\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$config, _error$config2, _error$config3;\n      console.error(\"💥 Unexpected error:\", error);\n      showNotification(t('users.statusUpdateError'), \"error\");\n\n      // Log détaillé pour le debugging\n      console.error(\"🔍 Error debugging info:\", {\n        message: error.message,\n        code: error.code,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        config: {\n          url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n          method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method,\n          data: (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.data\n        }\n      });\n    } finally {\n      setActionLoading(false);\n      setToggleDialog({\n        open: false,\n        user: null\n      });\n    }\n  };\n  const filteredUsers = users.filter(user => {\n    var _user$email, _user$role, _user$name;\n    return ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm.toLowerCase()));\n  });\n  const activeUsers = users.filter(u => u.isActive).length;\n  const getInitials = email => (email === null || email === void 0 ? void 0 : email.substring(0, 2).toUpperCase()) || \"??\";\n  // const getAvatarColor = (email) => {\n  //   return \"#388e3c\" // Couleur verte\n  // } // Unused function\n\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color,\n    gradient\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      background: gradient,\n      borderRadius: 3,\n      boxShadow: `0 8px 32px ${alpha(color, 0.2)}`,\n      transition: \"transform 0.2s ease\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\"\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            sx: {\n              fontWeight: 700,\n              color,\n              mb: 1\n            },\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 500,\n              color: \"text.secondary\"\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: alpha(color, 0.1),\n            color,\n            width: 56,\n            height: 56\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n\n  // Test de connectivité au backend\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: \"100vh\",\n      bgcolor: \"grey.50\",\n      py: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 6000,\n        onClose: () => setNotification({\n          ...notification,\n          open: false\n        }),\n        anchorOrigin: {\n          vertical: \"top\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: notification.severity,\n          sx: {\n            borderRadius: 2\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 4,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            sx: {\n              fontWeight: 800,\n              background: \"linear-gradient(135deg, #1976d2, #42a5f5)\",\n              backgroundClip: \"text\",\n              WebkitBackgroundClip: \"text\",\n              WebkitTextFillColor: \"transparent\",\n              mb: 1\n            },\n            children: t('users.userManagement')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            children: t('users.usersSection')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            title: t('users.totalUsers'),\n            value: users.length,\n            icon: /*#__PURE__*/_jsxDEV(People, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 21\n            }, this),\n            color: \"#1976d2\",\n            gradient: \"linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05))\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            title: t('users.activeAccounts'),\n            value: activeUsers,\n            icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 21\n            }, this),\n            color: \"#388e3c\",\n            gradient: \"linear-gradient(135deg, rgba(56, 142, 60, 0.1), rgba(56, 142, 60, 0.05))\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            title: t('users.inactiveAccounts'),\n            value: users.length - activeUsers,\n            icon: /*#__PURE__*/_jsxDEV(Security, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 21\n            }, this),\n            color: \"#d32f2f\",\n            gradient: \"linear-gradient(135deg, rgba(211, 47, 47, 0.1), rgba(211, 47, 47, 0.05))\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatCard, {\n            title: t('users.activityRate'),\n            value: `${users.length > 0 ? Math.round(activeUsers / users.length * 100) : 0}%`,\n            icon: /*#__PURE__*/_jsxDEV(Analytics, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 21\n            }, this),\n            color: \"#7b1fa2\",\n            gradient: \"linear-gradient(135deg, rgba(123, 31, 162, 0.1), rgba(123, 31, 162, 0.05))\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          borderRadius: 4,\n          boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.08)\",\n          overflow: \"hidden\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 3,\n            bgcolor: \"white\",\n            borderBottom: \"1px solid\",\n            borderColor: \"grey.200\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              gap: 2,\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                gap: 2,\n                alignItems: \"center\"\n              },\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                placeholder: t('users.searchPlaceholder'),\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(Search, {\n                    sx: {\n                      mr: 1,\n                      color: \"text.secondary\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 37\n                  }, this)\n                },\n                sx: {\n                  width: 400,\n                  \"& .MuiOutlinedInput-root\": {\n                    borderRadius: 3,\n                    bgcolor: \"grey.50\"\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 28\n              }, this),\n              onClick: () => window.location.href = \"/users/add\",\n              sx: {\n                borderRadius: 3,\n                background: \"linear-gradient(135deg, #1976d2, #42a5f5)\",\n                boxShadow: \"0 8px 24px rgba(25, 118, 210, 0.3)\",\n                \"&:hover\": {\n                  transform: \"translateY(-2px)\",\n                  boxShadow: \"0 12px 32px rgba(25, 118, 210, 0.4)\"\n                }\n              },\n              children: t('users.addUser')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            p: 6\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 48,\n              thickness: 4\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mt: 2,\n                color: \"text.secondary\"\n              },\n              children: t('common.loading')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this) : filteredUsers.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: \"center\",\n            p: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(People, {\n            sx: {\n              fontSize: 64,\n              color: \"text.secondary\",\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: t('users.noUsersFound')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: searchTerm ? t('users.noSearchResults') : t('users.startByAddingUser')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 54\n            }, this),\n            onClick: () => window.location.href = \"/users/add\",\n            children: t('users.addUser')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: \"grey.50\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    fontSize: \"1rem\"\n                  },\n                  children: t('users.user')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    fontSize: \"1rem\"\n                  },\n                  children: t('users.email')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    fontSize: \"1rem\"\n                  },\n                  children: t('users.role')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    fontSize: \"1rem\"\n                  },\n                  children: t('users.status')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 600,\n                    fontSize: \"1rem\"\n                  },\n                  children: t('common.actions')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredUsers.map(user => {\n                var _user$email2;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  sx: {\n                    \"&:hover\": {\n                      bgcolor: alpha(theme.palette.primary.main, 0.04)\n                    },\n                    transition: \"background-color 0.2s ease\",\n                    bgcolor: user.isActive ? \"inherit\" : alpha(\"#f5f5f5\", 0.7)\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        src: user.profilePic ? `http://localhost:8000/uploads${user.profilePic}` : undefined,\n                        sx: {\n                          bgcolor: \"#388e3c\",\n                          width: 50,\n                          height: 50,\n                          fontWeight: 600,\n                          border: \"2px solid #388e3c\"\n                        },\n                        children: getInitials(user.name || user.email)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 446,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body1\",\n                          sx: {\n                            fontWeight: 600\n                          },\n                          children: user.name || ((_user$email2 = user.email) === null || _user$email2 === void 0 ? void 0 : _user$email2.split(\"@\")[0])\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: [\"ID: \", user.id]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 462,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: t(`role.${(user.role || 'user').toLowerCase()}`),\n                      color: user.role === \"Admin\" ? \"primary\" : user.role === \"Instructor\" ? \"info\" : \"default\",\n                      size: \"small\",\n                      sx: {\n                        borderRadius: 2,\n                        fontWeight: 600,\n                        textTransform: \"capitalize\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: user.isActive ? t('users.active') : t('users.inactive'),\n                      color: user.isActive ? \"success\" : \"error\",\n                      size: \"small\",\n                      sx: {\n                        borderRadius: 2,\n                        fontWeight: 600\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: \"flex\",\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        sx: {\n                          color: \"info.main\",\n                          \"&:hover\": {\n                            bgcolor: alpha(\"#0288d1\", 0.1)\n                          }\n                        },\n                        onClick: () => {\n                          sessionStorage.setItem(\"viewingUser\", JSON.stringify(user));\n                          window.location.href = `/ProfilePage/${user.id}`;\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 497,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        sx: {\n                          color: \"primary.main\",\n                          \"&:hover\": {\n                            bgcolor: alpha(\"#1976d2\", 0.1)\n                          }\n                        },\n                        onClick: () => {\n                          sessionStorage.setItem(\"editingUser\", JSON.stringify(user));\n                          window.location.href = `/EditProfile/${user.id}`;\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 507,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: actionLoading,\n                        sx: {\n                          color: user.isActive ? \"warning.main\" : \"success.main\",\n                          \"&:hover\": {\n                            bgcolor: alpha(user.isActive ? \"#ed6c02\" : \"#2e7d32\", 0.1)\n                          }\n                        },\n                        onClick: () => setToggleDialog({\n                          open: true,\n                          user\n                        }),\n                        children: actionLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                          size: 16\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 46\n                        }, this) : user.isActive ? /*#__PURE__*/_jsxDEV(Block, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 95\n                        }, this) : /*#__PURE__*/_jsxDEV(LockOpen, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 107\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: actionLoading,\n                        sx: {\n                          color: \"error.main\",\n                          \"&:hover\": {\n                            bgcolor: alpha(\"#d32f2f\", 0.1)\n                          }\n                        },\n                        onClick: () => setDeleteDialog({\n                          open: true,\n                          user\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 528,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this)]\n                }, user.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: deleteDialog.open,\n        onClose: () => !actionLoading && setDeleteDialog({\n          open: false,\n          user: null\n        }),\n        PaperProps: {\n          sx: {\n            borderRadius: 3,\n            minWidth: 400\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: alpha(\"#d32f2f\", 0.1),\n                color: \"error.main\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: 600,\n              children: t('users.confirmDelete')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            children: [t('users.deleteConfirmMessage', {\n              name: ((_deleteDialog$user = deleteDialog.user) === null || _deleteDialog$user === void 0 ? void 0 : _deleteDialog$user.name) || '',\n              email: (_deleteDialog$user2 = deleteDialog.user) === null || _deleteDialog$user2 === void 0 ? void 0 : _deleteDialog$user2.email\n            }), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), t('users.irreversibleAction')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setDeleteDialog({\n              open: false,\n              user: null\n            }),\n            sx: {\n              borderRadius: 2\n            },\n            disabled: actionLoading,\n            children: t('common.cancel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleDelete,\n            color: \"error\",\n            variant: \"contained\",\n            sx: {\n              borderRadius: 2,\n              minWidth: 120\n            },\n            disabled: actionLoading,\n            children: actionLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 32\n            }, this) : t('users.deleteButton')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: toggleDialog.open,\n        onClose: () => !actionLoading && setToggleDialog({\n          open: false,\n          user: null\n        }),\n        PaperProps: {\n          sx: {\n            borderRadius: 3,\n            minWidth: 400\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: alpha((_toggleDialog$user = toggleDialog.user) !== null && _toggleDialog$user !== void 0 && _toggleDialog$user.isActive ? \"#ed6c02\" : \"#2e7d32\", 0.1),\n                color: (_toggleDialog$user2 = toggleDialog.user) !== null && _toggleDialog$user2 !== void 0 && _toggleDialog$user2.isActive ? \"warning.main\" : \"success.main\"\n              },\n              children: (_toggleDialog$user3 = toggleDialog.user) !== null && _toggleDialog$user3 !== void 0 && _toggleDialog$user3.isActive ? /*#__PURE__*/_jsxDEV(Block, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 48\n              }, this) : /*#__PURE__*/_jsxDEV(LockOpen, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 60\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: 600,\n              children: t((_toggleDialog$user4 = toggleDialog.user) !== null && _toggleDialog$user4 !== void 0 && _toggleDialog$user4.isActive ? 'users.confirmToggleTitleActive' : 'users.confirmToggleTitleInactive')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            children: [t((_toggleDialog$user5 = toggleDialog.user) !== null && _toggleDialog$user5 !== void 0 && _toggleDialog$user5.isActive ? 'users.confirmToggleMessageActive' : 'users.confirmToggleMessageInactive', {\n              name: ((_toggleDialog$user6 = toggleDialog.user) === null || _toggleDialog$user6 === void 0 ? void 0 : _toggleDialog$user6.name) || '',\n              email: ((_toggleDialog$user7 = toggleDialog.user) === null || _toggleDialog$user7 === void 0 ? void 0 : _toggleDialog$user7.email) || ''\n            }), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), \" \", (_toggleDialog$user8 = toggleDialog.user) === null || _toggleDialog$user8 === void 0 ? void 0 : _toggleDialog$user8.id, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('users.currentStatus'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this), \" \", t((_toggleDialog$user9 = toggleDialog.user) !== null && _toggleDialog$user9 !== void 0 && _toggleDialog$user9.isActive ? 'users.statusActive' : 'users.statusInactive')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setToggleDialog({\n              open: false,\n              user: null\n            }),\n            sx: {\n              borderRadius: 2\n            },\n            disabled: actionLoading,\n            children: t('common.cancel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleToggleStatus,\n            color: (_toggleDialog$user10 = toggleDialog.user) !== null && _toggleDialog$user10 !== void 0 && _toggleDialog$user10.isActive ? \"warning\" : \"success\",\n            variant: \"contained\",\n            sx: {\n              borderRadius: 2,\n              minWidth: 120\n            },\n            disabled: actionLoading,\n            children: actionLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this) : t((_toggleDialog$user11 = toggleDialog.user) !== null && _toggleDialog$user11 !== void 0 && _toggleDialog$user11.isActive ? 'users.deactivateUser' : 'users.activateUser')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n}\n_s(UserList, \"JFKEOP3+TMtjjgjN4u43dfdYusQ=\", false, function () {\n  return [useTranslation, useTheme];\n});\n_c = UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "useState", "useEffect", "useCallback", "useTranslation", "axios", "Box", "<PERSON><PERSON>", "Container", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "Grid", "IconButton", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "Typography", "Chip", "Avatar", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "Card", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "useTheme", "Add", "Visibility", "Edit", "Delete", "Block", "LockOpen", "Search", "People", "TrendingUp", "Security", "Analytics", "jsxDEV", "_jsxDEV", "UserList", "_deleteDialog$user", "_deleteDialog$user2", "_toggleDialog$user", "_toggleDialog$user2", "_toggleDialog$user3", "_toggleDialog$user4", "_toggleDialog$user5", "_toggleDialog$user6", "_toggleDialog$user7", "_toggleDialog$user8", "_toggleDialog$user9", "_toggleDialog$user10", "_toggleDialog$user11", "t", "theme", "users", "setUsers", "loading", "setLoading", "actionLoading", "setActionLoading", "searchTerm", "setSearchTerm", "deleteDialog", "setDeleteDialog", "open", "user", "toggleDialog", "setToggleDialog", "notification", "setNotification", "message", "severity", "showNotification", "fetchUsers", "console", "log", "response", "get", "data", "error", "handleDelete", "id", "delete", "filter", "u", "handleToggleStatus", "newStatus", "isActive", "email", "currentStatus", "config", "headers", "Accept", "timeout", "success", "url", "payload", "patch", "error1", "_error1$response", "_error1$response2", "status", "error2", "_error2$response", "_error2$response2", "action", "encodeURIComponent", "error3", "_error3$response", "_error3$response2", "prevUsers", "map", "setTimeout", "_error$response", "_error$response2", "_error$response3", "_error$config", "_error$config2", "_error$config3", "code", "statusText", "method", "filteredUsers", "_user$email", "_user$role", "_user$name", "toLowerCase", "includes", "role", "name", "activeUsers", "length", "getInitials", "substring", "toUpperCase", "StatCard", "title", "value", "icon", "color", "gradient", "sx", "background", "borderRadius", "boxShadow", "transition", "transform", "children", "p", "display", "alignItems", "justifyContent", "variant", "fontWeight", "mb", "fileName", "lineNumber", "columnNumber", "bgcolor", "width", "height", "minHeight", "py", "max<PERSON><PERSON><PERSON>", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "container", "spacing", "item", "xs", "sm", "md", "fontSize", "Math", "round", "overflow", "borderBottom", "borderColor", "gap", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "mr", "startIcon", "onClick", "window", "location", "href", "textAlign", "size", "thickness", "mt", "gutterBottom", "_user$email2", "hover", "palette", "primary", "main", "src", "profilePic", "undefined", "border", "split", "label", "textTransform", "sessionStorage", "setItem", "JSON", "stringify", "disabled", "PaperProps", "min<PERSON><PERSON><PERSON>", "pb", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/UserList.js"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect, useCallback } from \"react\"\r\nimport { useTranslation } from 'react-i18next'\r\nimport axios from \"axios\"\r\nimport {\r\n  Box,\r\n  Button,\r\n  Container,\r\n  Dialog,\r\n  DialogActions,\r\n  DialogContent,\r\n  DialogTitle,\r\n  Grid,\r\n  IconButton,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  TextField,\r\n  Typography,\r\n  Chip,\r\n  Avatar,\r\n  CircularProgress,\r\n  Alert,\r\n  Snackbar,\r\n  Card,\r\n  CardContent,\r\n  alpha,\r\n  useTheme,\r\n} from \"@mui/material\"\r\nimport {\r\n  Add,\r\n  // Refresh, // Unused import\r\n  Visibility,\r\n  Edit,\r\n  Delete,\r\n  Block,\r\n  LockOpen,\r\n  Search,\r\n  People,\r\n  TrendingUp,\r\n  Security,\r\n  Analytics,\r\n} from \"@mui/icons-material\"\r\n\r\nexport default function UserList() {\r\n  const { t } = useTranslation()\r\n  const theme = useTheme()\r\n  const [users, setUsers] = useState([])\r\n  const [loading, setLoading] = useState(true)\r\n  const [actionLoading, setActionLoading] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState(\"\")\r\n  const [deleteDialog, setDeleteDialog] = useState({ open: false, user: null })\r\n  const [toggleDialog, setToggleDialog] = useState({ open: false, user: null })\r\n  const [notification, setNotification] = useState({ open: false, message: \"\", severity: \"info\" })\r\n\r\n  const showNotification = (message, severity = \"info\") => {\r\n    setNotification({ open: true, message, severity })\r\n  }\r\n\r\n  const fetchUsers = useCallback(async () => {\r\n    setLoading(true)\r\n    try {\r\n      console.log(\"🔄 Fetching users...\")\r\n      const response = await axios.get(\"http://localhost:8000/users\")\r\n      console.log(\"✅ Users fetched:\", response.data)\r\n      setUsers(response.data)\r\n    } catch (error) {\r\n      console.error(\"❌ Fetch error:\", error)\r\n      showNotification(t('users.loadError'), \"error\")\r\n      setUsers([])\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }, [t])\r\n\r\n  useEffect(() => {\r\n    fetchUsers()\r\n  }, [fetchUsers])\r\n\r\n  const handleDelete = async () => {\r\n    if (!deleteDialog.user) return\r\n\r\n    setActionLoading(true)\r\n    try {\r\n      console.log(\"🗑️ Deleting user:\", deleteDialog.user.id)\r\n      await axios.delete(`http://localhost:8000/users/${deleteDialog.user.id}`)\r\n      setUsers(users.filter((u) => u.id !== deleteDialog.user.id))\r\n      showNotification(t('users.deleteSuccess'), \"success\")\r\n      console.log(\"✅ User deleted successfully\")\r\n    } catch (error) {\r\n      console.error(\"❌ Delete error:\", error)\r\n      showNotification(t('users.deleteError'), \"error\")\r\n    } finally {\r\n      setActionLoading(false)\r\n      setDeleteDialog({ open: false, user: null })\r\n    }\r\n  }\r\n\r\n  // Version simplifiée et plus robuste pour le toggle\r\n  const handleToggleStatus = async () => {\r\n    if (!toggleDialog.user) return\r\n\r\n    const { user } = toggleDialog\r\n    setActionLoading(true)\r\n\r\n    try {\r\n      const newStatus = !user.isActive\r\n      console.log(\"🔄 Starting status toggle...\")\r\n      console.log(\"👤 User:\", { id: user.id, email: user.email, currentStatus: user.isActive })\r\n      console.log(\"🎯 Target status:\", newStatus)\r\n\r\n      // Configuration axios avec headers explicites\r\n      const config = {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Accept: \"application/json\",\r\n        },\r\n        timeout: 10000, // 10 secondes de timeout\r\n      }\r\n\r\n      let success = false\r\n      let response = null\r\n\r\n      // Méthode 1: Utiliser l'endpoint de mise à jour par ID (le plus simple)\r\n      try {\r\n        const url = `http://localhost:8000/users/${user.id}`\r\n        const payload = { isActive: newStatus }\r\n\r\n        console.log(\"🌐 Method 1 - Update by ID:\")\r\n        console.log(\"📍 URL:\", url)\r\n        console.log(\"📦 Payload:\", payload)\r\n\r\n        response = await axios.patch(url, payload, config)\r\n        success = true\r\n        console.log(\"✅ Method 1 SUCCESS\")\r\n        console.log(\"📥 Response:\", response.data)\r\n      } catch (error1) {\r\n        console.log(\"❌ Method 1 FAILED:\")\r\n        console.log(\"📊 Status:\", error1.response?.status)\r\n        console.log(\"📝 Message:\", error1.message)\r\n        console.log(\"📋 Data:\", error1.response?.data)\r\n\r\n        // Méthode 2: Utiliser l'endpoint toggle-status par ID\r\n        try {\r\n          const url = `http://localhost:8000/users/${user.id}/toggle-status`\r\n          const payload = { isActive: newStatus }\r\n\r\n          console.log(\"🌐 Method 2 - Toggle by ID:\")\r\n          console.log(\"📍 URL:\", url)\r\n          console.log(\"📦 Payload:\", payload)\r\n\r\n          response = await axios.patch(url, payload, config)\r\n          success = true\r\n          console.log(\"✅ Method 2 SUCCESS\")\r\n          console.log(\"📥 Response:\", response.data)\r\n        } catch (error2) {\r\n          console.log(\"❌ Method 2 FAILED:\")\r\n          console.log(\"📊 Status:\", error2.response?.status)\r\n          console.log(\"📝 Message:\", error2.message)\r\n          console.log(\"📋 Data:\", error2.response?.data)\r\n\r\n          // Méthode 3: Utiliser l'endpoint spécifique activate/deactivate\r\n          try {\r\n            const action = newStatus ? \"activate\" : \"deactivate\"\r\n            const url = `http://localhost:8000/users/email/${encodeURIComponent(user.email)}/${action}`\r\n\r\n            console.log(\"🌐 Method 3 - Specific action:\")\r\n            console.log(\"📍 URL:\", url)\r\n            console.log(\"🎬 Action:\", action)\r\n\r\n            response = await axios.patch(url, {}, config)\r\n            success = true\r\n            console.log(\"✅ Method 3 SUCCESS\")\r\n            console.log(\"📥 Response:\", response.data)\r\n          } catch (error3) {\r\n            console.log(\"❌ Method 3 FAILED:\")\r\n            console.log(\"📊 Status:\", error3.response?.status)\r\n            console.log(\"📝 Message:\", error3.message)\r\n            console.log(\"📋 Data:\", error3.response?.data)\r\n\r\n            // Méthode 4: Mise à jour optimiste (fallback local)\r\n            console.log(\"🔄 All API methods failed, using optimistic update...\")\r\n            success = true\r\n            response = { data: { ...user, isActive: newStatus } }\r\n            console.log(\"⚠️ Using local fallback\")\r\n          }\r\n        }\r\n      }\r\n\r\n      if (success) {\r\n        // Mettre à jour l'état local\r\n        setUsers((prevUsers) => prevUsers.map((u) => (u.id === user.id ? { ...u, isActive: newStatus } : u)))\r\n\r\n        showNotification(t('users.statusUpdateSuccess'), \"success\")\r\n        console.log(\"🎉 Status toggle completed successfully\")\r\n\r\n        // Rafraîchir la liste après un délai pour vérifier la synchronisation\r\n        setTimeout(() => {\r\n          console.log(\"🔄 Refreshing user list to verify changes...\")\r\n          fetchUsers()\r\n        }, 1000)\r\n      }\r\n    } catch (error) {\r\n      console.error(\"💥 Unexpected error:\", error)\r\n\r\n      showNotification(t('users.statusUpdateError'), \"error\")\r\n\r\n      // Log détaillé pour le debugging\r\n      console.error(\"🔍 Error debugging info:\", {\r\n        message: error.message,\r\n        code: error.code,\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        config: {\r\n          url: error.config?.url,\r\n          method: error.config?.method,\r\n          data: error.config?.data,\r\n        },\r\n      })\r\n    } finally {\r\n      setActionLoading(false)\r\n      setToggleDialog({ open: false, user: null })\r\n    }\r\n  }\r\n\r\n  const filteredUsers = users.filter(\r\n    (user) =>\r\n      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      user.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      user.name?.toLowerCase().includes(searchTerm.toLowerCase()),\r\n  )\r\n\r\n  const activeUsers = users.filter((u) => u.isActive).length\r\n  const getInitials = (email) => email?.substring(0, 2).toUpperCase() || \"??\"\r\n  // const getAvatarColor = (email) => {\r\n  //   return \"#388e3c\" // Couleur verte\r\n  // } // Unused function\r\n\r\n  const StatCard = ({ title, value, icon, color, gradient }) => (\r\n    <Card\r\n      sx={{\r\n        background: gradient,\r\n        borderRadius: 3,\r\n        boxShadow: `0 8px 32px ${alpha(color, 0.2)}`,\r\n        transition: \"transform 0.2s ease\",\r\n        \"&:hover\": { transform: \"translateY(-4px)\" },\r\n      }}\r\n    >\r\n      <CardContent sx={{ p: 3 }}>\r\n        <Box sx={{ display: \"flex\", alignItems: \"center\", justifyContent: \"space-between\" }}>\r\n          <Box>\r\n            <Typography variant=\"h3\" sx={{ fontWeight: 700, color, mb: 1 }}>\r\n              {value}\r\n            </Typography>\r\n            <Typography variant=\"body1\" sx={{ fontWeight: 500, color: \"text.secondary\" }}>\r\n              {title}\r\n            </Typography>\r\n          </Box>\r\n          <Avatar sx={{ bgcolor: alpha(color, 0.1), color, width: 56, height: 56 }}>{icon}</Avatar>\r\n        </Box>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n\r\n  // Test de connectivité au backend\r\n  \r\n\r\n  return (\r\n    <Box sx={{ minHeight: \"100vh\", bgcolor: \"grey.50\", py: 4 }}>\r\n      <Container maxWidth=\"xl\">\r\n        <Snackbar\r\n          open={notification.open}\r\n          autoHideDuration={6000}\r\n          onClose={() => setNotification({ ...notification, open: false })}\r\n          anchorOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n        >\r\n          <Alert severity={notification.severity} sx={{ borderRadius: 2 }}>\r\n            {notification.message}\r\n          </Alert>\r\n        </Snackbar>\r\n\r\n        {/* Header */}\r\n        <Box sx={{ mb: 4, display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\" }}>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h3\"\r\n              sx={{\r\n                fontWeight: 800,\r\n                background: \"linear-gradient(135deg, #1976d2, #42a5f5)\",\r\n                backgroundClip: \"text\",\r\n                WebkitBackgroundClip: \"text\",\r\n                WebkitTextFillColor: \"transparent\",\r\n                mb: 1,\r\n              }}\r\n            >\r\n              {t('users.userManagement')}\r\n            </Typography>\r\n            <Typography variant=\"h6\" color=\"text.secondary\">\r\n              {t('users.usersSection')}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* Stats Cards */}\r\n        <Grid container spacing={3} sx={{ mb: 4 }}>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              title={t('users.totalUsers')}\r\n              value={users.length}\r\n              icon={<People fontSize=\"large\" />}\r\n              color=\"#1976d2\"\r\n              gradient=\"linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(25, 118, 210, 0.05))\"\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              title={t('users.activeAccounts')}\r\n              value={activeUsers}\r\n              icon={<TrendingUp fontSize=\"large\" />}\r\n              color=\"#388e3c\"\r\n              gradient=\"linear-gradient(135deg, rgba(56, 142, 60, 0.1), rgba(56, 142, 60, 0.05))\"\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              title={t('users.inactiveAccounts')}\r\n              value={users.length - activeUsers}\r\n              icon={<Security fontSize=\"large\" />}\r\n              color=\"#d32f2f\"\r\n              gradient=\"linear-gradient(135deg, rgba(211, 47, 47, 0.1), rgba(211, 47, 47, 0.05))\"\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={6} md={3}>\r\n            <StatCard\r\n              title={t('users.activityRate')}\r\n              value={`${users.length > 0 ? Math.round((activeUsers / users.length) * 100) : 0}%`}\r\n              icon={<Analytics fontSize=\"large\" />}\r\n              color=\"#7b1fa2\"\r\n              gradient=\"linear-gradient(135deg, rgba(123, 31, 162, 0.1), rgba(123, 31, 162, 0.05))\"\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* Main Content */}\r\n        <Paper\r\n          sx={{\r\n            borderRadius: 4,\r\n            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.08)\",\r\n            overflow: \"hidden\",\r\n          }}\r\n        >\r\n          {/* Search & Actions */}\r\n          <Box sx={{ p: 3, bgcolor: \"white\", borderBottom: \"1px solid\", borderColor: \"grey.200\" }}>\r\n            <Box sx={{ display: \"flex\", gap: 2, alignItems: \"center\", justifyContent: \"space-between\" }}>\r\n              <Box sx={{ display: \"flex\", gap: 2, alignItems: \"center\" }}>\r\n                <TextField\r\n                  placeholder={t('users.searchPlaceholder')}\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  InputProps={{\r\n                    startAdornment: <Search sx={{ mr: 1, color: \"text.secondary\" }} />,\r\n                  }}\r\n                  sx={{\r\n                    width: 400,\r\n                    \"& .MuiOutlinedInput-root\": {\r\n                      borderRadius: 3,\r\n                      bgcolor: \"grey.50\",\r\n                    },\r\n                  }}\r\n                />\r\n                \r\n              </Box>\r\n              <Button\r\n                variant=\"contained\"\r\n                startIcon={<Add />}\r\n                onClick={() => (window.location.href = \"/users/add\")}\r\n                sx={{\r\n                  borderRadius: 3,\r\n                  background: \"linear-gradient(135deg, #1976d2, #42a5f5)\",\r\n                  boxShadow: \"0 8px 24px rgba(25, 118, 210, 0.3)\",\r\n                  \"&:hover\": {\r\n                    transform: \"translateY(-2px)\",\r\n                    boxShadow: \"0 12px 32px rgba(25, 118, 210, 0.4)\",\r\n                  },\r\n                }}\r\n              >\r\n                {t('users.addUser')}\r\n              </Button>\r\n            </Box>\r\n          </Box>\r\n\r\n          {/* Users Table */}\r\n          {loading ? (\r\n            <Box sx={{ display: \"flex\", justifyContent: \"center\", p: 6 }}>\r\n              <Box sx={{ textAlign: \"center\" }}>\r\n                <CircularProgress size={48} thickness={4} />\r\n                <Typography variant=\"h6\" sx={{ mt: 2, color: \"text.secondary\" }}>\r\n                  {t('common.loading')}\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n          ) : filteredUsers.length === 0 ? (\r\n            <Box sx={{ textAlign: \"center\", p: 6 }}>\r\n              <People sx={{ fontSize: 64, color: \"text.secondary\", mb: 2 }} />\r\n              <Typography variant=\"h5\" gutterBottom>\r\n                {t('users.noUsersFound')}\r\n              </Typography>\r\n              <Typography color=\"text.secondary\" sx={{ mb: 3 }}>\r\n                {searchTerm ? t('users.noSearchResults') : t('users.startByAddingUser')}\r\n              </Typography>\r\n              <Button variant=\"contained\" startIcon={<Add />} onClick={() => (window.location.href = \"/users/add\")}>\r\n                {t('users.addUser')}\r\n              </Button>\r\n            </Box>\r\n          ) : (\r\n            <TableContainer>\r\n              <Table>\r\n                <TableHead>\r\n                  <TableRow sx={{ bgcolor: \"grey.50\" }}>\r\n                    <TableCell sx={{ fontWeight: 600, fontSize: \"1rem\" }}>{t('users.user')}</TableCell>\r\n                    <TableCell sx={{ fontWeight: 600, fontSize: \"1rem\" }}>{t('users.email')}</TableCell>\r\n                    <TableCell sx={{ fontWeight: 600, fontSize: \"1rem\" }}>{t('users.role')}</TableCell>\r\n                    <TableCell sx={{ fontWeight: 600, fontSize: \"1rem\" }}>{t('users.status')}</TableCell>\r\n                    <TableCell sx={{ fontWeight: 600, fontSize: \"1rem\" }}>{t('common.actions')}</TableCell>\r\n                  </TableRow>\r\n                </TableHead>\r\n                <TableBody>\r\n                  {filteredUsers.map((user) => (\r\n                    <TableRow\r\n                      key={user.id}\r\n                      hover\r\n                      sx={{\r\n                        \"&:hover\": { bgcolor: alpha(theme.palette.primary.main, 0.04) },\r\n                        transition: \"background-color 0.2s ease\",\r\n                        bgcolor: user.isActive ? \"inherit\" : alpha(\"#f5f5f5\", 0.7),\r\n                      }}\r\n                    >\r\n                      <TableCell>\r\n                        <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n                          <Avatar\r\n                            src={user.profilePic ? `http://localhost:8000/uploads${user.profilePic}` : undefined}\r\n                            sx={{\r\n                              bgcolor: \"#388e3c\",\r\n                              width: 50,\r\n                              height: 50,\r\n                              fontWeight: 600,\r\n                              border: \"2px solid #388e3c\",\r\n                            }}\r\n                          >\r\n                            {getInitials(user.name || user.email)}\r\n                          </Avatar>\r\n                          <Box>\r\n                            <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>\r\n                              {user.name || user.email?.split(\"@\")[0]}\r\n                            </Typography>\r\n                            <Typography variant=\"body2\" color=\"text.secondary\">\r\n                              ID: {user.id}\r\n                            </Typography>\r\n                          </Box>\r\n                        </Box>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <Typography variant=\"body1\">{user.email}</Typography>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <Chip\r\n                          label={t(`role.${(user.role || 'user').toLowerCase()}`)}\r\n                          color={user.role === \"Admin\" ? \"primary\" : user.role === \"Instructor\" ? \"info\" : \"default\"}\r\n                          size=\"small\"\r\n                          sx={{ borderRadius: 2, fontWeight: 600, textTransform: \"capitalize\" }}\r\n                        />\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <Chip\r\n                          label={user.isActive ? t('users.active') : t('users.inactive')}\r\n                          color={user.isActive ? \"success\" : \"error\"}\r\n                          size=\"small\"\r\n                          sx={{ borderRadius: 2, fontWeight: 600 }}\r\n                        />\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <Box sx={{ display: \"flex\", gap: 0.5 }}>\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            sx={{ color: \"info.main\", \"&:hover\": { bgcolor: alpha(\"#0288d1\", 0.1) } }}\r\n                            onClick={() => {\r\n                              sessionStorage.setItem(\"viewingUser\", JSON.stringify(user))\r\n                              window.location.href = `/ProfilePage/${user.id}`\r\n                            }}\r\n                          >\r\n                            <Visibility />\r\n                          </IconButton>\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            sx={{ color: \"primary.main\", \"&:hover\": { bgcolor: alpha(\"#1976d2\", 0.1) } }}\r\n                            onClick={() => {\r\n                              sessionStorage.setItem(\"editingUser\", JSON.stringify(user))\r\n                              window.location.href = `/EditProfile/${user.id}`\r\n                            }}\r\n                          >\r\n                            <Edit />\r\n                          </IconButton>\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            disabled={actionLoading}\r\n                            sx={{\r\n                              color: user.isActive ? \"warning.main\" : \"success.main\",\r\n                              \"&:hover\": {\r\n                                bgcolor: alpha(user.isActive ? \"#ed6c02\" : \"#2e7d32\", 0.1),\r\n                              },\r\n                            }}\r\n                            onClick={() => setToggleDialog({ open: true, user })}\r\n                          >\r\n                            {actionLoading ? <CircularProgress size={16} /> : user.isActive ? <Block /> : <LockOpen />}\r\n                          </IconButton>\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            disabled={actionLoading}\r\n                            sx={{ color: \"error.main\", \"&:hover\": { bgcolor: alpha(\"#d32f2f\", 0.1) } }}\r\n                            onClick={() => setDeleteDialog({ open: true, user })}\r\n                          >\r\n                            <Delete />\r\n                          </IconButton>\r\n                        </Box>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))}\r\n                </TableBody>\r\n              </Table>\r\n            </TableContainer>\r\n          )}\r\n        </Paper>\r\n\r\n        {/* Delete Dialog */}\r\n        <Dialog\r\n          open={deleteDialog.open}\r\n          onClose={() => !actionLoading && setDeleteDialog({ open: false, user: null })}\r\n          PaperProps={{ sx: { borderRadius: 3, minWidth: 400 } }}\r\n        >\r\n          <DialogTitle sx={{ pb: 1 }}>\r\n            <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n              <Avatar sx={{ bgcolor: alpha(\"#d32f2f\", 0.1), color: \"error.main\" }}>\r\n                <Delete />\r\n              </Avatar>\r\n              <Typography variant=\"h6\" fontWeight={600}>\r\n                {t('users.confirmDelete')}\r\n              </Typography>\r\n            </Box>\r\n          </DialogTitle>\r\n          <DialogContent>\r\n            <Typography>\r\n              {t('users.deleteConfirmMessage', { name: deleteDialog.user?.name || '', email: deleteDialog.user?.email })}\r\n              <br />\r\n              <br />\r\n              {t('users.irreversibleAction')}\r\n            </Typography>\r\n          </DialogContent>\r\n          <DialogActions sx={{ p: 3 }}>\r\n            <Button\r\n              onClick={() => setDeleteDialog({ open: false, user: null })}\r\n              sx={{ borderRadius: 2 }}\r\n              disabled={actionLoading}\r\n            >\r\n              {t('common.cancel')}\r\n            </Button>\r\n            <Button\r\n              onClick={handleDelete}\r\n              color=\"error\"\r\n              variant=\"contained\"\r\n              sx={{ borderRadius: 2, minWidth: 120 }}\r\n              disabled={actionLoading}\r\n            >\r\n              {actionLoading ? <CircularProgress size={20} color=\"inherit\" /> : t('users.deleteButton')}\r\n            </Button>\r\n          </DialogActions>\r\n        </Dialog>\r\n\r\n        {/* Toggle Status Dialog */}\r\n        <Dialog\r\n          open={toggleDialog.open}\r\n          onClose={() => !actionLoading && setToggleDialog({ open: false, user: null })}\r\n          PaperProps={{ sx: { borderRadius: 3, minWidth: 400 } }}\r\n        >\r\n          <DialogTitle sx={{ pb: 1 }}>\r\n            <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n              <Avatar\r\n                sx={{\r\n                  bgcolor: alpha(toggleDialog.user?.isActive ? \"#ed6c02\" : \"#2e7d32\", 0.1),\r\n                  color: toggleDialog.user?.isActive ? \"warning.main\" : \"success.main\",\r\n                }}\r\n              >\r\n                {toggleDialog.user?.isActive ? <Block /> : <LockOpen />}\r\n              </Avatar>\r\n              <Typography variant=\"h6\" fontWeight={600}>\r\n                {t(toggleDialog.user?.isActive ? 'users.confirmToggleTitleActive' : 'users.confirmToggleTitleInactive')}\r\n              </Typography>\r\n            </Box>\r\n          </DialogTitle>\r\n          <DialogContent>\r\n            <Typography>\r\n              {t(toggleDialog.user?.isActive ? 'users.confirmToggleMessageActive' : 'users.confirmToggleMessageInactive', {\r\n                name: toggleDialog.user?.name || '',\r\n                email: toggleDialog.user?.email || ''\r\n              })}\r\n              <br />\r\n              <br />\r\n              <strong>ID:</strong> {toggleDialog.user?.id}\r\n              <br />\r\n              <strong>{t('users.currentStatus')}:</strong> {t(toggleDialog.user?.isActive ? 'users.statusActive' : 'users.statusInactive')}\r\n            </Typography>\r\n          </DialogContent>\r\n          <DialogActions sx={{ p: 3 }}>\r\n            <Button\r\n              onClick={() => setToggleDialog({ open: false, user: null })}\r\n              sx={{ borderRadius: 2 }}\r\n              disabled={actionLoading}\r\n            >\r\n              {t('common.cancel')}\r\n            </Button>\r\n            <Button\r\n              onClick={handleToggleStatus}\r\n              color={toggleDialog.user?.isActive ? \"warning\" : \"success\"}\r\n              variant=\"contained\"\r\n              sx={{ borderRadius: 2, minWidth: 120 }}\r\n              disabled={actionLoading}\r\n            >\r\n              {actionLoading ? (\r\n                <CircularProgress size={20} color=\"inherit\" />\r\n              ) : t(toggleDialog.user?.isActive ? 'users.deactivateUser' : 'users.activateUser')}\r\n            </Button>\r\n          </DialogActions>\r\n        </Dialog>\r\n      </Container>\r\n    </Box>\r\n  )\r\n}\r\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AAEZ,SAASC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG;AACH;AACAC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,QAAQ,EACRC,SAAS,QACJ,qBAAqB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE5B,eAAe,SAASC,QAAQA,CAAA,EAAG;EAAA/C,EAAA;EAAA,IAAAgD,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA;EACjC,MAAM;IAAEC;EAAE,CAAC,GAAGxD,cAAc,CAAC,CAAC;EAC9B,MAAMyD,KAAK,GAAG7B,QAAQ,CAAC,CAAC;EACxB,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC;IAAEuE,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;EAC7E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC;IAAEuE,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;EAC7E,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC;IAAEuE,IAAI,EAAE,KAAK;IAAEM,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EAEhG,MAAMC,gBAAgB,GAAGA,CAACF,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACvDF,eAAe,CAAC;MAAEL,IAAI,EAAE,IAAI;MAAEM,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,UAAU,GAAG9E,WAAW,CAAC,YAAY;IACzC8D,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFiB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAMC,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAC,6BAA6B,CAAC;MAC/DH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC9CvB,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCP,gBAAgB,CAACpB,CAAC,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC;MAC/CG,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,CAAC,CAAC,CAAC;EAEP1D,SAAS,CAAC,MAAM;IACd+E,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAClB,YAAY,CAACG,IAAI,EAAE;IAExBN,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACFe,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEb,YAAY,CAACG,IAAI,CAACgB,EAAE,CAAC;MACvD,MAAMpF,KAAK,CAACqF,MAAM,CAAC,+BAA+BpB,YAAY,CAACG,IAAI,CAACgB,EAAE,EAAE,CAAC;MACzE1B,QAAQ,CAACD,KAAK,CAAC6B,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACH,EAAE,KAAKnB,YAAY,CAACG,IAAI,CAACgB,EAAE,CAAC,CAAC;MAC5DT,gBAAgB,CAACpB,CAAC,CAAC,qBAAqB,CAAC,EAAE,SAAS,CAAC;MACrDsB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCP,gBAAgB,CAACpB,CAAC,CAAC,mBAAmB,CAAC,EAAE,OAAO,CAAC;IACnD,CAAC,SAAS;MACRO,gBAAgB,CAAC,KAAK,CAAC;MACvBI,eAAe,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMoB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACnB,YAAY,CAACD,IAAI,EAAE;IAExB,MAAM;MAAEA;IAAK,CAAC,GAAGC,YAAY;IAC7BP,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAM2B,SAAS,GAAG,CAACrB,IAAI,CAACsB,QAAQ;MAChCb,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QAAEM,EAAE,EAAEhB,IAAI,CAACgB,EAAE;QAAEO,KAAK,EAAEvB,IAAI,CAACuB,KAAK;QAAEC,aAAa,EAAExB,IAAI,CAACsB;MAAS,CAAC,CAAC;MACzFb,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEW,SAAS,CAAC;;MAE3C;MACA,MAAMI,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,MAAM,EAAE;QACV,CAAC;QACDC,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC;MAED,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAIlB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAI;QACF,MAAMmB,GAAG,GAAG,+BAA+B9B,IAAI,CAACgB,EAAE,EAAE;QACpD,MAAMe,OAAO,GAAG;UAAET,QAAQ,EAAED;QAAU,CAAC;QAEvCZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoB,GAAG,CAAC;QAC3BrB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqB,OAAO,CAAC;QAEnCpB,QAAQ,GAAG,MAAM/E,KAAK,CAACoG,KAAK,CAACF,GAAG,EAAEC,OAAO,EAAEN,MAAM,CAAC;QAClDI,OAAO,GAAG,IAAI;QACdpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;QACjCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOoB,MAAM,EAAE;QAAA,IAAAC,gBAAA,EAAAC,iBAAA;QACf1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;QACjCD,OAAO,CAACC,GAAG,CAAC,YAAY,GAAAwB,gBAAA,GAAED,MAAM,CAACtB,QAAQ,cAAAuB,gBAAA,uBAAfA,gBAAA,CAAiBE,MAAM,CAAC;QAClD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEuB,MAAM,CAAC5B,OAAO,CAAC;QAC1CI,OAAO,CAACC,GAAG,CAAC,UAAU,GAAAyB,iBAAA,GAAEF,MAAM,CAACtB,QAAQ,cAAAwB,iBAAA,uBAAfA,iBAAA,CAAiBtB,IAAI,CAAC;;QAE9C;QACA,IAAI;UACF,MAAMiB,GAAG,GAAG,+BAA+B9B,IAAI,CAACgB,EAAE,gBAAgB;UAClE,MAAMe,OAAO,GAAG;YAAET,QAAQ,EAAED;UAAU,CAAC;UAEvCZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoB,GAAG,CAAC;UAC3BrB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqB,OAAO,CAAC;UAEnCpB,QAAQ,GAAG,MAAM/E,KAAK,CAACoG,KAAK,CAACF,GAAG,EAAEC,OAAO,EAAEN,MAAM,CAAC;UAClDI,OAAO,GAAG,IAAI;UACdpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAACE,IAAI,CAAC;QAC5C,CAAC,CAAC,OAAOwB,MAAM,EAAE;UAAA,IAAAC,gBAAA,EAAAC,iBAAA;UACf9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCD,OAAO,CAACC,GAAG,CAAC,YAAY,GAAA4B,gBAAA,GAAED,MAAM,CAAC1B,QAAQ,cAAA2B,gBAAA,uBAAfA,gBAAA,CAAiBF,MAAM,CAAC;UAClD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE2B,MAAM,CAAChC,OAAO,CAAC;UAC1CI,OAAO,CAACC,GAAG,CAAC,UAAU,GAAA6B,iBAAA,GAAEF,MAAM,CAAC1B,QAAQ,cAAA4B,iBAAA,uBAAfA,iBAAA,CAAiB1B,IAAI,CAAC;;UAE9C;UACA,IAAI;YACF,MAAM2B,MAAM,GAAGnB,SAAS,GAAG,UAAU,GAAG,YAAY;YACpD,MAAMS,GAAG,GAAG,qCAAqCW,kBAAkB,CAACzC,IAAI,CAACuB,KAAK,CAAC,IAAIiB,MAAM,EAAE;YAE3F/B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoB,GAAG,CAAC;YAC3BrB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE8B,MAAM,CAAC;YAEjC7B,QAAQ,GAAG,MAAM/E,KAAK,CAACoG,KAAK,CAACF,GAAG,EAAE,CAAC,CAAC,EAAEL,MAAM,CAAC;YAC7CI,OAAO,GAAG,IAAI;YACdpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;YACjCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,QAAQ,CAACE,IAAI,CAAC;UAC5C,CAAC,CAAC,OAAO6B,MAAM,EAAE;YAAA,IAAAC,gBAAA,EAAAC,iBAAA;YACfnC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;YACjCD,OAAO,CAACC,GAAG,CAAC,YAAY,GAAAiC,gBAAA,GAAED,MAAM,CAAC/B,QAAQ,cAAAgC,gBAAA,uBAAfA,gBAAA,CAAiBP,MAAM,CAAC;YAClD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,MAAM,CAACrC,OAAO,CAAC;YAC1CI,OAAO,CAACC,GAAG,CAAC,UAAU,GAAAkC,iBAAA,GAAEF,MAAM,CAAC/B,QAAQ,cAAAiC,iBAAA,uBAAfA,iBAAA,CAAiB/B,IAAI,CAAC;;YAE9C;YACAJ,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;YACpEmB,OAAO,GAAG,IAAI;YACdlB,QAAQ,GAAG;cAAEE,IAAI,EAAE;gBAAE,GAAGb,IAAI;gBAAEsB,QAAQ,EAAED;cAAU;YAAE,CAAC;YACrDZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;UACxC;QACF;MACF;MAEA,IAAImB,OAAO,EAAE;QACX;QACAvC,QAAQ,CAAEuD,SAAS,IAAKA,SAAS,CAACC,GAAG,CAAE3B,CAAC,IAAMA,CAAC,CAACH,EAAE,KAAKhB,IAAI,CAACgB,EAAE,GAAG;UAAE,GAAGG,CAAC;UAAEG,QAAQ,EAAED;QAAU,CAAC,GAAGF,CAAE,CAAC,CAAC;QAErGZ,gBAAgB,CAACpB,CAAC,CAAC,2BAA2B,CAAC,EAAE,SAAS,CAAC;QAC3DsB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;QAEtD;QACAqC,UAAU,CAAC,MAAM;UACftC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3DF,UAAU,CAAC,CAAC;QACd,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA,IAAAkC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;MACd5C,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5CP,gBAAgB,CAACpB,CAAC,CAAC,yBAAyB,CAAC,EAAE,OAAO,CAAC;;MAEvD;MACAsB,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAE;QACxCT,OAAO,EAAES,KAAK,CAACT,OAAO;QACtBiD,IAAI,EAAExC,KAAK,CAACwC,IAAI;QAChBlB,MAAM,GAAAY,eAAA,GAAElC,KAAK,CAACH,QAAQ,cAAAqC,eAAA,uBAAdA,eAAA,CAAgBZ,MAAM;QAC9BmB,UAAU,GAAAN,gBAAA,GAAEnC,KAAK,CAACH,QAAQ,cAAAsC,gBAAA,uBAAdA,gBAAA,CAAgBM,UAAU;QACtC1C,IAAI,GAAAqC,gBAAA,GAAEpC,KAAK,CAACH,QAAQ,cAAAuC,gBAAA,uBAAdA,gBAAA,CAAgBrC,IAAI;QAC1BY,MAAM,EAAE;UACNK,GAAG,GAAAqB,aAAA,GAAErC,KAAK,CAACW,MAAM,cAAA0B,aAAA,uBAAZA,aAAA,CAAcrB,GAAG;UACtB0B,MAAM,GAAAJ,cAAA,GAAEtC,KAAK,CAACW,MAAM,cAAA2B,cAAA,uBAAZA,cAAA,CAAcI,MAAM;UAC5B3C,IAAI,GAAAwC,cAAA,GAAEvC,KAAK,CAACW,MAAM,cAAA4B,cAAA,uBAAZA,cAAA,CAAcxC;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnB,gBAAgB,CAAC,KAAK,CAAC;MACvBQ,eAAe,CAAC;QAAEH,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMyD,aAAa,GAAGpE,KAAK,CAAC6B,MAAM,CAC/BlB,IAAI;IAAA,IAAA0D,WAAA,EAAAC,UAAA,EAAAC,UAAA;IAAA,OACH,EAAAF,WAAA,GAAA1D,IAAI,CAACuB,KAAK,cAAAmC,WAAA,uBAAVA,WAAA,CAAYG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnE,UAAU,CAACkE,WAAW,CAAC,CAAC,CAAC,OAAAF,UAAA,GAC5D3D,IAAI,CAAC+D,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnE,UAAU,CAACkE,WAAW,CAAC,CAAC,CAAC,OAAAD,UAAA,GAC3D5D,IAAI,CAACgE,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnE,UAAU,CAACkE,WAAW,CAAC,CAAC,CAAC;EAAA,CAC/D,CAAC;EAED,MAAMI,WAAW,GAAG5E,KAAK,CAAC6B,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACG,QAAQ,CAAC,CAAC4C,MAAM;EAC1D,MAAMC,WAAW,GAAI5C,KAAK,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,IAAI;EAC3E;EACA;EACA;;EAEA,MAAMC,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAS,CAAC,kBACvDvG,OAAA,CAAChB,IAAI;IACHwH,EAAE,EAAE;MACFC,UAAU,EAAEF,QAAQ;MACpBG,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,cAAczH,KAAK,CAACoH,KAAK,EAAE,GAAG,CAAC,EAAE;MAC5CM,UAAU,EAAE,qBAAqB;MACjC,SAAS,EAAE;QAAEC,SAAS,EAAE;MAAmB;IAC7C,CAAE;IAAAC,QAAA,eAEF9G,OAAA,CAACf,WAAW;MAACuH,EAAE,EAAE;QAAEO,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,eACxB9G,OAAA,CAACvC,GAAG;QAAC+I,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAJ,QAAA,gBAClF9G,OAAA,CAACvC,GAAG;UAAAqJ,QAAA,gBACF9G,OAAA,CAACtB,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,GAAG;cAAEd,KAAK;cAAEe,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAC5DV;UAAK;YAAAkB,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACbxH,OAAA,CAACtB,UAAU;YAACyI,OAAO,EAAC,OAAO;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,GAAG;cAAEd,KAAK,EAAE;YAAiB,CAAE;YAAAQ,QAAA,EAC1EX;UAAK;YAAAmB,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNxH,OAAA,CAACpB,MAAM;UAAC4H,EAAE,EAAE;YAAEiB,OAAO,EAAEvI,KAAK,CAACoH,KAAK,EAAE,GAAG,CAAC;YAAEA,KAAK;YAAEoB,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAE;UAAAb,QAAA,EAAET;QAAI;UAAAiB,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAF,QAAA,EAAArK,YAAA;MAAAsK,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAF,QAAA,EAAArK,YAAA;IAAAsK,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;;EAED;;EAGA,oBACExH,OAAA,CAACvC,GAAG;IAAC+I,EAAE,EAAE;MAAEoB,SAAS,EAAE,OAAO;MAAEH,OAAO,EAAE,SAAS;MAAEI,EAAE,EAAE;IAAE,CAAE;IAAAf,QAAA,eACzD9G,OAAA,CAACrC,SAAS;MAACmK,QAAQ,EAAC,IAAI;MAAAhB,QAAA,gBACtB9G,OAAA,CAACjB,QAAQ;QACP4C,IAAI,EAAEI,YAAY,CAACJ,IAAK;QACxBoG,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAEA,CAAA,KAAMhG,eAAe,CAAC;UAAE,GAAGD,YAAY;UAAEJ,IAAI,EAAE;QAAM,CAAC,CAAE;QACjEsG,YAAY,EAAE;UAAEC,QAAQ,EAAE,KAAK;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAArB,QAAA,eAEvD9G,OAAA,CAAClB,KAAK;UAACoD,QAAQ,EAAEH,YAAY,CAACG,QAAS;UAACsE,EAAE,EAAE;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAI,QAAA,EAC7D/E,YAAY,CAACE;QAAO;UAAAqF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGXxH,OAAA,CAACvC,GAAG;QAAC+I,EAAE,EAAE;UAAEa,EAAE,EAAE,CAAC;UAAEL,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,eACzF9G,OAAA,CAACvC,GAAG;UAAAqJ,QAAA,gBACF9G,OAAA,CAACtB,UAAU;YACTyI,OAAO,EAAC,IAAI;YACZX,EAAE,EAAE;cACFY,UAAU,EAAE,GAAG;cACfX,UAAU,EAAE,2CAA2C;cACvD2B,cAAc,EAAE,MAAM;cACtBC,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCjB,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAED/F,CAAC,CAAC,sBAAsB;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbxH,OAAA,CAACtB,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACb,KAAK,EAAC,gBAAgB;YAAAQ,QAAA,EAC5C/F,CAAC,CAAC,oBAAoB;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxH,OAAA,CAAChC,IAAI;QAACuK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAChC,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACxC9G,OAAA,CAAChC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eAC9B9G,OAAA,CAACkG,QAAQ;YACPC,KAAK,EAAEpF,CAAC,CAAC,kBAAkB,CAAE;YAC7BqF,KAAK,EAAEnF,KAAK,CAAC6E,MAAO;YACpBO,IAAI,eAAErG,OAAA,CAACL,MAAM;cAACkJ,QAAQ,EAAC;YAAO;cAAAvB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClClB,KAAK,EAAC,SAAS;YACfC,QAAQ,EAAC;UAA4E;YAAAe,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPxH,OAAA,CAAChC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eAC9B9G,OAAA,CAACkG,QAAQ;YACPC,KAAK,EAAEpF,CAAC,CAAC,sBAAsB,CAAE;YACjCqF,KAAK,EAAEP,WAAY;YACnBQ,IAAI,eAAErG,OAAA,CAACJ,UAAU;cAACiJ,QAAQ,EAAC;YAAO;cAAAvB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtClB,KAAK,EAAC,SAAS;YACfC,QAAQ,EAAC;UAA0E;YAAAe,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACpF;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPxH,OAAA,CAAChC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eAC9B9G,OAAA,CAACkG,QAAQ;YACPC,KAAK,EAAEpF,CAAC,CAAC,wBAAwB,CAAE;YACnCqF,KAAK,EAAEnF,KAAK,CAAC6E,MAAM,GAAGD,WAAY;YAClCQ,IAAI,eAAErG,OAAA,CAACH,QAAQ;cAACgJ,QAAQ,EAAC;YAAO;cAAAvB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpClB,KAAK,EAAC,SAAS;YACfC,QAAQ,EAAC;UAA0E;YAAAe,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACpF;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPxH,OAAA,CAAChC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eAC9B9G,OAAA,CAACkG,QAAQ;YACPC,KAAK,EAAEpF,CAAC,CAAC,oBAAoB,CAAE;YAC/BqF,KAAK,EAAE,GAAGnF,KAAK,CAAC6E,MAAM,GAAG,CAAC,GAAGgD,IAAI,CAACC,KAAK,CAAElD,WAAW,GAAG5E,KAAK,CAAC6E,MAAM,GAAI,GAAG,CAAC,GAAG,CAAC,GAAI;YACnFO,IAAI,eAAErG,OAAA,CAACF,SAAS;cAAC+I,QAAQ,EAAC;YAAO;cAAAvB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrClB,KAAK,EAAC,SAAS;YACfC,QAAQ,EAAC;UAA4E;YAAAe,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACtF;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPxH,OAAA,CAAC9B,KAAK;QACJsI,EAAE,EAAE;UACFE,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,gCAAgC;UAC3CqC,QAAQ,EAAE;QACZ,CAAE;QAAAlC,QAAA,gBAGF9G,OAAA,CAACvC,GAAG;UAAC+I,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAEU,OAAO,EAAE,OAAO;YAAEwB,YAAY,EAAE,WAAW;YAAEC,WAAW,EAAE;UAAW,CAAE;UAAApC,QAAA,eACtF9G,OAAA,CAACvC,GAAG;YAAC+I,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEmC,GAAG,EAAE,CAAC;cAAElC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE;YAAgB,CAAE;YAAAJ,QAAA,gBAC1F9G,OAAA,CAACvC,GAAG;cAAC+I,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEmC,GAAG,EAAE,CAAC;gBAAElC,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,eACzD9G,OAAA,CAACvB,SAAS;gBACR2K,WAAW,EAAErI,CAAC,CAAC,yBAAyB,CAAE;gBAC1CqF,KAAK,EAAE7E,UAAW;gBAClB8H,QAAQ,EAAGC,CAAC,IAAK9H,aAAa,CAAC8H,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;gBAC/CoD,UAAU,EAAE;kBACVC,cAAc,eAAEzJ,OAAA,CAACN,MAAM;oBAAC8G,EAAE,EAAE;sBAAEkD,EAAE,EAAE,CAAC;sBAAEpD,KAAK,EAAE;oBAAiB;kBAAE;oBAAAgB,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACnE,CAAE;gBACFhB,EAAE,EAAE;kBACFkB,KAAK,EAAE,GAAG;kBACV,0BAA0B,EAAE;oBAC1BhB,YAAY,EAAE,CAAC;oBACfe,OAAO,EAAE;kBACX;gBACF;cAAE;gBAAAH,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAF,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAEC,CAAC,eACNxH,OAAA,CAACtC,MAAM;cACLyJ,OAAO,EAAC,WAAW;cACnBwC,SAAS,eAAE3J,OAAA,CAACZ,GAAG;gBAAAkI,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBoC,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAc;cACrDvD,EAAE,EAAE;gBACFE,YAAY,EAAE,CAAC;gBACfD,UAAU,EAAE,2CAA2C;gBACvDE,SAAS,EAAE,oCAAoC;gBAC/C,SAAS,EAAE;kBACTE,SAAS,EAAE,kBAAkB;kBAC7BF,SAAS,EAAE;gBACb;cACF,CAAE;cAAAG,QAAA,EAED/F,CAAC,CAAC,eAAe;YAAC;cAAAuG,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAF,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLrG,OAAO,gBACNnB,OAAA,CAACvC,GAAG;UAAC+I,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,QAAQ;YAAEH,CAAC,EAAE;UAAE,CAAE;UAAAD,QAAA,eAC3D9G,OAAA,CAACvC,GAAG;YAAC+I,EAAE,EAAE;cAAEwD,SAAS,EAAE;YAAS,CAAE;YAAAlD,QAAA,gBAC/B9G,OAAA,CAACnB,gBAAgB;cAACoL,IAAI,EAAE,EAAG;cAACC,SAAS,EAAE;YAAE;cAAA5C,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CxH,OAAA,CAACtB,UAAU;cAACyI,OAAO,EAAC,IAAI;cAACX,EAAE,EAAE;gBAAE2D,EAAE,EAAE,CAAC;gBAAE7D,KAAK,EAAE;cAAiB,CAAE;cAAAQ,QAAA,EAC7D/F,CAAC,CAAC,gBAAgB;YAAC;cAAAuG,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAF,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJnC,aAAa,CAACS,MAAM,KAAK,CAAC,gBAC5B9F,OAAA,CAACvC,GAAG;UAAC+I,EAAE,EAAE;YAAEwD,SAAS,EAAE,QAAQ;YAAEjD,CAAC,EAAE;UAAE,CAAE;UAAAD,QAAA,gBACrC9G,OAAA,CAACL,MAAM;YAAC6G,EAAE,EAAE;cAAEqC,QAAQ,EAAE,EAAE;cAAEvC,KAAK,EAAE,gBAAgB;cAAEe,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChExH,OAAA,CAACtB,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACiD,YAAY;YAAAtD,QAAA,EAClC/F,CAAC,CAAC,oBAAoB;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACbxH,OAAA,CAACtB,UAAU;YAAC4H,KAAK,EAAC,gBAAgB;YAACE,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAC9CvF,UAAU,GAAGR,CAAC,CAAC,uBAAuB,CAAC,GAAGA,CAAC,CAAC,yBAAyB;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACbxH,OAAA,CAACtC,MAAM;YAACyJ,OAAO,EAAC,WAAW;YAACwC,SAAS,eAAE3J,OAAA,CAACZ,GAAG;cAAAkI,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACoC,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAc;YAAAjD,QAAA,EAClG/F,CAAC,CAAC,eAAe;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENxH,OAAA,CAAC1B,cAAc;UAAAwI,QAAA,eACb9G,OAAA,CAAC7B,KAAK;YAAA2I,QAAA,gBACJ9G,OAAA,CAACzB,SAAS;cAAAuI,QAAA,eACR9G,OAAA,CAACxB,QAAQ;gBAACgI,EAAE,EAAE;kBAAEiB,OAAO,EAAE;gBAAU,CAAE;gBAAAX,QAAA,gBACnC9G,OAAA,CAAC3B,SAAS;kBAACmI,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEyB,QAAQ,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAAE/F,CAAC,CAAC,YAAY;gBAAC;kBAAAuG,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnFxH,OAAA,CAAC3B,SAAS;kBAACmI,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEyB,QAAQ,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAAE/F,CAAC,CAAC,aAAa;gBAAC;kBAAAuG,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpFxH,OAAA,CAAC3B,SAAS;kBAACmI,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEyB,QAAQ,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAAE/F,CAAC,CAAC,YAAY;gBAAC;kBAAAuG,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnFxH,OAAA,CAAC3B,SAAS;kBAACmI,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEyB,QAAQ,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAAE/F,CAAC,CAAC,cAAc;gBAAC;kBAAAuG,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrFxH,OAAA,CAAC3B,SAAS;kBAACmI,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEyB,QAAQ,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,EAAE/F,CAAC,CAAC,gBAAgB;gBAAC;kBAAAuG,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAF,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC;cAAAF,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZxH,OAAA,CAAC5B,SAAS;cAAA0I,QAAA,EACPzB,aAAa,CAACX,GAAG,CAAE9C,IAAI;gBAAA,IAAAyI,YAAA;gBAAA,oBACtBrK,OAAA,CAACxB,QAAQ;kBAEP8L,KAAK;kBACL9D,EAAE,EAAE;oBACF,SAAS,EAAE;sBAAEiB,OAAO,EAAEvI,KAAK,CAAC8B,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,IAAI;oBAAE,CAAC;oBAC/D7D,UAAU,EAAE,4BAA4B;oBACxCa,OAAO,EAAE7F,IAAI,CAACsB,QAAQ,GAAG,SAAS,GAAGhE,KAAK,CAAC,SAAS,EAAE,GAAG;kBAC3D,CAAE;kBAAA4H,QAAA,gBAEF9G,OAAA,CAAC3B,SAAS;oBAAAyI,QAAA,eACR9G,OAAA,CAACvC,GAAG;sBAAC+I,EAAE,EAAE;wBAAEQ,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEkC,GAAG,EAAE;sBAAE,CAAE;sBAAArC,QAAA,gBACzD9G,OAAA,CAACpB,MAAM;wBACL8L,GAAG,EAAE9I,IAAI,CAAC+I,UAAU,GAAG,gCAAgC/I,IAAI,CAAC+I,UAAU,EAAE,GAAGC,SAAU;wBACrFpE,EAAE,EAAE;0BACFiB,OAAO,EAAE,SAAS;0BAClBC,KAAK,EAAE,EAAE;0BACTC,MAAM,EAAE,EAAE;0BACVP,UAAU,EAAE,GAAG;0BACfyD,MAAM,EAAE;wBACV,CAAE;wBAAA/D,QAAA,EAEDf,WAAW,CAACnE,IAAI,CAACgE,IAAI,IAAIhE,IAAI,CAACuB,KAAK;sBAAC;wBAAAmE,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,eACTxH,OAAA,CAACvC,GAAG;wBAAAqJ,QAAA,gBACF9G,OAAA,CAACtB,UAAU;0BAACyI,OAAO,EAAC,OAAO;0BAACX,EAAE,EAAE;4BAAEY,UAAU,EAAE;0BAAI,CAAE;0BAAAN,QAAA,EACjDlF,IAAI,CAACgE,IAAI,MAAAyE,YAAA,GAAIzI,IAAI,CAACuB,KAAK,cAAAkH,YAAA,uBAAVA,YAAA,CAAYS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAAA;0BAAAxD,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAC7B,CAAC,eACbxH,OAAA,CAACtB,UAAU;0BAACyI,OAAO,EAAC,OAAO;0BAACb,KAAK,EAAC,gBAAgB;0BAAAQ,QAAA,GAAC,MAC7C,EAAClF,IAAI,CAACgB,EAAE;wBAAA;0BAAA0E,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAF,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAF,QAAA,EAAArK,YAAA;sBAAAsK,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAF,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZxH,OAAA,CAAC3B,SAAS;oBAAAyI,QAAA,eACR9G,OAAA,CAACtB,UAAU;sBAACyI,OAAO,EAAC,OAAO;sBAAAL,QAAA,EAAElF,IAAI,CAACuB;oBAAK;sBAAAmE,QAAA,EAAArK,YAAA;sBAAAsK,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAF,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACZxH,OAAA,CAAC3B,SAAS;oBAAAyI,QAAA,eACR9G,OAAA,CAACrB,IAAI;sBACHoM,KAAK,EAAEhK,CAAC,CAAC,QAAQ,CAACa,IAAI,CAAC+D,IAAI,IAAI,MAAM,EAAEF,WAAW,CAAC,CAAC,EAAE,CAAE;sBACxDa,KAAK,EAAE1E,IAAI,CAAC+D,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG/D,IAAI,CAAC+D,IAAI,KAAK,YAAY,GAAG,MAAM,GAAG,SAAU;sBAC3FsE,IAAI,EAAC,OAAO;sBACZzD,EAAE,EAAE;wBAAEE,YAAY,EAAE,CAAC;wBAAEU,UAAU,EAAE,GAAG;wBAAE4D,aAAa,EAAE;sBAAa;oBAAE;sBAAA1D,QAAA,EAAArK,YAAA;sBAAAsK,UAAA;sBAAAC,YAAA;oBAAA,OACvE;kBAAC;oBAAAF,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZxH,OAAA,CAAC3B,SAAS;oBAAAyI,QAAA,eACR9G,OAAA,CAACrB,IAAI;sBACHoM,KAAK,EAAEnJ,IAAI,CAACsB,QAAQ,GAAGnC,CAAC,CAAC,cAAc,CAAC,GAAGA,CAAC,CAAC,gBAAgB,CAAE;sBAC/DuF,KAAK,EAAE1E,IAAI,CAACsB,QAAQ,GAAG,SAAS,GAAG,OAAQ;sBAC3C+G,IAAI,EAAC,OAAO;sBACZzD,EAAE,EAAE;wBAAEE,YAAY,EAAE,CAAC;wBAAEU,UAAU,EAAE;sBAAI;oBAAE;sBAAAE,QAAA,EAAArK,YAAA;sBAAAsK,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAF,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZxH,OAAA,CAAC3B,SAAS;oBAAAyI,QAAA,eACR9G,OAAA,CAACvC,GAAG;sBAAC+I,EAAE,EAAE;wBAAEQ,OAAO,EAAE,MAAM;wBAAEmC,GAAG,EAAE;sBAAI,CAAE;sBAAArC,QAAA,gBACrC9G,OAAA,CAAC/B,UAAU;wBACTgM,IAAI,EAAC,OAAO;wBACZzD,EAAE,EAAE;0BAAEF,KAAK,EAAE,WAAW;0BAAE,SAAS,EAAE;4BAAEmB,OAAO,EAAEvI,KAAK,CAAC,SAAS,EAAE,GAAG;0BAAE;wBAAE,CAAE;wBAC1E0K,OAAO,EAAEA,CAAA,KAAM;0BACbqB,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACxJ,IAAI,CAAC,CAAC;0BAC3DiI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgBnI,IAAI,CAACgB,EAAE,EAAE;wBAClD,CAAE;wBAAAkE,QAAA,eAEF9G,OAAA,CAACX,UAAU;0BAAAiI,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAF,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACbxH,OAAA,CAAC/B,UAAU;wBACTgM,IAAI,EAAC,OAAO;wBACZzD,EAAE,EAAE;0BAAEF,KAAK,EAAE,cAAc;0BAAE,SAAS,EAAE;4BAAEmB,OAAO,EAAEvI,KAAK,CAAC,SAAS,EAAE,GAAG;0BAAE;wBAAE,CAAE;wBAC7E0K,OAAO,EAAEA,CAAA,KAAM;0BACbqB,cAAc,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACxJ,IAAI,CAAC,CAAC;0BAC3DiI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgBnI,IAAI,CAACgB,EAAE,EAAE;wBAClD,CAAE;wBAAAkE,QAAA,eAEF9G,OAAA,CAACV,IAAI;0BAAAgI,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAF,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACbxH,OAAA,CAAC/B,UAAU;wBACTgM,IAAI,EAAC,OAAO;wBACZoB,QAAQ,EAAEhK,aAAc;wBACxBmF,EAAE,EAAE;0BACFF,KAAK,EAAE1E,IAAI,CAACsB,QAAQ,GAAG,cAAc,GAAG,cAAc;0BACtD,SAAS,EAAE;4BACTuE,OAAO,EAAEvI,KAAK,CAAC0C,IAAI,CAACsB,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG;0BAC3D;wBACF,CAAE;wBACF0G,OAAO,EAAEA,CAAA,KAAM9H,eAAe,CAAC;0BAAEH,IAAI,EAAE,IAAI;0BAAEC;wBAAK,CAAC,CAAE;wBAAAkF,QAAA,EAEpDzF,aAAa,gBAAGrB,OAAA,CAACnB,gBAAgB;0BAACoL,IAAI,EAAE;wBAAG;0BAAA3C,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG5F,IAAI,CAACsB,QAAQ,gBAAGlD,OAAA,CAACR,KAAK;0BAAA8H,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACP,QAAQ;0BAAA6H,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAF,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,eACbxH,OAAA,CAAC/B,UAAU;wBACTgM,IAAI,EAAC,OAAO;wBACZoB,QAAQ,EAAEhK,aAAc;wBACxBmF,EAAE,EAAE;0BAAEF,KAAK,EAAE,YAAY;0BAAE,SAAS,EAAE;4BAAEmB,OAAO,EAAEvI,KAAK,CAAC,SAAS,EAAE,GAAG;0BAAE;wBAAE,CAAE;wBAC3E0K,OAAO,EAAEA,CAAA,KAAMlI,eAAe,CAAC;0BAAEC,IAAI,EAAE,IAAI;0BAAEC;wBAAK,CAAC,CAAE;wBAAAkF,QAAA,eAErD9G,OAAA,CAACT,MAAM;0BAAA+H,QAAA,EAAArK,YAAA;0BAAAsK,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAF,QAAA,EAAArK,YAAA;wBAAAsK,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAF,QAAA,EAAArK,YAAA;sBAAAsK,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAF,QAAA,EAAArK,YAAA;oBAAAsK,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GA/FP5F,IAAI,CAACgB,EAAE;kBAAA0E,QAAA,EAAArK,YAAA;kBAAAsK,UAAA;kBAAAC,YAAA;gBAAA,OAgGJ,CAAC;cAAA,CACZ;YAAC;cAAAF,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAF,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGRxH,OAAA,CAACpC,MAAM;QACL+D,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBqG,OAAO,EAAEA,CAAA,KAAM,CAAC3G,aAAa,IAAIK,eAAe,CAAC;UAAEC,IAAI,EAAE,KAAK;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAE;QAC9E0J,UAAU,EAAE;UAAE9E,EAAE,EAAE;YAAEE,YAAY,EAAE,CAAC;YAAE6E,QAAQ,EAAE;UAAI;QAAE,CAAE;QAAAzE,QAAA,gBAEvD9G,OAAA,CAACjC,WAAW;UAACyI,EAAE,EAAE;YAAEgF,EAAE,EAAE;UAAE,CAAE;UAAA1E,QAAA,eACzB9G,OAAA,CAACvC,GAAG;YAAC+I,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEkC,GAAG,EAAE;YAAE,CAAE;YAAArC,QAAA,gBACzD9G,OAAA,CAACpB,MAAM;cAAC4H,EAAE,EAAE;gBAAEiB,OAAO,EAAEvI,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;gBAAEoH,KAAK,EAAE;cAAa,CAAE;cAAAQ,QAAA,eAClE9G,OAAA,CAACT,MAAM;gBAAA+H,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAF,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACTxH,OAAA,CAACtB,UAAU;cAACyI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAAN,QAAA,EACtC/F,CAAC,CAAC,qBAAqB;YAAC;cAAAuG,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAF,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACdxH,OAAA,CAAClC,aAAa;UAAAgJ,QAAA,eACZ9G,OAAA,CAACtB,UAAU;YAAAoI,QAAA,GACR/F,CAAC,CAAC,4BAA4B,EAAE;cAAE6E,IAAI,EAAE,EAAA1F,kBAAA,GAAAuB,YAAY,CAACG,IAAI,cAAA1B,kBAAA,uBAAjBA,kBAAA,CAAmB0F,IAAI,KAAI,EAAE;cAAEzC,KAAK,GAAAhD,mBAAA,GAAEsB,YAAY,CAACG,IAAI,cAAAzB,mBAAA,uBAAjBA,mBAAA,CAAmBgD;YAAM,CAAC,CAAC,eAC1GnD,OAAA;cAAAsH,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxH,OAAA;cAAAsH,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLzG,CAAC,CAAC,0BAA0B,CAAC;UAAA;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAChBxH,OAAA,CAACnC,aAAa;UAAC2I,EAAE,EAAE;YAAEO,CAAC,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC1B9G,OAAA,CAACtC,MAAM;YACLkM,OAAO,EAAEA,CAAA,KAAMlI,eAAe,CAAC;cAAEC,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAE;YAC5D4E,EAAE,EAAE;cAAEE,YAAY,EAAE;YAAE,CAAE;YACxB2E,QAAQ,EAAEhK,aAAc;YAAAyF,QAAA,EAEvB/F,CAAC,CAAC,eAAe;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACTxH,OAAA,CAACtC,MAAM;YACLkM,OAAO,EAAEjH,YAAa;YACtB2D,KAAK,EAAC,OAAO;YACba,OAAO,EAAC,WAAW;YACnBX,EAAE,EAAE;cAAEE,YAAY,EAAE,CAAC;cAAE6E,QAAQ,EAAE;YAAI,CAAE;YACvCF,QAAQ,EAAEhK,aAAc;YAAAyF,QAAA,EAEvBzF,aAAa,gBAAGrB,OAAA,CAACnB,gBAAgB;cAACoL,IAAI,EAAE,EAAG;cAAC3D,KAAK,EAAC;YAAS;cAAAgB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGzG,CAAC,CAAC,oBAAoB;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTxH,OAAA,CAACpC,MAAM;QACL+D,IAAI,EAAEE,YAAY,CAACF,IAAK;QACxBqG,OAAO,EAAEA,CAAA,KAAM,CAAC3G,aAAa,IAAIS,eAAe,CAAC;UAAEH,IAAI,EAAE,KAAK;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAE;QAC9E0J,UAAU,EAAE;UAAE9E,EAAE,EAAE;YAAEE,YAAY,EAAE,CAAC;YAAE6E,QAAQ,EAAE;UAAI;QAAE,CAAE;QAAAzE,QAAA,gBAEvD9G,OAAA,CAACjC,WAAW;UAACyI,EAAE,EAAE;YAAEgF,EAAE,EAAE;UAAE,CAAE;UAAA1E,QAAA,eACzB9G,OAAA,CAACvC,GAAG;YAAC+I,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEkC,GAAG,EAAE;YAAE,CAAE;YAAArC,QAAA,gBACzD9G,OAAA,CAACpB,MAAM;cACL4H,EAAE,EAAE;gBACFiB,OAAO,EAAEvI,KAAK,CAAC,CAAAkB,kBAAA,GAAAyB,YAAY,CAACD,IAAI,cAAAxB,kBAAA,eAAjBA,kBAAA,CAAmB8C,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG,CAAC;gBACxEoD,KAAK,EAAE,CAAAjG,mBAAA,GAAAwB,YAAY,CAACD,IAAI,cAAAvB,mBAAA,eAAjBA,mBAAA,CAAmB6C,QAAQ,GAAG,cAAc,GAAG;cACxD,CAAE;cAAA4D,QAAA,EAED,CAAAxG,mBAAA,GAAAuB,YAAY,CAACD,IAAI,cAAAtB,mBAAA,eAAjBA,mBAAA,CAAmB4C,QAAQ,gBAAGlD,OAAA,CAACR,KAAK;gBAAA8H,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACP,QAAQ;gBAAA6H,QAAA,EAAArK,YAAA;gBAAAsK,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAF,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACTxH,OAAA,CAACtB,UAAU;cAACyI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAAN,QAAA,EACtC/F,CAAC,CAAC,CAAAR,mBAAA,GAAAsB,YAAY,CAACD,IAAI,cAAArB,mBAAA,eAAjBA,mBAAA,CAAmB2C,QAAQ,GAAG,gCAAgC,GAAG,kCAAkC;YAAC;cAAAoE,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAF,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACdxH,OAAA,CAAClC,aAAa;UAAAgJ,QAAA,eACZ9G,OAAA,CAACtB,UAAU;YAAAoI,QAAA,GACR/F,CAAC,CAAC,CAAAP,mBAAA,GAAAqB,YAAY,CAACD,IAAI,cAAApB,mBAAA,eAAjBA,mBAAA,CAAmB0C,QAAQ,GAAG,kCAAkC,GAAG,oCAAoC,EAAE;cAC1G0C,IAAI,EAAE,EAAAnF,mBAAA,GAAAoB,YAAY,CAACD,IAAI,cAAAnB,mBAAA,uBAAjBA,mBAAA,CAAmBmF,IAAI,KAAI,EAAE;cACnCzC,KAAK,EAAE,EAAAzC,mBAAA,GAAAmB,YAAY,CAACD,IAAI,cAAAlB,mBAAA,uBAAjBA,mBAAA,CAAmByC,KAAK,KAAI;YACrC,CAAC,CAAC,eACFnD,OAAA;cAAAsH,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxH,OAAA;cAAAsH,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxH,OAAA;cAAA8G,QAAA,EAAQ;YAAG;cAAAQ,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAA7G,mBAAA,GAACkB,YAAY,CAACD,IAAI,cAAAjB,mBAAA,uBAAjBA,mBAAA,CAAmBiC,EAAE,eAC3C5C,OAAA;cAAAsH,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNxH,OAAA;cAAA8G,QAAA,GAAS/F,CAAC,CAAC,qBAAqB,CAAC,EAAC,GAAC;YAAA;cAAAuG,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzG,CAAC,CAAC,CAAAH,mBAAA,GAAAiB,YAAY,CAACD,IAAI,cAAAhB,mBAAA,eAAjBA,mBAAA,CAAmBsC,QAAQ,GAAG,oBAAoB,GAAG,sBAAsB,CAAC;UAAA;YAAAoE,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OAClH;QAAC;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAChBxH,OAAA,CAACnC,aAAa;UAAC2I,EAAE,EAAE;YAAEO,CAAC,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC1B9G,OAAA,CAACtC,MAAM;YACLkM,OAAO,EAAEA,CAAA,KAAM9H,eAAe,CAAC;cAAEH,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE;YAAK,CAAC,CAAE;YAC5D4E,EAAE,EAAE;cAAEE,YAAY,EAAE;YAAE,CAAE;YACxB2E,QAAQ,EAAEhK,aAAc;YAAAyF,QAAA,EAEvB/F,CAAC,CAAC,eAAe;UAAC;YAAAuG,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACTxH,OAAA,CAACtC,MAAM;YACLkM,OAAO,EAAE5G,kBAAmB;YAC5BsD,KAAK,EAAE,CAAAzF,oBAAA,GAAAgB,YAAY,CAACD,IAAI,cAAAf,oBAAA,eAAjBA,oBAAA,CAAmBqC,QAAQ,GAAG,SAAS,GAAG,SAAU;YAC3DiE,OAAO,EAAC,WAAW;YACnBX,EAAE,EAAE;cAAEE,YAAY,EAAE,CAAC;cAAE6E,QAAQ,EAAE;YAAI,CAAE;YACvCF,QAAQ,EAAEhK,aAAc;YAAAyF,QAAA,EAEvBzF,aAAa,gBACZrB,OAAA,CAACnB,gBAAgB;cAACoL,IAAI,EAAE,EAAG;cAAC3D,KAAK,EAAC;YAAS;cAAAgB,QAAA,EAAArK,YAAA;cAAAsK,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC5CzG,CAAC,CAAC,CAAAD,oBAAA,GAAAe,YAAY,CAACD,IAAI,cAAAd,oBAAA,eAAjBA,oBAAA,CAAmBoC,QAAQ,GAAG,sBAAsB,GAAG,oBAAoB;UAAC;YAAAoE,QAAA,EAAArK,YAAA;YAAAsK,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC;QAAA;UAAAF,QAAA,EAAArK,YAAA;UAAAsK,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAF,QAAA,EAAArK,YAAA;QAAAsK,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAF,QAAA,EAAArK,YAAA;MAAAsK,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAF,QAAA,EAAArK,YAAA;IAAAsK,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAACtK,EAAA,CAhlBuB+C,QAAQ;EAAA,QAChB1C,cAAc,EACd4B,QAAQ;AAAA;AAAAsM,EAAA,GAFAxL,QAAQ;AAAA,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}