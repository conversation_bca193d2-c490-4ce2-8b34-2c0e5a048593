"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Session2Service = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const common_2 = require("@nestjs/common");
let Session2Service = class Session2Service {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data, file) {
        console.log('🔍 Session2 create - Received data:', data);
        console.log('🔍 Session2 create - File:', file?.filename);
        const { name, programId, startDate, endDate } = data;
        console.log('🔍 Session2 create - Extracted fields:', { name, programId, startDate, endDate });
        try {
            const programStructure = await this.prisma.buildProgram.findFirst({
                where: { programId: Number(programId) },
                include: {
                    modules: {
                        include: {
                            module: true,
                            courses: {
                                include: {
                                    course: true,
                                    contenus: {
                                        include: { contenu: true },
                                    },
                                },
                            },
                        },
                    },
                },
            });
            console.log('🔍 Session2 create - Program structure found:', !!programStructure);
            if (programStructure) {
                console.log('🔍 Session2 create - Modules count:', programStructure.modules?.length || 0);
            }
            if (!programStructure) {
                console.error('❌ Session2 create - No program structure found for programId:', programId);
                throw new Error("Structure du programme introuvable.");
            }
            console.log('🔍 Session2 create - Creating session2 record...');
            const session2 = await this.prisma.session2.create({
                data: {
                    name,
                    programId: Number(programId),
                    startDate: startDate ? new Date(startDate) : undefined,
                    endDate: endDate ? new Date(endDate) : undefined,
                    imageUrl: file
                        ? `http://localhost:8000/uploads/sessions/${file.filename}`
                        : undefined,
                },
            });
            console.log('🔍 Session2 create - Session2 created with ID:', session2.id);
            console.log('🔍 Session2 create - Creating modules and courses...');
            for (const mod of programStructure.modules) {
                console.log('🔍 Session2 create - Processing module:', mod.moduleId);
                const s2mod = await this.prisma.session2Module.create({
                    data: {
                        session2Id: session2.id,
                        moduleId: mod.moduleId,
                    },
                });
                for (const course of mod.courses) {
                    console.log('🔍 Session2 create - Processing course:', course.courseId);
                    const s2course = await this.prisma.session2Course.create({
                        data: {
                            session2ModuleId: s2mod.id,
                            courseId: course.courseId,
                        },
                    });
                    for (const ct of course.contenus) {
                        console.log('🔍 Session2 create - Processing contenu:', ct.contenuId);
                        await this.prisma.session2Contenu.create({
                            data: {
                                session2CourseId: s2course.id,
                                contenuId: ct.contenuId,
                            },
                        });
                    }
                }
            }
            console.log('✅ Session2 create - Successfully created session with all structure');
            return { message: "✅ Session2 créée avec structure copiée avec succès" };
        }
        catch (error) {
            console.error('❌ Session2 create - Error:', error.message);
            console.error('❌ Session2 create - Stack:', error.stack);
            throw error;
        }
    }
    async remove(id) {
        return this.prisma.session2.delete({
            where: { id },
        });
    }
    async findAll() {
        const sessions = await this.prisma.session2.findMany({
            include: {
                program: true,
                session2Modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: {
                                        contenu: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        const sessionsWithFeedbackAverage = await Promise.all(sessions.map(async (session) => {
            const feedbacks = await this.prisma.sessionFeedback.findMany({
                where: { sessionId: session.id },
                select: { rating: true },
            });
            console.log(`Session ID: ${session.id}, Feedback ratings:`, feedbacks.map(f => f.rating));
            let averageRating = null;
            if (feedbacks.length > 0) {
                const totalRating = feedbacks.reduce((sum, feedback) => sum + feedback.rating, 0);
                averageRating = parseFloat((totalRating / feedbacks.length).toFixed(2));
                console.log(`Computed average rating for session ${session.id}:`, averageRating);
            }
            return {
                ...session,
                averageFeedbackRating: averageRating,
            };
        }));
        return sessionsWithFeedbackAverage;
    }
    async addUserToSession(session2Id, email) {
        const user = await this.prisma.user.findUnique({ where: { email } });
        if (!user)
            throw new common_2.NotFoundException("Utilisateur introuvable");
        const exists = await this.prisma.userSession2.findUnique({
            where: { userId_session2Id: { userId: user.id, session2Id } },
        });
        if (exists)
            throw new common_2.BadRequestException("Utilisateur déjà dans la session");
        await this.prisma.userSession2.create({
            data: { userId: user.id, session2Id },
        });
        return { message: "Utilisateur ajouté à la session !" };
    }
    async getUsersForSession(session2Id) {
        const assigned = await this.prisma.userSession2.findMany({
            where: { session2Id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        profilePic: true,
                        role: true,
                    },
                },
            },
        });
        return assigned.map(item => item.user);
    }
    async removeUserFromSession(session2Id, userId) {
        return this.prisma.userSession2.deleteMany({
            where: {
                session2Id,
                userId,
            },
        });
    }
    async updateStatus(id, status) {
        if (!['ACTIVE', 'INACTIVE', 'COMPLETED', 'ARCHIVED'].includes(status)) {
            throw new common_2.BadRequestException('Invalid status');
        }
        return this.prisma.session2.update({
            where: { id },
            data: { status: { set: status } },
        });
    }
};
exports.Session2Service = Session2Service;
exports.Session2Service = Session2Service = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], Session2Service);
//# sourceMappingURL=session2.service.js.map