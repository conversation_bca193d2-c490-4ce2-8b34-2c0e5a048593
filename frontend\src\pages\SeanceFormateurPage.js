import React, { useState, useEffect, useCallback } from "react";
import { Box, Container, Typo<PERSON>, Button } from "@mui/material";
import { useTranslation } from 'react-i18next';
import axios from "axios";
import { useParams } from "react-router-dom";

import AddSeanceFormateurView from "./users/views/AddSeanceFormateurView";
import SeanceFormateurList from "./users/views/SeanceFormateurList";
import AnimerSeanceView from "./users/views/AnimerSeanceView";

const SeanceFormateurPage = () => {
  const { t } = useTranslation();
  const { sessionId } = useParams(); // 🔸 session2Id from URL

  const [seances, setSeances] = useState([]);
  const [selectedSeance, setSelectedSeance] = useState(null);
  const [sessionDetails, setSessionDetails] = useState(null);

  const fetchSeances = useCallback(async () => {
    try {
      const res = await axios.get(`http://localhost:8000/seance-formateur/session/${sessionId}`);
      setSeances(res.data);
    } catch (err) {
      console.error("❌ Erreur chargement séances", err);
    }
  }, [sessionId]);

  const fetchSessionDetails = useCallback(async () => {
    console.log("🔍 Session ID:", sessionId);
    try {
      const res = await axios.get(`http://localhost:8000/seance-formateur/details/${sessionId}`);
      console.log("✅ Session details:", res.data);
      setSessionDetails(res.data);
    } catch (error) {
      console.error("❌ Erreur chargement session:", error);
    }
  }, [sessionId]);

  useEffect(() => {
    fetchSeances();
    fetchSessionDetails();
  }, [fetchSeances, fetchSessionDetails]);

  const handleAnimer = (seance) => setSelectedSeance(seance);
  const handleRetour = () => setSelectedSeance(null);

  const handleDelete = async (id) => {
    if (window.confirm("Confirmer la suppression de cette séance ?")) {
      await axios.delete(`http://localhost:8000/seance-formateur/${id}`);
      fetchSeances();
    }
  };

  return (
    <Container>
      <Box mt={4}>
        {/* ✅ Affichage du nom de la session */}
        {sessionDetails?.name ? (
          <>
            <Typography variant="h4" gutterBottom>
              🎓 {sessionDetails.name} 🎓
            </Typography>
          {sessionDetails.averageRating !== undefined && sessionDetails.averageRating !== null && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              ⭐ Note moyenne selon les seances: {sessionDetails.averageRating.toFixed(1)} / 5
            </Typography>
          )}
            
          </>
        ) : (
          <Typography variant="h6" color="text.secondary">
            Chargement de la session...
          </Typography>
        )}

        {selectedSeance ? (
          <>
            <Box display="flex" justifyContent="flex-end" mb={2}>
              <Button onClick={handleRetour} variant="outlined">
                ⬅️ {t('common.back')}
              </Button>
            </Box>
            <AnimerSeanceView seance={selectedSeance} />
          </>
        ) : (
          <>
            <AddSeanceFormateurView onSeanceCreated={fetchSeances} />
            <Box mt={4}>
              <SeanceFormateurList
                seances={seances}
                onAnimer={handleAnimer}
                onDelete={handleDelete}
              />
            </Box>
          </>
        )}
      </Box>
    </Container>
  );
};

export default SeanceFormateurPage;
