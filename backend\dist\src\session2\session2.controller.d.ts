import { Session2Service } from './session2.service';
import { PrismaService } from 'nestjs-prisma';
export declare class Session2Controller {
    private readonly service;
    private readonly prisma;
    constructor(service: Session2Service, prisma: PrismaService);
    create(file: Express.Multer.File, body: any): Promise<{
        message: string;
    }>;
    findAll(): Promise<({
        program: {
            id: number;
            name: string;
            createdAt: Date;
            published: boolean;
        };
        session2Modules: ({
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
            courses: ({
                course: {
                    id: number;
                    title: string;
                };
                contenus: ({
                    contenu: {
                        id: number;
                        published: boolean;
                        title: string;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                        type: import(".prisma/client").$Enums.ContenuType;
                        coursAssocie: string | null;
                    };
                } & {
                    id: number;
                    session2CourseId: number;
                    contenuId: number;
                })[];
            } & {
                id: number;
                session2ModuleId: number;
                courseId: number;
            })[];
        } & {
            id: number;
            session2Id: number;
            moduleId: number;
        })[];
    } & {
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: import(".prisma/client").$Enums.Session2Status;
    })[]>;
    findAllSimple(): import(".prisma/client").Prisma.PrismaPromise<{
        id: number;
        name: string;
        createdAt: Date;
    }[]>;
    remove(id: string): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: import(".prisma/client").$Enums.Session2Status;
    }>;
    addUserToSession(session2Id: string, email: string): Promise<{
        message: string;
    }>;
    getSessionUsers(session2Id: string): Promise<{
        id: number;
        name: string | null;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        profilePic: string | null;
    }[]>;
    removeUserFromSession(session2Id: string, userId: string): Promise<import(".prisma/client").Prisma.BatchPayload>;
    updateStatus(id: string, status: string): Promise<{
        id: number;
        name: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
        createdAt: Date;
        status: import(".prisma/client").$Enums.Session2Status;
    }>;
}
