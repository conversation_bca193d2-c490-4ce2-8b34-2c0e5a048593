{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\ProfilePage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Container, Paper, Typography, CircularProgress, Alert, Grid, Box, Button, Avatar, Divider, Chip, Stack } from \"@mui/material\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport EditIcon from '@mui/icons-material/Edit';\nimport EmailIcon from '@mui/icons-material/Email';\nimport PhoneIcon from '@mui/icons-material/Phone';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport WorkIcon from '@mui/icons-material/Work';\nimport PersonIcon from '@mui/icons-material/Person';\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePage = () => {\n  _s();\n  var _user$name, _user$email;\n  const {\n    t\n  } = useTranslation();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [sessions, setSessions] = useState([]);\n  const [sessionsLoading, setSessionsLoading] = useState(true);\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    setSessionsLoading(true);\n    axios.get(`http://localhost:8000/users/${user.id}/sessions2`).then(res => setSessions(res.data)).catch(() => setSessions([])).finally(() => setSessionsLoading(false));\n  }, [user === null || user === void 0 ? void 0 : user.id, t]);\n  useEffect(() => {\n    const fetchUser = async () => {\n      console.log(\"ProfilePage: Début de fetchUser avec ID:\", id);\n\n      // Commencer par récupérer les données utilisateur depuis le localStorage\n      const storedUser = localStorage.getItem(\"user\");\n      let localStorageUserData = null;\n      if (storedUser) {\n        try {\n          localStorageUserData = JSON.parse(storedUser);\n          console.log(\"ProfilePage: Utilisateur récupéré depuis localStorage:\", localStorageUserData);\n        } catch (err) {\n          console.error(\"ProfilePage: Erreur lors de la récupération de l'utilisateur depuis localStorage:\", err);\n        }\n      }\n\n      // Si aucun ID n'est fourni dans l'URL\n      if (!id) {\n        console.log(\"ProfilePage: ID utilisateur manquant dans l'URL\");\n\n        // Utiliser les données du localStorage si disponibles\n        if (localStorageUserData) {\n          // Si l'utilisateur a un ID dans le localStorage, rediriger vers la page avec ID\n          if (localStorageUserData.id) {\n            const userId = typeof localStorageUserData.id === 'string' ? parseInt(localStorageUserData.id, 10) : localStorageUserData.id;\n            if (!isNaN(userId)) {\n              console.log(\"ProfilePage: Redirection vers la page de profil avec ID:\", userId);\n              navigate(`/ProfilePage/${userId}`, {\n                replace: true\n              });\n              return;\n            }\n          }\n\n          // Si pas d'ID valide mais des données utilisateur, les utiliser directement\n          setUser(localStorageUserData);\n          setLoading(false);\n          return;\n        }\n        setError(t('profile.missingUserData'));\n        setLoading(false);\n        return;\n      }\n      console.log(\"ProfilePage: Tentative de chargement du profil pour l'ID:\", id);\n      setLoading(true);\n\n      // Convertir l'ID en nombre\n      const userId = typeof id === 'string' ? parseInt(id, 10) : id;\n      if (isNaN(userId)) {\n        console.error(\"ProfilePage: ID invalide:\", id);\n        setError(t('profile.invalidUserId'));\n        setLoading(false);\n        return;\n      }\n\n      // Si l'ID dans l'URL correspond à l'ID dans le localStorage, utiliser les données du localStorage\n      if (localStorageUserData && localStorageUserData.id === userId) {\n        console.log(\"ProfilePage: Utilisation des données du localStorage car l'ID correspond\");\n        setUser(localStorageUserData);\n        setLoading(false);\n\n        // Mettre à jour les données en arrière-plan\n        try {\n          const res = await axios.get(`http://localhost:8000/users/id/${userId}`);\n          if (res.data) {\n            console.log(\"ProfilePage: Mise à jour des données utilisateur en arrière-plan:\", res.data);\n\n            // Mettre à jour le rôle pour khalil si nécessaire\n            if (res.data.email === \"<EMAIL>\" && res.data.role !== \"Admin\") {\n              res.data.role = \"Admin\";\n            }\n            setUser(res.data);\n            localStorage.setItem(\"user\", JSON.stringify(res.data));\n          }\n        } catch (err) {\n          console.error(\"ProfilePage: Erreur lors de la mise à jour des données en arrière-plan:\", err);\n        }\n        return;\n      }\n\n      // Stratégie 1: Essayer directement par ID\n      try {\n        console.log(\"ProfilePage: Stratégie 1: Chargement par ID\", userId);\n        const res = await axios.get(`http://localhost:8000/users/id/${userId}`);\n        if (res.data) {\n          console.log(\"ProfilePage: Succès - Données utilisateur chargées par ID:\", res.data);\n\n          // Mettre à jour le rôle pour khalil si nécessaire\n          if (res.data.email === \"<EMAIL>\" && res.data.role !== \"Admin\") {\n            res.data.role = \"Admin\";\n          }\n          setUser(res.data);\n          setLoading(false);\n          return;\n        }\n      } catch (err) {\n        var _err$response;\n        console.error(\"ProfilePage: Échec de la stratégie 1:\", ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      }\n\n      // Stratégie 2: Utiliser les données du localStorage comme solution de secours\n      if (localStorageUserData) {\n        console.log(\"ProfilePage: Stratégie 2: Utilisation des données du localStorage comme solution de secours\");\n        setUser(localStorageUserData);\n        setLoading(false);\n        return;\n      }\n\n      // Si tout échoue\n      setError(t('profile.loadUserError'));\n      setLoading(false);\n    };\n    fetchUser();\n  }, [id, navigate, t]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '80vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        thickness: 4\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"sm\",\n      sx: {\n        mt: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 4,\n          borderRadius: 4,\n          textAlign: 'center',\n          background: 'linear-gradient(45deg, #f8f9fa 30%, #e9ecef 90%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 600\n          },\n          children: t('profile.profileError')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3,\n            borderRadius: 2\n          },\n          children: error || t('profile.userNotFound')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          justifyContent: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => window.location.reload(),\n            sx: {\n              px: 4,\n              py: 1\n            },\n            children: t('common.tryAgain')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => navigate('/'),\n            sx: {\n              px: 4,\n              py: 1\n            },\n            children: t('common.goHome')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    sx: {\n      py: 6\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 4,\n      sx: {\n        p: 6,\n        borderRadius: 6,\n        background: 'linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          sx: {\n            fontWeight: 700,\n            background: 'linear-gradient(45deg, #1976d2 30%, #2196f3 90%)',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent'\n          },\n          children: t('profile.userProfile')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            // Stocker temporairement l'utilisateur à éditer dans sessionStorage\n            console.log(\"Storing user data for editing:\", user);\n            sessionStorage.setItem(\"editingUser\", JSON.stringify(user));\n            navigate(`/EditProfile/${user.email}`);\n          },\n          sx: {\n            borderRadius: 20,\n            px: 3,\n            py: 1,\n            textTransform: 'none',\n            boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n            '&:hover': {\n              boxShadow: '0 6px 8px rgba(0,0,0,0.15)'\n            }\n          },\n          children: t('profile.editProfile')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: {\n            xs: 'column',\n            md: 'row'\n          },\n          alignItems: 'center',\n          mb: 6,\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: user.profilePic ? user.profilePic.startsWith('/profile-pics/') ? `http://localhost:8000/uploads${user.profilePic}` : user.profilePic.startsWith('http') ? user.profilePic : `http://localhost:8000/uploads/profile-pics/${user.profilePic.split('/').pop()}` : undefined,\n          sx: {\n            width: 150,\n            height: 150,\n            fontSize: 60,\n            border: '4px solid #1976d2',\n            boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)'\n          },\n          children: ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.charAt(0).toUpperCase()) || \"U\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: {\n              xs: 'center',\n              md: 'left'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            sx: {\n              fontWeight: 600,\n              mb: 1\n            },\n            children: user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            icon: /*#__PURE__*/_jsxDEV(WorkIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 21\n            }, this),\n            label: user.role ? t(`role.${user.role.toLowerCase()}`) : t('role.etudiant'),\n            color: \"primary\",\n            variant: \"outlined\",\n            sx: {\n              borderRadius: 2,\n              px: 1,\n              fontSize: '0.9rem',\n              fontWeight: 500\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              mt: 2,\n              color: 'text.secondary',\n              maxWidth: 500\n            },\n            children: user.about || t('profile.noBio')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 4,\n          borderColor: 'rgba(0,0,0,0.1)',\n          borderWidth: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              mb: 3,\n              fontWeight: 600,\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), \" \", t('profile.personalInfo')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: user.phone || t('profile.notProvided')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(LocationOnIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: user.location || t('profile.locationNotSpecified')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              mb: 3,\n              fontWeight: 600\n            },\n            children: t('profile.skillsExpertise')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), user.skills && Array.isArray(user.skills) && user.skills.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1.5\n            },\n            children: user.skills.map((skill, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: skill,\n              color: \"primary\",\n              sx: {\n                borderRadius: 2,\n                px: 1.5,\n                py: 1,\n                fontSize: '0.9rem',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(25, 118, 210, 0.2)'\n                }\n              }\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: t('profile.noSkillsAdded')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 4\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 600,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(WorkIcon, {\n            color: \"primary\",\n            sx: {\n              mr: 1,\n              mb: '-5px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 5\n          }, this), \"Sessions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 3\n        }, this), sessionsLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 5\n        }, this) : sessions.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: \"This user is not assigned to any session.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 5\n        }, this) : /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: sessions.map(us => {\n            var _us$session2$program;\n            return /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 1,\n              sx: {\n                p: 2,\n                borderRadius: 2,\n                background: \"#f5faff\",\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                flexWrap: \"wrap\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: us.session2.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 13\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Programme: \", ((_us$session2$program = us.session2.program) === null || _us$session2$program === void 0 ? void 0 : _us$session2$program.name) || \"Non spécifié\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 13\n                }, this), us.session2.startDate && us.session2.endDate && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Du \", new Date(us.session2.startDate).toLocaleDateString(), \" au \", new Date(us.session2.endDate).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 11\n              }, this)\n            }, us.session2.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 9\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePage, \"1P4bpNLN3hK9H6XTha8kanFaKHA=\", false, function () {\n  return [useTranslation, useParams, useNavigate];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Container", "Paper", "Typography", "CircularProgress", "<PERSON><PERSON>", "Grid", "Box", "<PERSON><PERSON>", "Avatar", "Divider", "Chip", "<PERSON><PERSON>", "useParams", "useNavigate", "EditIcon", "EmailIcon", "PhoneIcon", "LocationOnIcon", "WorkIcon", "PersonIcon", "useTranslation", "axios", "jsxDEV", "_jsxDEV", "ProfilePage", "_s", "_user$name", "_user$email", "t", "id", "navigate", "user", "setUser", "loading", "setLoading", "error", "setError", "sessions", "setSessions", "sessionsLoading", "setSessionsLoading", "get", "then", "res", "data", "catch", "finally", "fetchUser", "console", "log", "storedUser", "localStorage", "getItem", "localStorageUserData", "JSON", "parse", "err", "userId", "parseInt", "isNaN", "replace", "email", "role", "setItem", "stringify", "_err$response", "response", "message", "sx", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "thickness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "mt", "elevation", "p", "borderRadius", "textAlign", "background", "variant", "gutterBottom", "fontWeight", "severity", "mb", "direction", "spacing", "onClick", "window", "location", "reload", "px", "py", "WebkitBackgroundClip", "WebkitTextFillColor", "startIcon", "sessionStorage", "textTransform", "boxShadow", "flexDirection", "xs", "md", "gap", "src", "profilePic", "startsWith", "split", "pop", "undefined", "width", "height", "fontSize", "border", "name", "char<PERSON>t", "toUpperCase", "icon", "label", "toLowerCase", "color", "about", "my", "borderColor", "borderWidth", "container", "item", "phone", "skills", "Array", "isArray", "length", "flexWrap", "map", "skill", "idx", "transform", "mr", "us", "_us$session2$program", "session2", "program", "startDate", "endDate", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/ProfilePage.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n  Grid,\r\n  Box,\r\n  Button,\r\n  Avatar,\r\n  Divider,\r\n  <PERSON>,\r\n  Stack,\r\n} from \"@mui/material\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport EmailIcon from '@mui/icons-material/Email';\r\nimport PhoneIcon from '@mui/icons-material/Phone';\r\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\r\nimport WorkIcon from '@mui/icons-material/Work';\r\nimport PersonIcon from '@mui/icons-material/Person';\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\n\r\n\r\nconst ProfilePage = () => {\r\n  const { t } = useTranslation();\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(\"\");\r\n  const [sessions, setSessions] = useState([]);\r\nconst [sessionsLoading, setSessionsLoading] = useState(true);\r\n\r\n\r\nuseEffect(() => {\r\n  if (!user?.id) return;\r\n  setSessionsLoading(true);\r\n  axios.get(`http://localhost:8000/users/${user.id}/sessions2`)\r\n    .then((res) => setSessions(res.data))\r\n    .catch(() => setSessions([]))\r\n    .finally(() => setSessionsLoading(false));\r\n}, [user?.id, t]);\r\n\r\n  useEffect(() => {\r\n    const fetchUser = async () => {\r\n      console.log(\"ProfilePage: Début de fetchUser avec ID:\", id);\r\n\r\n      // Commencer par récupérer les données utilisateur depuis le localStorage\r\n      const storedUser = localStorage.getItem(\"user\");\r\n      let localStorageUserData = null;\r\n\r\n      if (storedUser) {\r\n        try {\r\n          localStorageUserData = JSON.parse(storedUser);\r\n          console.log(\"ProfilePage: Utilisateur récupéré depuis localStorage:\", localStorageUserData);\r\n        } catch (err) {\r\n          console.error(\"ProfilePage: Erreur lors de la récupération de l'utilisateur depuis localStorage:\", err);\r\n        }\r\n      }\r\n\r\n      // Si aucun ID n'est fourni dans l'URL\r\n      if (!id) {\r\n        console.log(\"ProfilePage: ID utilisateur manquant dans l'URL\");\r\n\r\n        // Utiliser les données du localStorage si disponibles\r\n        if (localStorageUserData) {\r\n          // Si l'utilisateur a un ID dans le localStorage, rediriger vers la page avec ID\r\n          if (localStorageUserData.id) {\r\n            const userId = typeof localStorageUserData.id === 'string'\r\n              ? parseInt(localStorageUserData.id, 10)\r\n              : localStorageUserData.id;\r\n\r\n            if (!isNaN(userId)) {\r\n              console.log(\"ProfilePage: Redirection vers la page de profil avec ID:\", userId);\r\n              navigate(`/ProfilePage/${userId}`, { replace: true });\r\n              return;\r\n            }\r\n          }\r\n\r\n          // Si pas d'ID valide mais des données utilisateur, les utiliser directement\r\n          setUser(localStorageUserData);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        setError(t('profile.missingUserData'));\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      console.log(\"ProfilePage: Tentative de chargement du profil pour l'ID:\", id);\r\n      setLoading(true);\r\n\r\n      // Convertir l'ID en nombre\r\n      const userId = typeof id === 'string' ? parseInt(id, 10) : id;\r\n\r\n      if (isNaN(userId)) {\r\n        console.error(\"ProfilePage: ID invalide:\", id);\r\n        setError(t('profile.invalidUserId'));\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Si l'ID dans l'URL correspond à l'ID dans le localStorage, utiliser les données du localStorage\r\n      if (localStorageUserData && localStorageUserData.id === userId) {\r\n        console.log(\"ProfilePage: Utilisation des données du localStorage car l'ID correspond\");\r\n        setUser(localStorageUserData);\r\n        setLoading(false);\r\n\r\n        // Mettre à jour les données en arrière-plan\r\n        try {\r\n          const res = await axios.get(`http://localhost:8000/users/id/${userId}`);\r\n          if (res.data) {\r\n            console.log(\"ProfilePage: Mise à jour des données utilisateur en arrière-plan:\", res.data);\r\n\r\n            // Mettre à jour le rôle pour khalil si nécessaire\r\n            if (res.data.email === \"<EMAIL>\" && res.data.role !== \"Admin\") {\r\n              res.data.role = \"Admin\";\r\n            }\r\n\r\n            setUser(res.data);\r\n            localStorage.setItem(\"user\", JSON.stringify(res.data));\r\n          }\r\n        } catch (err) {\r\n          console.error(\"ProfilePage: Erreur lors de la mise à jour des données en arrière-plan:\", err);\r\n        }\r\n\r\n        return;\r\n      }\r\n      \r\n\r\n      // Stratégie 1: Essayer directement par ID\r\n      try {\r\n        console.log(\"ProfilePage: Stratégie 1: Chargement par ID\", userId);\r\n        const res = await axios.get(`http://localhost:8000/users/id/${userId}`);\r\n\r\n        if (res.data) {\r\n          console.log(\"ProfilePage: Succès - Données utilisateur chargées par ID:\", res.data);\r\n\r\n          // Mettre à jour le rôle pour khalil si nécessaire\r\n          if (res.data.email === \"<EMAIL>\" && res.data.role !== \"Admin\") {\r\n            res.data.role = \"Admin\";\r\n          }\r\n\r\n          setUser(res.data);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      } catch (err) {\r\n        console.error(\"ProfilePage: Échec de la stratégie 1:\", err.response?.data || err.message);\r\n      }\r\n\r\n      // Stratégie 2: Utiliser les données du localStorage comme solution de secours\r\n      if (localStorageUserData) {\r\n        console.log(\"ProfilePage: Stratégie 2: Utilisation des données du localStorage comme solution de secours\");\r\n        setUser(localStorageUserData);\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Si tout échoue\r\n      setError(t('profile.loadUserError'));\r\n      setLoading(false);\r\n    };\r\n\r\n    fetchUser();\r\n  }, [id, navigate, t]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <Container sx={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        minHeight: '80vh'\r\n      }}>\r\n        <CircularProgress size={60} thickness={4} />\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  if (error || !user) {\r\n    return (\r\n      <Container maxWidth=\"sm\" sx={{ mt: 8 }}>\r\n        <Paper elevation={3} sx={{\r\n          p: 4,\r\n          borderRadius: 4,\r\n          textAlign: 'center',\r\n          background: 'linear-gradient(45deg, #f8f9fa 30%, #e9ecef 90%)'\r\n        }}>\r\n          <Typography variant=\"h5\" gutterBottom sx={{ fontWeight: 600 }}>\r\n            {t('profile.profileError')}\r\n          </Typography>\r\n          <Alert severity=\"error\" sx={{ mb: 3, borderRadius: 2 }}>\r\n            {error || t('profile.userNotFound')}\r\n          </Alert>\r\n          <Stack direction=\"row\" spacing={2} justifyContent=\"center\">\r\n            <Button\r\n              variant=\"contained\"\r\n              onClick={() => window.location.reload()}\r\n              sx={{ px: 4, py: 1 }}\r\n            >\r\n              {t('common.tryAgain')}\r\n            </Button>\r\n            <Button\r\n              variant=\"outlined\"\r\n              onClick={() => navigate('/')}\r\n              sx={{ px: 4, py: 1 }}\r\n            >\r\n              {t('common.goHome')}\r\n            </Button>\r\n          </Stack>\r\n        </Paper>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"md\" sx={{ py: 6 }}>\r\n      <Paper elevation={4} sx={{\r\n        p: 6,\r\n        borderRadius: 6,\r\n        background: 'linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%)'\r\n      }}>\r\n        {/* Header Section */}\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          mb: 4\r\n        }}>\r\n          <Typography variant=\"h4\" sx={{\r\n            fontWeight: 700,\r\n            background: 'linear-gradient(45deg, #1976d2 30%, #2196f3 90%)',\r\n            WebkitBackgroundClip: 'text',\r\n            WebkitTextFillColor: 'transparent'\r\n          }}>\r\n            {t('profile.userProfile')}\r\n          </Typography>\r\n          <Button\r\n            variant=\"contained\"\r\n            startIcon={<EditIcon />}\r\n            onClick={() => {\r\n              // Stocker temporairement l'utilisateur à éditer dans sessionStorage\r\n              console.log(\"Storing user data for editing:\", user);\r\n              sessionStorage.setItem(\"editingUser\", JSON.stringify(user));\r\n              navigate(`/EditProfile/${user.email}`);\r\n            }}\r\n            sx={{\r\n              borderRadius: 20,\r\n              px: 3,\r\n              py: 1,\r\n              textTransform: 'none',\r\n              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\r\n              '&:hover': {\r\n                boxShadow: '0 6px 8px rgba(0,0,0,0.15)'\r\n              }\r\n            }}\r\n          >\r\n            {t('profile.editProfile')}\r\n          </Button>\r\n        </Box>\r\n\r\n        {/* Profile Top Section */}\r\n        <Box sx={{\r\n          display: 'flex',\r\n          flexDirection: { xs: 'column', md: 'row' },\r\n          alignItems: 'center',\r\n          mb: 6,\r\n          gap: 4\r\n        }}>\r\n          <Avatar\r\n            src={user.profilePic ?\r\n              (user.profilePic.startsWith('/profile-pics/') ?\r\n                `http://localhost:8000/uploads${user.profilePic}` :\r\n                (user.profilePic.startsWith('http') ?\r\n                  user.profilePic :\r\n                  `http://localhost:8000/uploads/profile-pics/${user.profilePic.split('/').pop()}`\r\n                )\r\n              ) :\r\n              undefined\r\n            }\r\n            sx={{\r\n              width: 150,\r\n              height: 150,\r\n              fontSize: 60,\r\n              border: '4px solid #1976d2',\r\n              boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)'\r\n            }}\r\n          >\r\n            {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || \"U\"}\r\n          </Avatar>\r\n\r\n          <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>\r\n            <Typography variant=\"h3\" sx={{\r\n              fontWeight: 600,\r\n              mb: 1\r\n            }}>\r\n              {user.name}\r\n            </Typography>\r\n\r\n            <Chip\r\n              icon={<WorkIcon />}\r\n              label={user.role ? t(`role.${user.role.toLowerCase()}`) : t('role.etudiant')}\r\n              color=\"primary\"\r\n              variant=\"outlined\"\r\n              sx={{\r\n                borderRadius: 2,\r\n                px: 1,\r\n                fontSize: '0.9rem',\r\n                fontWeight: 500\r\n              }}\r\n            />\r\n\r\n            <Typography variant=\"body1\" sx={{\r\n              mt: 2,\r\n              color: 'text.secondary',\r\n              maxWidth: 500\r\n            }}>\r\n              {user.about || t('profile.noBio')}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n\r\n        <Divider sx={{\r\n          my: 4,\r\n          borderColor: 'rgba(0,0,0,0.1)',\r\n          borderWidth: 1\r\n        }} />\r\n\r\n        {/* Details Section */}\r\n        <Grid container spacing={4}>\r\n          {/* Contact Info */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"h5\" sx={{\r\n              mb: 3,\r\n              fontWeight: 600,\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              gap: 1\r\n            }}>\r\n              <PersonIcon color=\"primary\" /> {t('profile.personalInfo')}\r\n            </Typography>\r\n\r\n            <Stack spacing={2}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                <EmailIcon color=\"action\" />\r\n                <Typography>{user.email}</Typography>\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                <PhoneIcon color=\"action\" />\r\n                <Typography>{user.phone || t('profile.notProvided')}</Typography>\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                <LocationOnIcon color=\"action\" />\r\n                <Typography>{user.location || t('profile.locationNotSpecified')}</Typography>\r\n              </Box>\r\n            </Stack>\r\n          </Grid>\r\n\r\n          {/* Skills Section */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography variant=\"h5\" sx={{\r\n              mb: 3,\r\n              fontWeight: 600\r\n            }}>\r\n              {t('profile.skillsExpertise')}\r\n            </Typography>\r\n\r\n            {user.skills && Array.isArray(user.skills) && user.skills.length > 0 ? (\r\n              <Box sx={{\r\n                display: 'flex',\r\n                flexWrap: 'wrap',\r\n                gap: 1.5\r\n              }}>\r\n                {user.skills.map((skill, idx) => (\r\n                  <Chip\r\n                    key={idx}\r\n                    label={skill}\r\n                    color=\"primary\"\r\n                    sx={{\r\n                      borderRadius: 2,\r\n                      px: 1.5,\r\n                      py: 1,\r\n                      fontSize: '0.9rem',\r\n                      '&:hover': {\r\n                        transform: 'translateY(-2px)',\r\n                        boxShadow: '0 4px 8px rgba(25, 118, 210, 0.2)'\r\n                      }\r\n                    }}\r\n                  />\r\n                ))}\r\n              </Box>\r\n            ) : (\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                {t('profile.noSkillsAdded')}\r\n              </Typography>\r\n            )}\r\n          </Grid>\r\n        </Grid>\r\n        <Divider sx={{ my: 4 }} />\r\n\r\n<Box sx={{ mt: 4 }}>\r\n  <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 2 }}>\r\n    <WorkIcon color=\"primary\" sx={{ mr: 1, mb: '-5px' }} />\r\n    Sessions\r\n  </Typography>\r\n  {sessionsLoading ? (\r\n    <CircularProgress />\r\n  ) : sessions.length === 0 ? (\r\n    <Typography color=\"text.secondary\">This user is not assigned to any session.</Typography>\r\n  ) : (\r\n    <Stack spacing={2}>\r\n      {sessions.map((us) => (\r\n        <Paper\r\n          key={us.session2.id}\r\n          elevation={1}\r\n          sx={{\r\n            p: 2,\r\n            borderRadius: 2,\r\n            background: \"#f5faff\",\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n            flexWrap: \"wrap\"\r\n          }}\r\n        >\r\n          <Box>\r\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\r\n              {us.session2.name}\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              Programme: {us.session2.program?.name || \"Non spécifié\"}\r\n            </Typography>\r\n            {us.session2.startDate && us.session2.endDate && (\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                Du {new Date(us.session2.startDate).toLocaleDateString()} au {new Date(us.session2.endDate).toLocaleDateString()}\r\n              </Typography>\r\n            )}\r\n          </Box>\r\n        </Paper>\r\n      ))}\r\n    </Stack>\r\n  )}\r\n</Box>\r\n\r\n      </Paper>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProfilePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA;EACxB,MAAM;IAAEC;EAAE,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAG,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAC1B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAG5DD,SAAS,CAAC,MAAM;IACd,IAAI,EAACiC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEF,EAAE,GAAE;IACfW,kBAAkB,CAAC,IAAI,CAAC;IACxBnB,KAAK,CAACoB,GAAG,CAAC,+BAA+BV,IAAI,CAACF,EAAE,YAAY,CAAC,CAC1Da,IAAI,CAAEC,GAAG,IAAKL,WAAW,CAACK,GAAG,CAACC,IAAI,CAAC,CAAC,CACpCC,KAAK,CAAC,MAAMP,WAAW,CAAC,EAAE,CAAC,CAAC,CAC5BQ,OAAO,CAAC,MAAMN,kBAAkB,CAAC,KAAK,CAAC,CAAC;EAC7C,CAAC,EAAE,CAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEF,EAAE,EAAED,CAAC,CAAC,CAAC;EAEf9B,SAAS,CAAC,MAAM;IACd,MAAMiD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEpB,EAAE,CAAC;;MAE3D;MACA,MAAMqB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIC,oBAAoB,GAAG,IAAI;MAE/B,IAAIH,UAAU,EAAE;QACd,IAAI;UACFG,oBAAoB,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;UAC7CF,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEI,oBAAoB,CAAC;QAC7F,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZR,OAAO,CAACb,KAAK,CAAC,mFAAmF,EAAEqB,GAAG,CAAC;QACzG;MACF;;MAEA;MACA,IAAI,CAAC3B,EAAE,EAAE;QACPmB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAII,oBAAoB,EAAE;UACxB;UACA,IAAIA,oBAAoB,CAACxB,EAAE,EAAE;YAC3B,MAAM4B,MAAM,GAAG,OAAOJ,oBAAoB,CAACxB,EAAE,KAAK,QAAQ,GACtD6B,QAAQ,CAACL,oBAAoB,CAACxB,EAAE,EAAE,EAAE,CAAC,GACrCwB,oBAAoB,CAACxB,EAAE;YAE3B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC,EAAE;cAClBT,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEQ,MAAM,CAAC;cAC/E3B,QAAQ,CAAC,gBAAgB2B,MAAM,EAAE,EAAE;gBAAEG,OAAO,EAAE;cAAK,CAAC,CAAC;cACrD;YACF;UACF;;UAEA;UACA5B,OAAO,CAACqB,oBAAoB,CAAC;UAC7BnB,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAE,QAAQ,CAACR,CAAC,CAAC,yBAAyB,CAAC,CAAC;QACtCM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAc,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAEpB,EAAE,CAAC;MAC5EK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuB,MAAM,GAAG,OAAO5B,EAAE,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,EAAE,EAAE,EAAE,CAAC,GAAGA,EAAE;MAE7D,IAAI8B,KAAK,CAACF,MAAM,CAAC,EAAE;QACjBT,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEN,EAAE,CAAC;QAC9CO,QAAQ,CAACR,CAAC,CAAC,uBAAuB,CAAC,CAAC;QACpCM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAImB,oBAAoB,IAAIA,oBAAoB,CAACxB,EAAE,KAAK4B,MAAM,EAAE;QAC9DT,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;QACvFjB,OAAO,CAACqB,oBAAoB,CAAC;QAC7BnB,UAAU,CAAC,KAAK,CAAC;;QAEjB;QACA,IAAI;UACF,MAAMS,GAAG,GAAG,MAAMtB,KAAK,CAACoB,GAAG,CAAC,kCAAkCgB,MAAM,EAAE,CAAC;UACvE,IAAId,GAAG,CAACC,IAAI,EAAE;YACZI,OAAO,CAACC,GAAG,CAAC,mEAAmE,EAAEN,GAAG,CAACC,IAAI,CAAC;;YAE1F;YACA,IAAID,GAAG,CAACC,IAAI,CAACiB,KAAK,KAAK,kBAAkB,IAAIlB,GAAG,CAACC,IAAI,CAACkB,IAAI,KAAK,OAAO,EAAE;cACtEnB,GAAG,CAACC,IAAI,CAACkB,IAAI,GAAG,OAAO;YACzB;YAEA9B,OAAO,CAACW,GAAG,CAACC,IAAI,CAAC;YACjBO,YAAY,CAACY,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACrB,GAAG,CAACC,IAAI,CAAC,CAAC;UACxD;QACF,CAAC,CAAC,OAAOY,GAAG,EAAE;UACZR,OAAO,CAACb,KAAK,CAAC,yEAAyE,EAAEqB,GAAG,CAAC;QAC/F;QAEA;MACF;;MAGA;MACA,IAAI;QACFR,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEQ,MAAM,CAAC;QAClE,MAAMd,GAAG,GAAG,MAAMtB,KAAK,CAACoB,GAAG,CAAC,kCAAkCgB,MAAM,EAAE,CAAC;QAEvE,IAAId,GAAG,CAACC,IAAI,EAAE;UACZI,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEN,GAAG,CAACC,IAAI,CAAC;;UAEnF;UACA,IAAID,GAAG,CAACC,IAAI,CAACiB,KAAK,KAAK,kBAAkB,IAAIlB,GAAG,CAACC,IAAI,CAACkB,IAAI,KAAK,OAAO,EAAE;YACtEnB,GAAG,CAACC,IAAI,CAACkB,IAAI,GAAG,OAAO;UACzB;UAEA9B,OAAO,CAACW,GAAG,CAACC,IAAI,CAAC;UACjBV,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;QAAA,IAAAS,aAAA;QACZjB,OAAO,CAACb,KAAK,CAAC,uCAAuC,EAAE,EAAA8B,aAAA,GAAAT,GAAG,CAACU,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcrB,IAAI,KAAIY,GAAG,CAACW,OAAO,CAAC;MAC3F;;MAEA;MACA,IAAId,oBAAoB,EAAE;QACxBL,OAAO,CAACC,GAAG,CAAC,6FAA6F,CAAC;QAC1GjB,OAAO,CAACqB,oBAAoB,CAAC;QAC7BnB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAE,QAAQ,CAACR,CAAC,CAAC,uBAAuB,CAAC,CAAC;MACpCM,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDa,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,EAAEF,CAAC,CAAC,CAAC;EAErB,IAAIK,OAAO,EAAE;IACX,oBACEV,OAAA,CAACvB,SAAS;MAACoE,EAAE,EAAE;QACbC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,eACAlD,OAAA,CAACpB,gBAAgB;QAACuE,IAAI,EAAE,EAAG;QAACC,SAAS,EAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEhB;EAEA,IAAI5C,KAAK,IAAI,CAACJ,IAAI,EAAE;IAClB,oBACER,OAAA,CAACvB,SAAS;MAACgF,QAAQ,EAAC,IAAI;MAACZ,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eACrClD,OAAA,CAACtB,KAAK;QAACiF,SAAS,EAAE,CAAE;QAACd,EAAE,EAAE;UACvBe,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,QAAQ;UACnBC,UAAU,EAAE;QACd,CAAE;QAAAb,QAAA,gBACAlD,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACC,YAAY;UAACpB,EAAE,EAAE;YAAEqB,UAAU,EAAE;UAAI,CAAE;UAAAhB,QAAA,EAC3D7C,CAAC,CAAC,sBAAsB;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACbxD,OAAA,CAACnB,KAAK;UAACsF,QAAQ,EAAC,OAAO;UAACtB,EAAE,EAAE;YAAEuB,EAAE,EAAE,CAAC;YAAEP,YAAY,EAAE;UAAE,CAAE;UAAAX,QAAA,EACpDtC,KAAK,IAAIP,CAAC,CAAC,sBAAsB;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACRxD,OAAA,CAACZ,KAAK;UAACiF,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAACvB,cAAc,EAAC,QAAQ;UAAAG,QAAA,gBACxDlD,OAAA,CAAChB,MAAM;YACLgF,OAAO,EAAC,WAAW;YACnBO,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxC7B,EAAE,EAAE;cAAE8B,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,EAEpB7C,CAAC,CAAC,iBAAiB;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACTxD,OAAA,CAAChB,MAAM;YACLgF,OAAO,EAAC,UAAU;YAClBO,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,GAAG,CAAE;YAC7BsC,EAAE,EAAE;cAAE8B,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,EAEpB7C,CAAC,CAAC,eAAe;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACExD,OAAA,CAACvB,SAAS;IAACgF,QAAQ,EAAC,IAAI;IAACZ,EAAE,EAAE;MAAE+B,EAAE,EAAE;IAAE,CAAE;IAAA1B,QAAA,eACrClD,OAAA,CAACtB,KAAK;MAACiF,SAAS,EAAE,CAAE;MAACd,EAAE,EAAE;QACvBe,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,CAAC;QACfE,UAAU,EAAE;MACd,CAAE;MAAAb,QAAA,gBAEAlD,OAAA,CAACjB,GAAG;QAAC8D,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBoB,EAAE,EAAE;QACN,CAAE;QAAAlB,QAAA,gBACAlD,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACnB,EAAE,EAAE;YAC3BqB,UAAU,EAAE,GAAG;YACfH,UAAU,EAAE,kDAAkD;YAC9Dc,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE;UACvB,CAAE;UAAA5B,QAAA,EACC7C,CAAC,CAAC,qBAAqB;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbxD,OAAA,CAAChB,MAAM;UACLgF,OAAO,EAAC,WAAW;UACnBe,SAAS,eAAE/E,OAAA,CAACT,QAAQ;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBe,OAAO,EAAEA,CAAA,KAAM;YACb;YACA9C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAElB,IAAI,CAAC;YACnDwE,cAAc,CAACxC,OAAO,CAAC,aAAa,EAAET,IAAI,CAACU,SAAS,CAACjC,IAAI,CAAC,CAAC;YAC3DD,QAAQ,CAAC,gBAAgBC,IAAI,CAAC8B,KAAK,EAAE,CAAC;UACxC,CAAE;UACFO,EAAE,EAAE;YACFgB,YAAY,EAAE,EAAE;YAChBc,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLK,aAAa,EAAE,MAAM;YACrBC,SAAS,EAAE,2BAA2B;YACtC,SAAS,EAAE;cACTA,SAAS,EAAE;YACb;UACF,CAAE;UAAAhC,QAAA,EAED7C,CAAC,CAAC,qBAAqB;QAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxD,OAAA,CAACjB,GAAG;QAAC8D,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfqC,aAAa,EAAE;YAAEC,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAC;UAC1CrC,UAAU,EAAE,QAAQ;UACpBoB,EAAE,EAAE,CAAC;UACLkB,GAAG,EAAE;QACP,CAAE;QAAApC,QAAA,gBACAlD,OAAA,CAACf,MAAM;UACLsG,GAAG,EAAE/E,IAAI,CAACgF,UAAU,GACjBhF,IAAI,CAACgF,UAAU,CAACC,UAAU,CAAC,gBAAgB,CAAC,GAC3C,gCAAgCjF,IAAI,CAACgF,UAAU,EAAE,GAChDhF,IAAI,CAACgF,UAAU,CAACC,UAAU,CAAC,MAAM,CAAC,GACjCjF,IAAI,CAACgF,UAAU,GACf,8CAA8ChF,IAAI,CAACgF,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,EAC/E,GAEHC,SACD;UACD/C,EAAE,EAAE;YACFgD,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXC,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,mBAAmB;YAC3Bd,SAAS,EAAE;UACb,CAAE;UAAAhC,QAAA,EAED,EAAA/C,UAAA,GAAAK,IAAI,CAACyF,IAAI,cAAA9F,UAAA,uBAATA,UAAA,CAAW+F,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,OAAA/F,WAAA,GAAII,IAAI,CAAC8B,KAAK,cAAAlC,WAAA,uBAAVA,WAAA,CAAY8F,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAG;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eAETxD,OAAA,CAACjB,GAAG;UAAC8D,EAAE,EAAE;YAAEiB,SAAS,EAAE;cAAEsB,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO;UAAE,CAAE;UAAAnC,QAAA,gBACnDlD,OAAA,CAACrB,UAAU;YAACqF,OAAO,EAAC,IAAI;YAACnB,EAAE,EAAE;cAC3BqB,UAAU,EAAE,GAAG;cACfE,EAAE,EAAE;YACN,CAAE;YAAAlB,QAAA,EACC1C,IAAI,CAACyF;UAAI;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEbxD,OAAA,CAACb,IAAI;YACHiH,IAAI,eAAEpG,OAAA,CAACL,QAAQ;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnB6C,KAAK,EAAE7F,IAAI,CAAC+B,IAAI,GAAGlC,CAAC,CAAC,QAAQG,IAAI,CAAC+B,IAAI,CAAC+D,WAAW,CAAC,CAAC,EAAE,CAAC,GAAGjG,CAAC,CAAC,eAAe,CAAE;YAC7EkG,KAAK,EAAC,SAAS;YACfvC,OAAO,EAAC,UAAU;YAClBnB,EAAE,EAAE;cACFgB,YAAY,EAAE,CAAC;cACfc,EAAE,EAAE,CAAC;cACLoB,QAAQ,EAAE,QAAQ;cAClB7B,UAAU,EAAE;YACd;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFxD,OAAA,CAACrB,UAAU;YAACqF,OAAO,EAAC,OAAO;YAACnB,EAAE,EAAE;cAC9Ba,EAAE,EAAE,CAAC;cACL6C,KAAK,EAAE,gBAAgB;cACvB9C,QAAQ,EAAE;YACZ,CAAE;YAAAP,QAAA,EACC1C,IAAI,CAACgG,KAAK,IAAInG,CAAC,CAAC,eAAe;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA,CAACd,OAAO;QAAC2D,EAAE,EAAE;UACX4D,EAAE,EAAE,CAAC;UACLC,WAAW,EAAE,iBAAiB;UAC9BC,WAAW,EAAE;QACf;MAAE;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGLxD,OAAA,CAAClB,IAAI;QAAC8H,SAAS;QAACtC,OAAO,EAAE,CAAE;QAAApB,QAAA,gBAEzBlD,OAAA,CAAClB,IAAI;UAAC+H,IAAI;UAACzB,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnC,QAAA,gBACvBlD,OAAA,CAACrB,UAAU;YAACqF,OAAO,EAAC,IAAI;YAACnB,EAAE,EAAE;cAC3BuB,EAAE,EAAE,CAAC;cACLF,UAAU,EAAE,GAAG;cACfpB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBsC,GAAG,EAAE;YACP,CAAE;YAAApC,QAAA,gBACAlD,OAAA,CAACJ,UAAU;cAAC2G,KAAK,EAAC;YAAS;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,KAAC,EAACnD,CAAC,CAAC,sBAAsB,CAAC;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEbxD,OAAA,CAACZ,KAAK;YAACkF,OAAO,EAAE,CAAE;YAAApB,QAAA,gBAChBlD,OAAA,CAACjB,GAAG;cAAC8D,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEsC,GAAG,EAAE;cAAE,CAAE;cAAApC,QAAA,gBACzDlD,OAAA,CAACR,SAAS;gBAAC+G,KAAK,EAAC;cAAQ;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BxD,OAAA,CAACrB,UAAU;gBAAAuE,QAAA,EAAE1C,IAAI,CAAC8B;cAAK;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eAENxD,OAAA,CAACjB,GAAG;cAAC8D,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEsC,GAAG,EAAE;cAAE,CAAE;cAAApC,QAAA,gBACzDlD,OAAA,CAACP,SAAS;gBAAC8G,KAAK,EAAC;cAAQ;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BxD,OAAA,CAACrB,UAAU;gBAAAuE,QAAA,EAAE1C,IAAI,CAACsG,KAAK,IAAIzG,CAAC,CAAC,qBAAqB;cAAC;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eAENxD,OAAA,CAACjB,GAAG;cAAC8D,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEsC,GAAG,EAAE;cAAE,CAAE;cAAApC,QAAA,gBACzDlD,OAAA,CAACN,cAAc;gBAAC6G,KAAK,EAAC;cAAQ;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjCxD,OAAA,CAACrB,UAAU;gBAAAuE,QAAA,EAAE1C,IAAI,CAACiE,QAAQ,IAAIpE,CAAC,CAAC,8BAA8B;cAAC;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPxD,OAAA,CAAClB,IAAI;UAAC+H,IAAI;UAACzB,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnC,QAAA,gBACvBlD,OAAA,CAACrB,UAAU;YAACqF,OAAO,EAAC,IAAI;YAACnB,EAAE,EAAE;cAC3BuB,EAAE,EAAE,CAAC;cACLF,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,EACC7C,CAAC,CAAC,yBAAyB;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EAEZhD,IAAI,CAACuG,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACzG,IAAI,CAACuG,MAAM,CAAC,IAAIvG,IAAI,CAACuG,MAAM,CAACG,MAAM,GAAG,CAAC,gBAClElH,OAAA,CAACjB,GAAG;YAAC8D,EAAE,EAAE;cACPC,OAAO,EAAE,MAAM;cACfqE,QAAQ,EAAE,MAAM;cAChB7B,GAAG,EAAE;YACP,CAAE;YAAApC,QAAA,EACC1C,IAAI,CAACuG,MAAM,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,kBAC1BtH,OAAA,CAACb,IAAI;cAEHkH,KAAK,EAAEgB,KAAM;cACbd,KAAK,EAAC,SAAS;cACf1D,EAAE,EAAE;gBACFgB,YAAY,EAAE,CAAC;gBACfc,EAAE,EAAE,GAAG;gBACPC,EAAE,EAAE,CAAC;gBACLmB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE;kBACTwB,SAAS,EAAE,kBAAkB;kBAC7BrC,SAAS,EAAE;gBACb;cACF;YAAE,GAZGoC,GAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaT,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENxD,OAAA,CAACrB,UAAU;YAACqF,OAAO,EAAC,OAAO;YAACuC,KAAK,EAAC,gBAAgB;YAAArD,QAAA,EAC/C7C,CAAC,CAAC,uBAAuB;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxD,OAAA,CAACd,OAAO;QAAC2D,EAAE,EAAE;UAAE4D,EAAE,EAAE;QAAE;MAAE;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAElCxD,OAAA,CAACjB,GAAG;QAAC8D,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjBlD,OAAA,CAACrB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACnB,EAAE,EAAE;YAAEqB,UAAU,EAAE,GAAG;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACtDlD,OAAA,CAACL,QAAQ;YAAC4G,KAAK,EAAC,SAAS;YAAC1D,EAAE,EAAE;cAAE2E,EAAE,EAAE,CAAC;cAAEpD,EAAE,EAAE;YAAO;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZxC,eAAe,gBACdhB,OAAA,CAACpB,gBAAgB;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAClB1C,QAAQ,CAACoG,MAAM,KAAK,CAAC,gBACvBlH,OAAA,CAACrB,UAAU;UAAC4H,KAAK,EAAC,gBAAgB;UAAArD,QAAA,EAAC;QAAyC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEzFxD,OAAA,CAACZ,KAAK;UAACkF,OAAO,EAAE,CAAE;UAAApB,QAAA,EACfpC,QAAQ,CAACsG,GAAG,CAAEK,EAAE;YAAA,IAAAC,oBAAA;YAAA,oBACf1H,OAAA,CAACtB,KAAK;cAEJiF,SAAS,EAAE,CAAE;cACbd,EAAE,EAAE;gBACFe,CAAC,EAAE,CAAC;gBACJC,YAAY,EAAE,CAAC;gBACfE,UAAU,EAAE,SAAS;gBACrBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBsC,GAAG,EAAE,CAAC;gBACN6B,QAAQ,EAAE;cACZ,CAAE;cAAAjE,QAAA,eAEFlD,OAAA,CAACjB,GAAG;gBAAAmE,QAAA,gBACFlD,OAAA,CAACrB,UAAU;kBAACqF,OAAO,EAAC,WAAW;kBAACnB,EAAE,EAAE;oBAAEqB,UAAU,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,EACrDuE,EAAE,CAACE,QAAQ,CAAC1B;gBAAI;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACbxD,OAAA,CAACrB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACuC,KAAK,EAAC,gBAAgB;kBAAArD,QAAA,GAAC,aACtC,EAAC,EAAAwE,oBAAA,GAAAD,EAAE,CAACE,QAAQ,CAACC,OAAO,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBzB,IAAI,KAAI,cAAc;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,EACZiE,EAAE,CAACE,QAAQ,CAACE,SAAS,IAAIJ,EAAE,CAACE,QAAQ,CAACG,OAAO,iBAC3C9H,OAAA,CAACrB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACuC,KAAK,EAAC,gBAAgB;kBAAArD,QAAA,GAAC,KAC9C,EAAC,IAAI6E,IAAI,CAACN,EAAE,CAACE,QAAQ,CAACE,SAAS,CAAC,CAACG,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAAC,IAAID,IAAI,CAACN,EAAE,CAACE,QAAQ,CAACG,OAAO,CAAC,CAACE,kBAAkB,CAAC,CAAC;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAxBDiE,EAAE,CAACE,QAAQ,CAACrH,EAAE;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBd,CAAC;UAAA,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACtD,EAAA,CA5aID,WAAW;EAAA,QACDJ,cAAc,EACbR,SAAS,EACPC,WAAW;AAAA;AAAA2I,EAAA,GAHxBhI,WAAW;AA8ajB,eAAeA,WAAW;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}