{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\EditProgramView.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Paper, Button, Checkbox, FormGroup, FormControlLabel, Stepper, Step, StepLabel, TextField } from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\nimport Accordion from \"@mui/material/Accordion\";\nimport AccordionSummary from \"@mui/material/AccordionSummary\";\nimport AccordionDetails from \"@mui/material/AccordionDetails\";\nimport axios from \"axios\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { MenuItem } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst steps = [\"Modules, cours et contenus\", \"Niveau du programme\"];\nconst EditProgramBuildView = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    programId\n  } = useParams();\n  const [activeStep, setActiveStep] = useState(0);\n  const [programName, setProgramName] = useState(\"\");\n  const [modules, setModules] = useState([]);\n  const [coursesByModule, setCoursesByModule] = useState({});\n  const [contenusByCourse, setContenusByCourse] = useState({});\n  const [selectedModules, setSelectedModules] = useState([]);\n  const [selectedCourses, setSelectedCourses] = useState({});\n  const [selectedContenus, setSelectedContenus] = useState({});\n  const [level, setLevel] = useState(\"\");\n  const [buildProgramId, setbuildProgramId] = useState(null);\n  const [search, setSearch] = useState(\"\");\n  const [searchScope, setSearchScope] = useState(\"module\");\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/modules\").then(res => setModules(res.data));\n    axios.get(`http://localhost:8000/buildProgram/program/${programId}`).then(async res => {\n      const buildProgram = res.data;\n      setbuildProgramId(buildProgram.id);\n      setProgramName(buildProgram.program.name);\n      setLevel(buildProgram.level);\n      const selectedModuleIds = buildProgram.modules.map(sm => sm.module.id);\n      setSelectedModules(selectedModuleIds);\n      const courseMap = {};\n      const contenuMap = {};\n      for (const sm of buildProgram.modules) {\n        const moduleId = sm.module.id;\n        if (!coursesByModule[moduleId]) {\n          const res = await axios.get(\"http://localhost:8000/courses\");\n          setCoursesByModule(prev => ({\n            ...prev,\n            [moduleId]: res.data\n          }));\n        }\n        courseMap[moduleId] = sm.courses.map(sc => sc.course.id);\n        for (const sc of sm.courses) {\n          const courseId = sc.course.id;\n          const key = `${moduleId}-${courseId}`;\n          if (!contenusByCourse[courseId]) {\n            const res = await axios.get(\"http://localhost:8000/contenus\");\n            setContenusByCourse(prev => ({\n              ...prev,\n              [courseId]: res.data\n            }));\n          }\n          contenuMap[key] = sc.contenus.map(scct => scct.contenu.id);\n        }\n      }\n      setSelectedCourses(courseMap);\n      setSelectedContenus(contenuMap);\n    }).catch(err => {\n      console.error(\"Erreur chargement Program du programme:\", err);\n    });\n  }, [programId, coursesByModule, contenusByCourse]);\n  const handleModuleToggle = async moduleId => {\n    const updated = selectedModules.includes(moduleId) ? selectedModules.filter(id => id !== moduleId) : [...selectedModules, moduleId];\n    setSelectedModules(updated);\n    if (!coursesByModule[moduleId]) {\n      const res = await axios.get(\"http://localhost:8000/courses\");\n      setCoursesByModule(prev => ({\n        ...prev,\n        [moduleId]: res.data\n      }));\n    }\n  };\n  const handleCourseToggle = async (moduleId, courseId) => {\n    const current = selectedCourses[moduleId] || [];\n    const updated = current.includes(courseId) ? current.filter(id => id !== courseId) : [...current, courseId];\n    setSelectedCourses(prev => ({\n      ...prev,\n      [moduleId]: updated\n    }));\n    if (!contenusByCourse[courseId]) {\n      const res = await axios.get(\"http://localhost:8000/contenus\");\n      setContenusByCourse(prev => ({\n        ...prev,\n        [courseId]: res.data\n      }));\n    }\n  };\n  const handleContenuToggle = (moduleId, courseId, contenuId) => {\n    const key = `${moduleId}-${courseId}`;\n    const current = selectedContenus[key] || [];\n    const updated = current.includes(contenuId) ? current.filter(id => id !== contenuId) : [...current, contenuId];\n    setSelectedContenus(prev => ({\n      ...prev,\n      [key]: updated\n    }));\n  };\n  const handleSubmit = async () => {\n    if (!buildProgramId || !programName || !level || selectedModules.length === 0) {\n      alert(\"Veuillez remplir tous les champs nécessaires.\");\n      return;\n    }\n    try {\n      // 1. Update program name\n      await axios.patch(`http://localhost:8000/programs/${programId}`, {\n        name: programName\n      });\n\n      // 2. Update buildProgram structure\n      const modulesPayload = selectedModules.map(moduleId => {\n        const courseIds = selectedCourses[moduleId] || [];\n        return {\n          moduleId,\n          courses: courseIds.map(courseId => {\n            const key = `${moduleId}-${courseId}`;\n            const contenuIds = selectedContenus[key] || [];\n            return {\n              courseId,\n              contenus: contenuIds.map(contenuId => ({\n                contenuId\n              }))\n            };\n          })\n        };\n      });\n      const formData = new FormData();\n      formData.append(\"level\", level);\n      formData.append(\"modules\", JSON.stringify(modulesPayload));\n      await axios.patch(`http://localhost:8000/buildProgram/${buildProgramId}`, formData, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      alert(\"✅ Programme modifié avec succès !\");\n      navigate(\"/programs\");\n    } catch (err) {\n      console.error(err);\n      alert(\"❌ Erreur lors de la modification du programme.\");\n    }\n  };\n  const filteredModules = modules.filter(mod => {\n    if (searchScope === \"module\") {\n      return mod.name.toLowerCase().includes(search.toLowerCase());\n    }\n    if (searchScope === \"course\") {\n      const courses = coursesByModule[mod.id] || [];\n      return courses.some(course => course.title.toLowerCase().includes(search.toLowerCase()));\n    }\n    if (searchScope === \"contenu\") {\n      const courses = coursesByModule[mod.id] || [];\n      return courses.some(course => {\n        const contenus = contenusByCourse[course.id] || [];\n        return contenus.some(c => c.title.toLowerCase().includes(search.toLowerCase()));\n      });\n    }\n    return true;\n  });\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      mt: 6\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 4,\n        width: \"100%\",\n        maxWidth: 1000,\n        borderRadius: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        align: \"center\",\n        gutterBottom: true,\n        children: \"\\uD83D\\uDEE0\\uFE0F Modifier un Programme\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        label: \"Nom du programme\",\n        variant: \"outlined\",\n        value: programName,\n        onChange: e => setProgramName(e.target.value),\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          variant: \"outlined\",\n          size: \"small\",\n          label: `🔍 Rechercher par ${searchScope}`,\n          value: search,\n          onChange: e => setSearch(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          select: true,\n          label: \"Filtrer par\",\n          size: \"small\",\n          value: searchScope,\n          onChange: e => setSearchScope(e.target.value),\n          sx: {\n            minWidth: 160\n          },\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"module\",\n            children: \"Module\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"course\",\n            children: \"Cours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"contenu\",\n            children: \"Contenu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n        activeStep: activeStep,\n        alternativeLabel: true,\n        sx: {\n          mb: 4\n        },\n        children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n          children: /*#__PURE__*/_jsxDEV(StepLabel, {\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this)\n        }, label, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 7\n      }, this), activeStep === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"\\uD83D\\uDCE6 Modules\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: filteredModules.map(m => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: selectedModules.includes(m.id),\n              onChange: () => handleModuleToggle(m.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this),\n            label: m.name\n          }, m.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), selectedModules.map(moduleId => {\n          var _modules$find;\n          return /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mt: 2\n            },\n            defaultExpanded: true,\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 45\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                fontWeight: \"bold\",\n                children: [\"\\uD83E\\uDDE9 \", (_modules$find = modules.find(m => m.id === moduleId)) === null || _modules$find === void 0 ? void 0 : _modules$find.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: (coursesByModule[moduleId] || []).map(course => {\n                var _contenusByCourse$cou;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ml: 2,\n                  mt: 1,\n                  children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                    control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: (selectedCourses[moduleId] || []).includes(course.id),\n                      onChange: () => handleCourseToggle(moduleId, course.id)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this),\n                    label: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this), (selectedCourses[moduleId] || []).includes(course.id) && ((_contenusByCourse$cou = contenusByCourse[course.id]) === null || _contenusByCourse$cou === void 0 ? void 0 : _contenusByCourse$cou.length) > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                    ml: 4,\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: \"\\uD83D\\uDCC4 Contenus :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n                      children: contenusByCourse[course.id].map(ct => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                        control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                          checked: (selectedContenus[`${moduleId}-${course.id}`] || []).includes(ct.id),\n                          onChange: () => handleContenuToggle(moduleId, course.id, ct.id)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 280,\n                          columnNumber: 35\n                        }, this),\n                        label: ct.title\n                      }, ct.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)]\n                }, course.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, moduleId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this);\n        }), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 3,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            onClick: () => navigate(\"/programs\"),\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => setActiveStep(1),\n            children: \"Suivant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true), activeStep === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"\\uD83C\\uDFAF Niveau du programme\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [\"Basique\", \"Intermédiaire\", \"Avancé\"].map(lvl => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: level === lvl,\n              onChange: () => setLevel(lvl)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this),\n            label: lvl\n          }, lvl, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 3,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => setActiveStep(0),\n            children: \"Retour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"error\",\n              sx: {\n                mr: 2\n              },\n              onClick: () => navigate(\"/programs\"),\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSubmit,\n              children: \"Enregistrer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 3\n  }, this);\n};\n_s(EditProgramBuildView, \"StBgeL/aztUF7zg5KVPH1Mue/g0=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = EditProgramBuildView;\nexport default EditProgramBuildView;\nvar _c;\n$RefreshReg$(_c, \"EditProgramBuildView\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Checkbox", "FormGroup", "FormControlLabel", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "ExpandMoreIcon", "Accordion", "AccordionSummary", "AccordionDetails", "axios", "useNavigate", "useParams", "MenuItem", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "steps", "EditProgramBuildView", "_s", "navigate", "programId", "activeStep", "setActiveStep", "programName", "setProgramName", "modules", "setModules", "coursesByModule", "setCoursesByModule", "contenusByCourse", "setContenusByCourse", "selectedModules", "setSelectedModules", "selectedCourses", "setSelectedCourses", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedContenus", "level", "setLevel", "buildProgramId", "setbuildProgramId", "search", "setSearch", "searchScope", "setSearchScope", "get", "then", "res", "data", "buildProgram", "id", "program", "name", "selectedModuleIds", "map", "sm", "module", "courseMap", "contenuMap", "moduleId", "prev", "courses", "sc", "course", "courseId", "key", "contenus", "scct", "contenu", "catch", "err", "console", "error", "handleModuleToggle", "updated", "includes", "filter", "handleCourseToggle", "current", "handleContenuToggle", "contenuId", "handleSubmit", "length", "alert", "patch", "modulesPayload", "courseIds", "contenuIds", "formData", "FormData", "append", "JSON", "stringify", "headers", "filteredModules", "mod", "toLowerCase", "some", "title", "c", "sx", "display", "justifyContent", "mt", "children", "p", "width", "max<PERSON><PERSON><PERSON>", "borderRadius", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "value", "onChange", "e", "target", "mb", "gap", "size", "select", "min<PERSON><PERSON><PERSON>", "alternativeLabel", "m", "control", "checked", "_modules$find", "defaultExpanded", "expandIcon", "fontWeight", "find", "_contenusByCourse$cou", "ml", "ct", "color", "onClick", "lvl", "mr", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/EditProgramView.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Box, Typography, Paper, Button, Checkbox,\r\n  FormGroup, FormControlLabel, Stepper, Step, StepLabel, TextField\r\n} from \"@mui/material\";\r\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\r\nimport Accordion from \"@mui/material/Accordion\";\r\nimport AccordionSummary from \"@mui/material/AccordionSummary\";\r\nimport AccordionDetails from \"@mui/material/AccordionDetails\";\r\nimport axios from \"axios\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { MenuItem } from \"@mui/material\";\r\n\r\n\r\nconst steps = [\"Modules, cours et contenus\", \"Niveau du programme\"];\r\n\r\nconst EditProgramBuildView = () => {\r\n  const navigate = useNavigate();\r\n  const { programId } = useParams();\r\n\r\n  const [activeStep, setActiveStep] = useState(0);\r\n  const [programName, setProgramName] = useState(\"\");\r\n  const [modules, setModules] = useState([]);\r\n  const [coursesByModule, setCoursesByModule] = useState({});\r\n  const [contenusByCourse, setContenusByCourse] = useState({});\r\n  const [selectedModules, setSelectedModules] = useState([]);\r\n  const [selectedCourses, setSelectedCourses] = useState({});\r\n  const [selectedContenus, setSelectedContenus] = useState({});\r\n  const [level, setLevel] = useState(\"\");\r\n  const [buildProgramId, setbuildProgramId] = useState(null);\r\nconst [search, setSearch] = useState(\"\");\r\nconst [searchScope, setSearchScope] = useState(\"module\");\r\n\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/modules\").then(res => setModules(res.data));\r\n\r\n    axios.get(`http://localhost:8000/buildProgram/program/${programId}`)\r\n      .then(async (res) => {\r\n        const buildProgram = res.data;\r\n        setbuildProgramId(buildProgram.id);\r\n        setProgramName(buildProgram.program.name);\r\n        setLevel(buildProgram.level);\r\n\r\n        const selectedModuleIds = buildProgram.modules.map(sm => sm.module.id);\r\n        setSelectedModules(selectedModuleIds);\r\n\r\n        const courseMap = {};\r\n        const contenuMap = {};\r\n\r\n        for (const sm of buildProgram.modules) {\r\n          const moduleId = sm.module.id;\r\n\r\n          if (!coursesByModule[moduleId]) {\r\n            const res = await axios.get(\"http://localhost:8000/courses\");\r\n            setCoursesByModule(prev => ({ ...prev, [moduleId]: res.data }));\r\n          }\r\n\r\n          courseMap[moduleId] = sm.courses.map(sc => sc.course.id);\r\n\r\n          for (const sc of sm.courses) {\r\n            const courseId = sc.course.id;\r\n            const key = `${moduleId}-${courseId}`;\r\n            if (!contenusByCourse[courseId]) {\r\n              const res = await axios.get(\"http://localhost:8000/contenus\");\r\n              setContenusByCourse(prev => ({ ...prev, [courseId]: res.data }));\r\n            }\r\n            contenuMap[key] = sc.contenus.map(scct => scct.contenu.id);\r\n          }\r\n        }\r\n\r\n        setSelectedCourses(courseMap);\r\n        setSelectedContenus(contenuMap);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Erreur chargement Program du programme:\", err);\r\n      });\r\n  }, [programId, coursesByModule, contenusByCourse]);\r\n\r\n  const handleModuleToggle = async (moduleId) => {\r\n    const updated = selectedModules.includes(moduleId)\r\n      ? selectedModules.filter(id => id !== moduleId)\r\n      : [...selectedModules, moduleId];\r\n    setSelectedModules(updated);\r\n\r\n    if (!coursesByModule[moduleId]) {\r\n      const res = await axios.get(\"http://localhost:8000/courses\");\r\n      setCoursesByModule(prev => ({ ...prev, [moduleId]: res.data }));\r\n    }\r\n  };\r\n\r\n  const handleCourseToggle = async (moduleId, courseId) => {\r\n    const current = selectedCourses[moduleId] || [];\r\n    const updated = current.includes(courseId)\r\n      ? current.filter(id => id !== courseId)\r\n      : [...current, courseId];\r\n    setSelectedCourses(prev => ({ ...prev, [moduleId]: updated }));\r\n\r\n    if (!contenusByCourse[courseId]) {\r\n      const res = await axios.get(\"http://localhost:8000/contenus\");\r\n      setContenusByCourse(prev => ({ ...prev, [courseId]: res.data }));\r\n    }\r\n  };\r\n\r\n  const handleContenuToggle = (moduleId, courseId, contenuId) => {\r\n    const key = `${moduleId}-${courseId}`;\r\n    const current = selectedContenus[key] || [];\r\n    const updated = current.includes(contenuId)\r\n      ? current.filter(id => id !== contenuId)\r\n      : [...current, contenuId];\r\n    setSelectedContenus(prev => ({ ...prev, [key]: updated }));\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!buildProgramId || !programName || !level || selectedModules.length === 0) {\r\n      alert(\"Veuillez remplir tous les champs nécessaires.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 1. Update program name\r\n      await axios.patch(`http://localhost:8000/programs/${programId}`, {\r\n        name: programName,\r\n      });\r\n\r\n      // 2. Update buildProgram structure\r\n      const modulesPayload = selectedModules.map((moduleId) => {\r\n        const courseIds = selectedCourses[moduleId] || [];\r\n        return {\r\n          moduleId,\r\n          courses: courseIds.map((courseId) => {\r\n            const key = `${moduleId}-${courseId}`;\r\n            const contenuIds = selectedContenus[key] || [];\r\n            return {\r\n              courseId,\r\n              contenus: contenuIds.map((contenuId) => ({ contenuId }))\r\n            };\r\n          })\r\n        };\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append(\"level\", level);\r\n      formData.append(\"modules\", JSON.stringify(modulesPayload));\r\n\r\n      await axios.patch(`http://localhost:8000/buildProgram/${buildProgramId}`, formData, {\r\n        headers: { \"Content-Type\": \"multipart/form-data\" },\r\n      });\r\n\r\n      alert(\"✅ Programme modifié avec succès !\");\r\n      navigate(\"/programs\");\r\n    } catch (err) {\r\n      console.error(err);\r\n      alert(\"❌ Erreur lors de la modification du programme.\");\r\n    }\r\n  };\r\n\r\n\r\nconst filteredModules = modules.filter((mod) => {\r\n  if (searchScope === \"module\") {\r\n    return mod.name.toLowerCase().includes(search.toLowerCase());\r\n  }\r\n\r\n  if (searchScope === \"course\") {\r\n    const courses = coursesByModule[mod.id] || [];\r\n    return courses.some(course =>\r\n      course.title.toLowerCase().includes(search.toLowerCase())\r\n    );\r\n  }\r\n\r\n  if (searchScope === \"contenu\") {\r\n    const courses = coursesByModule[mod.id] || [];\r\n    return courses.some(course => {\r\n      const contenus = contenusByCourse[course.id] || [];\r\n      return contenus.some(c =>\r\n        c.title.toLowerCase().includes(search.toLowerCase())\r\n      );\r\n    });\r\n  }\r\n\r\n  return true;\r\n});\r\n\r\n\r\nreturn (\r\n  <Box sx={{ display: \"flex\", justifyContent: \"center\", mt: 6 }}>\r\n    <Paper sx={{ p: 4, width: \"100%\", maxWidth: 1000, borderRadius: 4 }}>\r\n      <Typography variant=\"h5\" align=\"center\" gutterBottom>\r\n        🛠️ Modifier un Programme\r\n      </Typography>\r\n\r\n      {/* 🖊️ Nom du programme */}\r\n      <TextField\r\n        fullWidth\r\n        label=\"Nom du programme\"\r\n        variant=\"outlined\"\r\n        value={programName}\r\n        onChange={(e) => setProgramName(e.target.value)}\r\n        sx={{ mb: 3 }}\r\n      />\r\n\r\n      {/* 🔍 Search + Filter Scope */}\r\n      <Box display=\"flex\" gap={2} mb={2}>\r\n        <TextField\r\n          fullWidth\r\n          variant=\"outlined\"\r\n          size=\"small\"\r\n          label={`🔍 Rechercher par ${searchScope}`}\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n        <TextField\r\n          select\r\n          label=\"Filtrer par\"\r\n          size=\"small\"\r\n          value={searchScope}\r\n          onChange={(e) => setSearchScope(e.target.value)}\r\n          sx={{ minWidth: 160 }}\r\n        >\r\n          <MenuItem value=\"module\">Module</MenuItem>\r\n          <MenuItem value=\"course\">Cours</MenuItem>\r\n          <MenuItem value=\"contenu\">Contenu</MenuItem>\r\n        </TextField>\r\n      </Box>\r\n\r\n      <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>\r\n        {steps.map((label) => (\r\n          <Step key={label}><StepLabel>{label}</StepLabel></Step>\r\n        ))}\r\n      </Stepper>\r\n\r\n      {/* 👣 Étape 1 : Modules */}\r\n      {activeStep === 0 && (\r\n        <>\r\n          <Typography variant=\"h6\">📦 Modules</Typography>\r\n          <FormGroup>\r\n            {filteredModules.map((m) => (\r\n              <FormControlLabel\r\n                key={m.id}\r\n                control={\r\n                  <Checkbox\r\n                    checked={selectedModules.includes(m.id)}\r\n                    onChange={() => handleModuleToggle(m.id)}\r\n                  />\r\n                }\r\n                label={m.name}\r\n              />\r\n            ))}\r\n          </FormGroup>\r\n\r\n          {selectedModules.map((moduleId) => (\r\n            <Accordion key={moduleId} sx={{ mt: 2 }} defaultExpanded>\r\n              <AccordionSummary expandIcon={<ExpandMoreIcon />}>\r\n                <Typography fontWeight=\"bold\">\r\n                  🧩 {modules.find(m => m.id === moduleId)?.name}\r\n                </Typography>\r\n              </AccordionSummary>\r\n              <AccordionDetails>\r\n                {(coursesByModule[moduleId] || []).map((course) => (\r\n                  <Box key={course.id} ml={2} mt={1}>\r\n                    <FormControlLabel\r\n                      control={\r\n                        <Checkbox\r\n                          checked={(selectedCourses[moduleId] || []).includes(course.id)}\r\n                          onChange={() => handleCourseToggle(moduleId, course.id)}\r\n                        />\r\n                      }\r\n                      label={course.title}\r\n                    />\r\n                    {(selectedCourses[moduleId] || []).includes(course.id) &&\r\n                      contenusByCourse[course.id]?.length > 0 && (\r\n                        <Box ml={4} mt={1}>\r\n                          <Typography variant=\"body2\" fontWeight=\"bold\">\r\n                            📄 Contenus :\r\n                          </Typography>\r\n                          <FormGroup>\r\n                            {contenusByCourse[course.id].map((ct) => (\r\n                              <FormControlLabel\r\n                                key={ct.id}\r\n                                control={\r\n                                  <Checkbox\r\n                                    checked={\r\n                                      (selectedContenus[`${moduleId}-${course.id}`] || []).includes(ct.id)\r\n                                    }\r\n                                    onChange={() =>\r\n                                      handleContenuToggle(moduleId, course.id, ct.id)\r\n                                    }\r\n                                  />\r\n                                }\r\n                                label={ct.title}\r\n                              />\r\n                            ))}\r\n                          </FormGroup>\r\n                        </Box>\r\n                    )}\r\n                  </Box>\r\n                ))}\r\n              </AccordionDetails>\r\n            </Accordion>\r\n          ))}\r\n\r\n          <Box mt={3} display=\"flex\" justifyContent=\"space-between\">\r\n            <Button variant=\"outlined\" color=\"error\" onClick={() => navigate(\"/programs\")}>\r\n              Annuler\r\n            </Button>\r\n            <Button variant=\"contained\" onClick={() => setActiveStep(1)}>\r\n              Suivant\r\n            </Button>\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* 👣 Étape 2 : Niveau */}\r\n      {activeStep === 1 && (\r\n        <>\r\n          <Typography variant=\"h6\">🎯 Niveau du programme</Typography>\r\n          <FormGroup>\r\n            {[\"Basique\", \"Intermédiaire\", \"Avancé\"].map((lvl) => (\r\n              <FormControlLabel\r\n                key={lvl}\r\n                control={\r\n                  <Checkbox\r\n                    checked={level === lvl}\r\n                    onChange={() => setLevel(lvl)}\r\n                  />\r\n                }\r\n                label={lvl}\r\n              />\r\n            ))}\r\n          </FormGroup>\r\n\r\n          <Box mt={3} display=\"flex\" justifyContent=\"space-between\">\r\n            <Button variant=\"outlined\" onClick={() => setActiveStep(0)}>\r\n              Retour\r\n            </Button>\r\n            <Box>\r\n              <Button\r\n                variant=\"outlined\"\r\n                color=\"error\"\r\n                sx={{ mr: 2 }}\r\n                onClick={() => navigate(\"/programs\")}\r\n              >\r\n                Annuler\r\n              </Button>\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={handleSubmit}\r\n              >\r\n                Enregistrer\r\n              </Button>\r\n            </Box>\r\n          </Box>\r\n        </>\r\n      )}\r\n    </Paper>\r\n  </Box>\r\n);\r\n};\r\n\r\nexport default EditProgramBuildView;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EACxCC,SAAS,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,QAC3D,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGzC,MAAMC,KAAK,GAAG,CAAC,4BAA4B,EAAE,qBAAqB,CAAC;AAEnE,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAU,CAAC,GAAGV,SAAS,CAAC,CAAC;EAEjC,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,QAAQ,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACdiB,KAAK,CAACqC,GAAG,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIrB,UAAU,CAACqB,GAAG,CAACC,IAAI,CAAC,CAAC;IAE5ExC,KAAK,CAACqC,GAAG,CAAC,8CAA8CzB,SAAS,EAAE,CAAC,CACjE0B,IAAI,CAAC,MAAOC,GAAG,IAAK;MACnB,MAAME,YAAY,GAAGF,GAAG,CAACC,IAAI;MAC7BR,iBAAiB,CAACS,YAAY,CAACC,EAAE,CAAC;MAClC1B,cAAc,CAACyB,YAAY,CAACE,OAAO,CAACC,IAAI,CAAC;MACzCd,QAAQ,CAACW,YAAY,CAACZ,KAAK,CAAC;MAE5B,MAAMgB,iBAAiB,GAAGJ,YAAY,CAACxB,OAAO,CAAC6B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,MAAM,CAACN,EAAE,CAAC;MACtElB,kBAAkB,CAACqB,iBAAiB,CAAC;MAErC,MAAMI,SAAS,GAAG,CAAC,CAAC;MACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;MAErB,KAAK,MAAMH,EAAE,IAAIN,YAAY,CAACxB,OAAO,EAAE;QACrC,MAAMkC,QAAQ,GAAGJ,EAAE,CAACC,MAAM,CAACN,EAAE;QAE7B,IAAI,CAACvB,eAAe,CAACgC,QAAQ,CAAC,EAAE;UAC9B,MAAMZ,GAAG,GAAG,MAAMvC,KAAK,CAACqC,GAAG,CAAC,+BAA+B,CAAC;UAC5DjB,kBAAkB,CAACgC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACD,QAAQ,GAAGZ,GAAG,CAACC;UAAK,CAAC,CAAC,CAAC;QACjE;QAEAS,SAAS,CAACE,QAAQ,CAAC,GAAGJ,EAAE,CAACM,OAAO,CAACP,GAAG,CAACQ,EAAE,IAAIA,EAAE,CAACC,MAAM,CAACb,EAAE,CAAC;QAExD,KAAK,MAAMY,EAAE,IAAIP,EAAE,CAACM,OAAO,EAAE;UAC3B,MAAMG,QAAQ,GAAGF,EAAE,CAACC,MAAM,CAACb,EAAE;UAC7B,MAAMe,GAAG,GAAG,GAAGN,QAAQ,IAAIK,QAAQ,EAAE;UACrC,IAAI,CAACnC,gBAAgB,CAACmC,QAAQ,CAAC,EAAE;YAC/B,MAAMjB,GAAG,GAAG,MAAMvC,KAAK,CAACqC,GAAG,CAAC,gCAAgC,CAAC;YAC7Df,mBAAmB,CAAC8B,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE,CAACI,QAAQ,GAAGjB,GAAG,CAACC;YAAK,CAAC,CAAC,CAAC;UAClE;UACAU,UAAU,CAACO,GAAG,CAAC,GAAGH,EAAE,CAACI,QAAQ,CAACZ,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAClB,EAAE,CAAC;QAC5D;MACF;MAEAhB,kBAAkB,CAACuB,SAAS,CAAC;MAC7BrB,mBAAmB,CAACsB,UAAU,CAAC;IACjC,CAAC,CAAC,CACDW,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEF,GAAG,CAAC;IAC/D,CAAC,CAAC;EACN,CAAC,EAAE,CAAClD,SAAS,EAAEO,eAAe,EAAEE,gBAAgB,CAAC,CAAC;EAElD,MAAM4C,kBAAkB,GAAG,MAAOd,QAAQ,IAAK;IAC7C,MAAMe,OAAO,GAAG3C,eAAe,CAAC4C,QAAQ,CAAChB,QAAQ,CAAC,GAC9C5B,eAAe,CAAC6C,MAAM,CAAC1B,EAAE,IAAIA,EAAE,KAAKS,QAAQ,CAAC,GAC7C,CAAC,GAAG5B,eAAe,EAAE4B,QAAQ,CAAC;IAClC3B,kBAAkB,CAAC0C,OAAO,CAAC;IAE3B,IAAI,CAAC/C,eAAe,CAACgC,QAAQ,CAAC,EAAE;MAC9B,MAAMZ,GAAG,GAAG,MAAMvC,KAAK,CAACqC,GAAG,CAAC,+BAA+B,CAAC;MAC5DjB,kBAAkB,CAACgC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,QAAQ,GAAGZ,GAAG,CAACC;MAAK,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAG,MAAAA,CAAOlB,QAAQ,EAAEK,QAAQ,KAAK;IACvD,MAAMc,OAAO,GAAG7C,eAAe,CAAC0B,QAAQ,CAAC,IAAI,EAAE;IAC/C,MAAMe,OAAO,GAAGI,OAAO,CAACH,QAAQ,CAACX,QAAQ,CAAC,GACtCc,OAAO,CAACF,MAAM,CAAC1B,EAAE,IAAIA,EAAE,KAAKc,QAAQ,CAAC,GACrC,CAAC,GAAGc,OAAO,EAAEd,QAAQ,CAAC;IAC1B9B,kBAAkB,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,QAAQ,GAAGe;IAAQ,CAAC,CAAC,CAAC;IAE9D,IAAI,CAAC7C,gBAAgB,CAACmC,QAAQ,CAAC,EAAE;MAC/B,MAAMjB,GAAG,GAAG,MAAMvC,KAAK,CAACqC,GAAG,CAAC,gCAAgC,CAAC;MAC7Df,mBAAmB,CAAC8B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACI,QAAQ,GAAGjB,GAAG,CAACC;MAAK,CAAC,CAAC,CAAC;IAClE;EACF,CAAC;EAED,MAAM+B,mBAAmB,GAAGA,CAACpB,QAAQ,EAAEK,QAAQ,EAAEgB,SAAS,KAAK;IAC7D,MAAMf,GAAG,GAAG,GAAGN,QAAQ,IAAIK,QAAQ,EAAE;IACrC,MAAMc,OAAO,GAAG3C,gBAAgB,CAAC8B,GAAG,CAAC,IAAI,EAAE;IAC3C,MAAMS,OAAO,GAAGI,OAAO,CAACH,QAAQ,CAACK,SAAS,CAAC,GACvCF,OAAO,CAACF,MAAM,CAAC1B,EAAE,IAAIA,EAAE,KAAK8B,SAAS,CAAC,GACtC,CAAC,GAAGF,OAAO,EAAEE,SAAS,CAAC;IAC3B5C,mBAAmB,CAACwB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACK,GAAG,GAAGS;IAAQ,CAAC,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1C,cAAc,IAAI,CAAChB,WAAW,IAAI,CAACc,KAAK,IAAIN,eAAe,CAACmD,MAAM,KAAK,CAAC,EAAE;MAC7EC,KAAK,CAAC,+CAA+C,CAAC;MACtD;IACF;IAEA,IAAI;MACF;MACA,MAAM3E,KAAK,CAAC4E,KAAK,CAAC,kCAAkChE,SAAS,EAAE,EAAE;QAC/DgC,IAAI,EAAE7B;MACR,CAAC,CAAC;;MAEF;MACA,MAAM8D,cAAc,GAAGtD,eAAe,CAACuB,GAAG,CAAEK,QAAQ,IAAK;QACvD,MAAM2B,SAAS,GAAGrD,eAAe,CAAC0B,QAAQ,CAAC,IAAI,EAAE;QACjD,OAAO;UACLA,QAAQ;UACRE,OAAO,EAAEyB,SAAS,CAAChC,GAAG,CAAEU,QAAQ,IAAK;YACnC,MAAMC,GAAG,GAAG,GAAGN,QAAQ,IAAIK,QAAQ,EAAE;YACrC,MAAMuB,UAAU,GAAGpD,gBAAgB,CAAC8B,GAAG,CAAC,IAAI,EAAE;YAC9C,OAAO;cACLD,QAAQ;cACRE,QAAQ,EAAEqB,UAAU,CAACjC,GAAG,CAAE0B,SAAS,KAAM;gBAAEA;cAAU,CAAC,CAAC;YACzD,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC,CAAC;MAEF,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAErD,KAAK,CAAC;MAC/BmD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACP,cAAc,CAAC,CAAC;MAE1D,MAAM7E,KAAK,CAAC4E,KAAK,CAAC,sCAAsC7C,cAAc,EAAE,EAAEiD,QAAQ,EAAE;QAClFK,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEFV,KAAK,CAAC,mCAAmC,CAAC;MAC1ChE,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,CAAC,OAAOmD,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;MAClBa,KAAK,CAAC,gDAAgD,CAAC;IACzD;EACF,CAAC;EAGH,MAAMW,eAAe,GAAGrE,OAAO,CAACmD,MAAM,CAAEmB,GAAG,IAAK;IAC9C,IAAIpD,WAAW,KAAK,QAAQ,EAAE;MAC5B,OAAOoD,GAAG,CAAC3C,IAAI,CAAC4C,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAClC,MAAM,CAACuD,WAAW,CAAC,CAAC,CAAC;IAC9D;IAEA,IAAIrD,WAAW,KAAK,QAAQ,EAAE;MAC5B,MAAMkB,OAAO,GAAGlC,eAAe,CAACoE,GAAG,CAAC7C,EAAE,CAAC,IAAI,EAAE;MAC7C,OAAOW,OAAO,CAACoC,IAAI,CAAClC,MAAM,IACxBA,MAAM,CAACmC,KAAK,CAACF,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAClC,MAAM,CAACuD,WAAW,CAAC,CAAC,CAC1D,CAAC;IACH;IAEA,IAAIrD,WAAW,KAAK,SAAS,EAAE;MAC7B,MAAMkB,OAAO,GAAGlC,eAAe,CAACoE,GAAG,CAAC7C,EAAE,CAAC,IAAI,EAAE;MAC7C,OAAOW,OAAO,CAACoC,IAAI,CAAClC,MAAM,IAAI;QAC5B,MAAMG,QAAQ,GAAGrC,gBAAgB,CAACkC,MAAM,CAACb,EAAE,CAAC,IAAI,EAAE;QAClD,OAAOgB,QAAQ,CAAC+B,IAAI,CAACE,CAAC,IACpBA,CAAC,CAACD,KAAK,CAACF,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAClC,MAAM,CAACuD,WAAW,CAAC,CAAC,CACrD,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAGF,oBACEnF,OAAA,CAACpB,GAAG;IAAC2G,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC5D3F,OAAA,CAAClB,KAAK;MAACyG,EAAE,EAAE;QAAEK,CAAC,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAClE3F,OAAA,CAACnB,UAAU;QAACmH,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,QAAQ;QAACC,YAAY;QAAAP,QAAA,EAAC;MAErD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbtG,OAAA,CAACV,SAAS;QACRiH,SAAS;QACTC,KAAK,EAAC,kBAAkB;QACxBR,OAAO,EAAC,UAAU;QAClBS,KAAK,EAAE/F,WAAY;QACnBgG,QAAQ,EAAGC,CAAC,IAAKhG,cAAc,CAACgG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAChDlB,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGFtG,OAAA,CAACpB,GAAG;QAAC4G,OAAO,EAAC,MAAM;QAACsB,GAAG,EAAE,CAAE;QAACD,EAAE,EAAE,CAAE;QAAAlB,QAAA,gBAChC3F,OAAA,CAACV,SAAS;UACRiH,SAAS;UACTP,OAAO,EAAC,UAAU;UAClBe,IAAI,EAAC,OAAO;UACZP,KAAK,EAAE,qBAAqB1E,WAAW,EAAG;UAC1C2E,KAAK,EAAE7E,MAAO;UACd8E,QAAQ,EAAGC,CAAC,IAAK9E,SAAS,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFtG,OAAA,CAACV,SAAS;UACR0H,MAAM;UACNR,KAAK,EAAC,aAAa;UACnBO,IAAI,EAAC,OAAO;UACZN,KAAK,EAAE3E,WAAY;UACnB4E,QAAQ,EAAGC,CAAC,IAAK5E,cAAc,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDlB,EAAE,EAAE;YAAE0B,QAAQ,EAAE;UAAI,CAAE;UAAAtB,QAAA,gBAEtB3F,OAAA,CAACF,QAAQ;YAAC2G,KAAK,EAAC,QAAQ;YAAAd,QAAA,EAAC;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC1CtG,OAAA,CAACF,QAAQ;YAAC2G,KAAK,EAAC,QAAQ;YAAAd,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACzCtG,OAAA,CAACF,QAAQ;YAAC2G,KAAK,EAAC,SAAS;YAAAd,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENtG,OAAA,CAACb,OAAO;QAACqB,UAAU,EAAEA,UAAW;QAAC0G,gBAAgB;QAAC3B,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,EAC7DxF,KAAK,CAACsC,GAAG,CAAE+D,KAAK,iBACfxG,OAAA,CAACZ,IAAI;UAAAuG,QAAA,eAAa3F,OAAA,CAACX,SAAS;YAAAsG,QAAA,EAAEa;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC,GAArCE,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAsC,CACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGT9F,UAAU,KAAK,CAAC,iBACfR,OAAA,CAAAE,SAAA;QAAAyF,QAAA,gBACE3F,OAAA,CAACnB,UAAU;UAACmH,OAAO,EAAC,IAAI;UAAAL,QAAA,EAAC;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChDtG,OAAA,CAACf,SAAS;UAAA0G,QAAA,EACPV,eAAe,CAACxC,GAAG,CAAE0E,CAAC,iBACrBnH,OAAA,CAACd,gBAAgB;YAEfkI,OAAO,eACLpH,OAAA,CAAChB,QAAQ;cACPqI,OAAO,EAAEnG,eAAe,CAAC4C,QAAQ,CAACqD,CAAC,CAAC9E,EAAE,CAAE;cACxCqE,QAAQ,EAAEA,CAAA,KAAM9C,kBAAkB,CAACuD,CAAC,CAAC9E,EAAE;YAAE;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACF;YACDE,KAAK,EAAEW,CAAC,CAAC5E;UAAK,GAPT4E,CAAC,CAAC9E,EAAE;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEXpF,eAAe,CAACuB,GAAG,CAAEK,QAAQ;UAAA,IAAAwE,aAAA;UAAA,oBAC5BtH,OAAA,CAACR,SAAS;YAAgB+F,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAC6B,eAAe;YAAA5B,QAAA,gBACtD3F,OAAA,CAACP,gBAAgB;cAAC+H,UAAU,eAAExH,OAAA,CAACT,cAAc;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAX,QAAA,eAC/C3F,OAAA,CAACnB,UAAU;gBAAC4I,UAAU,EAAC,MAAM;gBAAA9B,QAAA,GAAC,eACzB,GAAA2B,aAAA,GAAC1G,OAAO,CAAC8G,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAC9E,EAAE,KAAKS,QAAQ,CAAC,cAAAwE,aAAA,uBAApCA,aAAA,CAAsC/E,IAAI;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBtG,OAAA,CAACN,gBAAgB;cAAAiG,QAAA,EACd,CAAC7E,eAAe,CAACgC,QAAQ,CAAC,IAAI,EAAE,EAAEL,GAAG,CAAES,MAAM;gBAAA,IAAAyE,qBAAA;gBAAA,oBAC5C3H,OAAA,CAACpB,GAAG;kBAAiBgJ,EAAE,EAAE,CAAE;kBAAClC,EAAE,EAAE,CAAE;kBAAAC,QAAA,gBAChC3F,OAAA,CAACd,gBAAgB;oBACfkI,OAAO,eACLpH,OAAA,CAAChB,QAAQ;sBACPqI,OAAO,EAAE,CAACjG,eAAe,CAAC0B,QAAQ,CAAC,IAAI,EAAE,EAAEgB,QAAQ,CAACZ,MAAM,CAACb,EAAE,CAAE;sBAC/DqE,QAAQ,EAAEA,CAAA,KAAM1C,kBAAkB,CAAClB,QAAQ,EAAEI,MAAM,CAACb,EAAE;oBAAE;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CACF;oBACDE,KAAK,EAAEtD,MAAM,CAACmC;kBAAM;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,EACD,CAAClF,eAAe,CAAC0B,QAAQ,CAAC,IAAI,EAAE,EAAEgB,QAAQ,CAACZ,MAAM,CAACb,EAAE,CAAC,IACpD,EAAAsF,qBAAA,GAAA3G,gBAAgB,CAACkC,MAAM,CAACb,EAAE,CAAC,cAAAsF,qBAAA,uBAA3BA,qBAAA,CAA6BtD,MAAM,IAAG,CAAC,iBACrCrE,OAAA,CAACpB,GAAG;oBAACgJ,EAAE,EAAE,CAAE;oBAAClC,EAAE,EAAE,CAAE;oBAAAC,QAAA,gBAChB3F,OAAA,CAACnB,UAAU;sBAACmH,OAAO,EAAC,OAAO;sBAACyB,UAAU,EAAC,MAAM;sBAAA9B,QAAA,EAAC;oBAE9C;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbtG,OAAA,CAACf,SAAS;sBAAA0G,QAAA,EACP3E,gBAAgB,CAACkC,MAAM,CAACb,EAAE,CAAC,CAACI,GAAG,CAAEoF,EAAE,iBAClC7H,OAAA,CAACd,gBAAgB;wBAEfkI,OAAO,eACLpH,OAAA,CAAChB,QAAQ;0BACPqI,OAAO,EACL,CAAC/F,gBAAgB,CAAC,GAAGwB,QAAQ,IAAII,MAAM,CAACb,EAAE,EAAE,CAAC,IAAI,EAAE,EAAEyB,QAAQ,CAAC+D,EAAE,CAACxF,EAAE,CACpE;0BACDqE,QAAQ,EAAEA,CAAA,KACRxC,mBAAmB,CAACpB,QAAQ,EAAEI,MAAM,CAACb,EAAE,EAAEwF,EAAE,CAACxF,EAAE;wBAC/C;0BAAA8D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CACF;wBACDE,KAAK,EAAEqB,EAAE,CAACxC;sBAAM,GAXXwC,EAAE,CAACxF,EAAE;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAYX,CACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACR;gBAAA,GAnCOpD,MAAM,CAACb,EAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoCd,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA,GA9CLxD,QAAQ;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+Cb,CAAC;QAAA,CACb,CAAC,eAEFtG,OAAA,CAACpB,GAAG;UAAC8G,EAAE,EAAE,CAAE;UAACF,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAAAE,QAAA,gBACvD3F,OAAA,CAACjB,MAAM;YAACiH,OAAO,EAAC,UAAU;YAAC8B,KAAK,EAAC,OAAO;YAACC,OAAO,EAAEA,CAAA,KAAMzH,QAAQ,CAAC,WAAW,CAAE;YAAAqF,QAAA,EAAC;UAE/E;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtG,OAAA,CAACjB,MAAM;YAACiH,OAAO,EAAC,WAAW;YAAC+B,OAAO,EAAEA,CAAA,KAAMtH,aAAa,CAAC,CAAC,CAAE;YAAAkF,QAAA,EAAC;UAE7D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN,CACH,EAGA9F,UAAU,KAAK,CAAC,iBACfR,OAAA,CAAAE,SAAA;QAAAyF,QAAA,gBACE3F,OAAA,CAACnB,UAAU;UAACmH,OAAO,EAAC,IAAI;UAAAL,QAAA,EAAC;QAAsB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5DtG,OAAA,CAACf,SAAS;UAAA0G,QAAA,EACP,CAAC,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAClD,GAAG,CAAEuF,GAAG,iBAC9ChI,OAAA,CAACd,gBAAgB;YAEfkI,OAAO,eACLpH,OAAA,CAAChB,QAAQ;cACPqI,OAAO,EAAE7F,KAAK,KAAKwG,GAAI;cACvBtB,QAAQ,EAAEA,CAAA,KAAMjF,QAAQ,CAACuG,GAAG;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CACF;YACDE,KAAK,EAAEwB;UAAI,GAPNA,GAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQT,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZtG,OAAA,CAACpB,GAAG;UAAC8G,EAAE,EAAE,CAAE;UAACF,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAAAE,QAAA,gBACvD3F,OAAA,CAACjB,MAAM;YAACiH,OAAO,EAAC,UAAU;YAAC+B,OAAO,EAAEA,CAAA,KAAMtH,aAAa,CAAC,CAAC,CAAE;YAAAkF,QAAA,EAAC;UAE5D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtG,OAAA,CAACpB,GAAG;YAAA+G,QAAA,gBACF3F,OAAA,CAACjB,MAAM;cACLiH,OAAO,EAAC,UAAU;cAClB8B,KAAK,EAAC,OAAO;cACbvC,EAAE,EAAE;gBAAE0C,EAAE,EAAE;cAAE,CAAE;cACdF,OAAO,EAAEA,CAAA,KAAMzH,QAAQ,CAAC,WAAW,CAAE;cAAAqF,QAAA,EACtC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA,CAACjB,MAAM;cACLiH,OAAO,EAAC,WAAW;cACnB8B,KAAK,EAAC,SAAS;cACfC,OAAO,EAAE3D,YAAa;cAAAuB,QAAA,EACvB;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAER,CAAC;AAACjG,EAAA,CArVID,oBAAoB;EAAA,QACPR,WAAW,EACNC,SAAS;AAAA;AAAAqI,EAAA,GAF3B9H,oBAAoB;AAuV1B,eAAeA,oBAAoB;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}