{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\AnimerSeanceView.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Tabs, Tab, Paper, Chip, Button, Collapse, Stack, TextField, IconButton, Divider } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport ReactPlayer from \"react-player\";\nimport { Description as DescriptionIcon, Quiz as QuizIcon, Chat as ChatIcon, InsertDriveFile as InsertDriveFileIcon, AddPhotoAlternate as AddPhotoAlternateIcon, Movie as MovieIcon, Save as SaveIcon, ZoomInMap as ZoomInMapIcon, Feedback as FeedbackIcon } from \"@mui/icons-material\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport io from \"socket.io-client\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport { Avatar } from \"@mui/material\";\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport { useNavigate } from \"react-router-dom\";\nimport AddSeanceFeedback from './AddSeanceFeedback';\nimport FeedbackFormateur from '../../../components/FeedbackFormateur';\nimport SeanceFeedbackList from './seancefeedbacklist';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimerSeanceView = () => {\n  _s();\n  var _programDetails$progr;\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [seance, setSeance] = useState(null);\n  const [programDetails, setProgramDetails] = useState(null);\n  const [tab, setTab] = useState(0);\n  const [showContenus, setShowContenus] = useState(true);\n  const [sessionImages, setSessionImages] = useState([]);\n  const [sessionVideos, setSessionVideos] = useState([]);\n  const [zoomedImage, setZoomedImage] = useState(null);\n  const [expandedCourses, setExpandedCourses] = useState({});\n  const [sessionNotes, setSessionNotes] = useState(\"\");\n  const [saving, setSaving] = useState(false);\n  const [showFeedback, setShowFeedback] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [newMsg, setNewMsg] = useState(\"\");\n  const [showEmoji, setShowEmoji] = useState(false);\n  const [newFile, setNewFile] = useState(null);\n  const chatBottomRef = useRef();\n  const [socket, setSocket] = useState(null);\n  const user = JSON.parse(localStorage.getItem(\"user\"));\n  const navigate = useNavigate();\n\n  // Init socket.io\n  useEffect(() => {\n    const s = io(\"http://localhost:8000\");\n    setSocket(s);\n    s.emit(\"joinRoom\", {\n      seanceId: Number(seanceId)\n    });\n    s.on(\"newMessage\", msg => {\n      setChatMessages(prev => [...prev, msg]);\n    });\n    s.on(\"deleteMessage\", payload => {\n      setChatMessages(prev => prev.filter(m => m.id !== payload.id));\n    });\n    return () => {\n      s.disconnect();\n    };\n  }, [seanceId]);\n\n  // Load old messages\n  useEffect(() => {\n    if (!seanceId) return;\n    axios.get(`http://localhost:8000/chat-messages/${seanceId}`).then(res => setChatMessages(res.data)).catch(() => setChatMessages([]));\n  }, [seanceId]);\n\n  // Load seance data\n  useEffect(() => {\n    const fetchSeance = async () => {\n      try {\n        var _base$session;\n        const res = await axios.get(`http://localhost:8000/seance-formateur/${seanceId}`);\n        const base = res.data;\n        setSeance(base);\n        if (base !== null && base !== void 0 && (_base$session = base.session2) !== null && _base$session !== void 0 && _base$session.id) {\n          const detailRes = await axios.get(`http://localhost:8000/seance-formateur/details/${base.session2.id}`);\n          setProgramDetails(detailRes.data);\n        }\n      } catch (err) {\n        console.error(\"❌ Erreur chargement séance :\", err);\n      }\n    };\n    fetchSeance();\n  }, [seanceId]);\n\n  // Load media\n  useEffect(() => {\n    if (!seanceId) return;\n    axios.get(`http://localhost:8000/seance-formateur/${seanceId}/media`).then(res => {\n      setSessionImages(res.data.filter(m => m.type === \"IMAGE\"));\n      setSessionVideos(res.data.filter(m => m.type === \"VIDEO\"));\n    }).catch(err => {\n      console.error(\"Erreur chargement médias:\", err);\n    });\n  }, [seanceId]);\n  const uploadMedia = async (file, type) => {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    formData.append(\"type\", type);\n    const res = await axios.post(`http://localhost:8000/seance-formateur/${seanceId}/upload-media`, formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\"\n      }\n    });\n    return res.data;\n  };\n  const toggleCourseVisibility = courseId => {\n    setExpandedCourses(prev => ({\n      ...prev,\n      [courseId]: !prev[courseId]\n    }));\n  };\n  const handleTabChange = (e, newValue) => {\n    if (newValue !== 4 && newValue !== 5) setPrevTab(tab);\n    setTab(newValue);\n  };\n  const handleAddImage = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    try {\n      const media = await uploadMedia(file, \"IMAGE\");\n      setSessionImages(prev => [...prev, media]);\n    } catch (err) {\n      alert(t('uploadImageError'));\n    }\n  };\n  const handleAddVideo = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    try {\n      const media = await uploadMedia(file, \"VIDEO\");\n      setSessionVideos(prev => [...prev, media]);\n    } catch (err) {\n      alert(t('uploadVideoError'));\n    }\n  };\n  const handleSaveSession = async () => {\n    setSaving(true);\n    setTimeout(() => setSaving(false), 1000);\n    alert(t('seance.saveSuccess'));\n  };\n  const handlePublishContenu = async contenuId => {\n    if (!contenuId) return;\n    try {\n      await axios.patch(`http://localhost:8000/contenus/${contenuId}/publish`, {\n        published: true\n      });\n      const detailRes = await axios.get(`http://localhost:8000/seance-formateur/details/${seance.session2.id}`);\n      setProgramDetails(detailRes.data);\n    } catch {\n      alert(t('statusChangeError'));\n    }\n  };\n  const handleEmoji = e => {\n    setNewMsg(prev => prev + e.emoji);\n    setShowEmoji(false);\n  };\n  const handleChatSend = async () => {\n    if (!socket) return;\n    if (newFile) {\n      const formData = new FormData();\n      formData.append(\"file\", newFile);\n      formData.append(\"seanceId\", seanceId);\n      try {\n        const res = await axios.post(\"http://localhost:8000/chat-messages/upload-chat\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\"\n          }\n        });\n        socket.emit(\"sendMessage\", {\n          content: res.data.fileUrl,\n          type: res.data.fileType || \"file\",\n          seanceId: Number(seanceId),\n          senderId: user.id\n        });\n        setNewFile(null);\n      } catch {\n        alert(t('fileUploadError'));\n      }\n    } else if (newMsg.trim()) {\n      socket.emit(\"sendMessage\", {\n        content: newMsg,\n        type: \"text\",\n        seanceId: Number(seanceId),\n        senderId: user.id\n      });\n      setNewMsg(\"\");\n    }\n  };\n  const handleDeleteMsg = async msgId => {\n    try {\n      await axios.delete(`http://localhost:8000/chat-messages/${msgId}`, {\n        data: {\n          userId: user.id\n        }\n      });\n      setChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } catch (err) {\n      alert(t('seances.deleteMessageError'));\n    }\n  };\n  const renderProgramHierarchy = () => {\n    if (!programDetails) return /*#__PURE__*/_jsxDEV(Typography, {\n      children: t('loadingProgram')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 33\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        mb: 1,\n        children: [\"\\uD83D\\uDCD8 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [t('program'), \" : \", programDetails.program.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 44\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        ml: 2,\n        mt: 2,\n        children: programDetails.session2Modules.map(mod => /*#__PURE__*/_jsxDEV(Box, {\n          mt: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            fontWeight: \"bold\",\n            sx: {\n              color: \"#1976d2\"\n            },\n            children: [\"\\uD83D\\uDCE6 \", mod.module.title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            ml: 3,\n            children: mod.courses.map(course => /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                alignItems: \"center\",\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  fontWeight: \"bold\",\n                  sx: {\n                    color: \"#1e88e5\"\n                  },\n                  children: [\"\\uD83D\\uDCD8 \", course.course.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"outlined\",\n                  onClick: () => toggleCourseVisibility(course.id),\n                  children: expandedCourses[course.id] ? t('hide') : t('show')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n                in: expandedCourses[course.id],\n                children: course.contenus.map(ct => {\n                  var _ct$contenu$id, _ct$contenu, _ct$contenu3, _ct$contenu4, _ct$contenu6;\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    flexWrap: \"wrap\",\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(InsertDriveFileIcon, {\n                        sx: {\n                          fontSize: 22,\n                          color: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 35\n                      }, this),\n                      label: ct.contenu.title,\n                      variant: \"outlined\",\n                      onClick: () => {\n                        var _ct$contenu2;\n                        return ((_ct$contenu2 = ct.contenu) === null || _ct$contenu2 === void 0 ? void 0 : _ct$contenu2.fileUrl) && window.open(ct.contenu.fileUrl, \"_blank\");\n                      },\n                      sx: {\n                        cursor: (_ct$contenu3 = ct.contenu) !== null && _ct$contenu3 !== void 0 && _ct$contenu3.fileUrl ? \"pointer\" : \"default\",\n                        borderColor: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\",\n                        color: ct.contenu.published ? \"#2e7d32\" : \"#546e7a\",\n                        fontWeight: \"bold\",\n                        minWidth: 140,\n                        justifyContent: \"flex-start\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"small\",\n                      variant: \"outlined\",\n                      color: (_ct$contenu4 = ct.contenu) !== null && _ct$contenu4 !== void 0 && _ct$contenu4.published ? \"success\" : \"warning\",\n                      onClick: () => {\n                        var _ct$contenu5;\n                        return handlePublishContenu((_ct$contenu5 = ct.contenu) === null || _ct$contenu5 === void 0 ? void 0 : _ct$contenu5.id);\n                      },\n                      children: (_ct$contenu6 = ct.contenu) !== null && _ct$contenu6 !== void 0 && _ct$contenu6.published ? t('unpublish') : t('publish')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this)]\n                  }, (_ct$contenu$id = (_ct$contenu = ct.contenu) === null || _ct$contenu === void 0 ? void 0 : _ct$contenu.id) !== null && _ct$contenu$id !== void 0 ? _ct$contenu$id : uuidv4(), true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, course.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, mod.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this);\n  };\n  if (!seance) return /*#__PURE__*/_jsxDEV(Typography, {\n    children: t('loadingSession')\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 310,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 0,\n        background: \"#f8fafc\",\n        minHeight: \"70vh\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        border: \"2px solid #bcbcbc\",\n        overflow: \"hidden\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        src: `https://localhost:8443/${seance.title || \"default-room\"}`,\n        allow: \"camera; microphone; fullscreen; display-capture\",\n        style: {\n          width: \"100%\",\n          height: \"70vh\",\n          border: \"none\"\n        },\n        title: t('jitsiMeeting')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `${t('program')} : ${(programDetails === null || programDetails === void 0 ? void 0 : (_programDetails$progr = programDetails.program) === null || _programDetails$progr === void 0 ? void 0 : _programDetails$progr.title) || \"\"}`,\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(ZoomInMapIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 24\n          }, this),\n          onClick: () => setShowContenus(!showContenus),\n          variant: \"outlined\",\n          size: \"small\",\n          children: showContenus ? t('hideHierarchy') : t('showHierarchy')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 24\n          }, this),\n          onClick: () => setShowFeedback(!showFeedback),\n          variant: showFeedback ? \"outlined\" : \"contained\",\n          color: \"secondary\",\n          size: \"small\",\n          children: showFeedback ? t('hideFeedback') : t('showFeedback')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: showContenus,\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), renderProgramHierarchy()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      mt: 2,\n      children: [/*#__PURE__*/_jsxDEV(Tabs, {\n        orientation: \"vertical\",\n        value: tab,\n        onChange: handleTabChange,\n        sx: {\n          borderRight: 1,\n          borderColor: \"divider\",\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('sessionAdditions')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(QuizIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('quizComing')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(ChatIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('notesChat')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(InsertDriveFileIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('whiteboard'),\n          onClick: () => navigate(`/whiteboard/${seanceId}`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedbackFormateur')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), showFeedback && /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 24\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedback')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 22\n          }, this),\n          iconPosition: \"start\",\n          label: t('feedbackList')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        flex: 1,\n        pl: 3,\n        children: [tab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 1,\n            children: [t('sessionImages'), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              component: \"label\",\n              children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                hidden: true,\n                onChange: handleAddImage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            flexWrap: \"wrap\",\n            children: sessionImages.map(img => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: img.fileUrl,\n              alt: \"\",\n              style: {\n                maxHeight: 100,\n                margin: 2,\n                cursor: \"pointer\",\n                borderRadius: 8,\n                boxShadow: \"0 1px 6px #bbb\"\n              },\n              onClick: () => setZoomedImage(img.fileUrl)\n            }, img.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 2,\n            children: [t('sessionVideos'), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              component: \"label\",\n              children: [/*#__PURE__*/_jsxDEV(MovieIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"video/*\",\n                hidden: true,\n                onChange: handleAddVideo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            flexWrap: \"wrap\",\n            children: sessionVideos.map(vid => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 180\n              },\n              children: /*#__PURE__*/_jsxDEV(ReactPlayer, {\n                url: vid.fileUrl,\n                controls: true,\n                width: \"100%\",\n                height: 100\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this)\n            }, vid.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 2,\n            children: t('sessionNotes')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            multiline: true,\n            minRows: 3,\n            placeholder: t('notesPlaceholder'),\n            value: sessionNotes,\n            onChange: e => setSessionNotes(e.target.value),\n            sx: {\n              my: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 34\n            }, this),\n            variant: \"contained\",\n            onClick: handleSaveSession,\n            disabled: saving,\n            children: saving ? t('saving') : t('saveSession')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), tab === 1 && /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          children: [\"\\uD83E\\uDDEA \", t('quizFeature')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), tab === 2 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mb: 1,\n            children: [\"\\uD83D\\uDCAC \", t('sessionChat')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              maxHeight: 320,\n              minHeight: 150,\n              overflowY: \"auto\",\n              border: \"1px solid #ccc\",\n              borderRadius: 2,\n              background: \"#f9f9f9\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [chatMessages.map((msg, i) => {\n                var _msg$sender, _msg$sender2, _msg$sender2$profileP, _msg$sender3, _msg$sender4, _msg$sender5, _msg$sender5$name, _msg$sender5$name$, _msg$sender6, _msg$sender7, _msg$sender8;\n                return /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    p: 1,\n                    background: \"#fff\",\n                    display: \"flex\",\n                    alignItems: \"flex-start\",\n                    mb: 1,\n                    gap: 1\n                  },\n                  children: [(_msg$sender = msg.sender) !== null && _msg$sender !== void 0 && _msg$sender.profilePic ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: (_msg$sender2 = msg.sender) !== null && _msg$sender2 !== void 0 && (_msg$sender2$profileP = _msg$sender2.profilePic) !== null && _msg$sender2$profileP !== void 0 && _msg$sender2$profileP.startsWith('http') ? msg.sender.profilePic : `http://localhost:8000${((_msg$sender3 = msg.sender) === null || _msg$sender3 === void 0 ? void 0 : _msg$sender3.profilePic) || '/profile-pics/default.png'}`,\n                    alt: (_msg$sender4 = msg.sender) === null || _msg$sender4 === void 0 ? void 0 : _msg$sender4.name,\n                    style: {\n                      width: 32,\n                      height: 32,\n                      borderRadius: \"50%\",\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      width: 32,\n                      height: 32,\n                      marginRight: 1\n                    },\n                    children: ((_msg$sender5 = msg.sender) === null || _msg$sender5 === void 0 ? void 0 : (_msg$sender5$name = _msg$sender5.name) === null || _msg$sender5$name === void 0 ? void 0 : (_msg$sender5$name$ = _msg$sender5$name[0]) === null || _msg$sender5$name$ === void 0 ? void 0 : _msg$sender5$name$.toUpperCase()) || \"?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      color: \"primary\",\n                      children: [((_msg$sender6 = msg.sender) === null || _msg$sender6 === void 0 ? void 0 : _msg$sender6.name) || t('anonymous'), ((_msg$sender7 = msg.sender) === null || _msg$sender7 === void 0 ? void 0 : _msg$sender7.role) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: \"#888\",\n                          fontWeight: 400,\n                          marginLeft: 8,\n                          fontSize: 13\n                        },\n                        children: [\"\\xB7 \", msg.sender.role]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 29\n                      }, this), msg.createdAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: \"#888\",\n                          fontSize: 11,\n                          marginLeft: 8\n                        },\n                        children: new Date(msg.createdAt).toLocaleTimeString([], {\n                          hour: \"2-digit\",\n                          minute: \"2-digit\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 475,\n                        columnNumber: 29\n                      }, this), ((_msg$sender8 = msg.sender) === null || _msg$sender8 === void 0 ? void 0 : _msg$sender8.id) === user.id && /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleDeleteMsg(msg.id),\n                        color: \"error\",\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 480,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 479,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 25\n                    }, this), msg.type === \"text\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 49\n                    }, this), msg.type === \"image\" && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: msg.content,\n                      alt: \"img\",\n                      style: {\n                        maxWidth: 180,\n                        borderRadius: 6,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 27\n                    }, this), msg.type === \"audio\" && /*#__PURE__*/_jsxDEV(\"audio\", {\n                      controls: true,\n                      src: msg.content,\n                      style: {\n                        maxWidth: 180,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 27\n                    }, this), msg.type === \"video\" && /*#__PURE__*/_jsxDEV(\"video\", {\n                      controls: true,\n                      src: msg.content,\n                      style: {\n                        maxWidth: 180,\n                        borderRadius: 6,\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 27\n                    }, this), msg.type === \"file\" && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: msg.content,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      style: {\n                        display: \"block\",\n                        marginTop: 4\n                      },\n                      children: [\"\\uD83D\\uDCCE \", msg.content.split(\"/\").pop()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this)]\n                }, i, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: chatBottomRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              value: newMsg,\n              size: \"small\",\n              placeholder: t('writeMessage'),\n              onChange: e => setNewMsg(e.target.value),\n              onKeyDown: e => e.key === \"Enter\" && handleChatSend(),\n              sx: {\n                background: \"#fff\",\n                borderRadius: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setShowEmoji(v => !v),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                role: \"img\",\n                \"aria-label\": \"emoji\",\n                children: \"\\uD83D\\uDE00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              component: \"label\",\n              color: newFile ? \"success\" : \"primary\",\n              children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                hidden: true,\n                type: \"file\",\n                accept: \"image/*,video/*,audio/*,application/pdf\",\n                onChange: e => setNewFile(e.target.files[0])\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleChatSend,\n              variant: \"contained\",\n              disabled: !newMsg.trim() && !newFile,\n              children: t('send')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), showEmoji && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: \"absolute\",\n              zIndex: 11\n            },\n            children: /*#__PURE__*/_jsxDEV(EmojiPicker, {\n              onEmojiClick: handleEmoji,\n              autoFocusSearch: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 17\n          }, this), newFile && /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"primary\",\n            fontSize: 12,\n            ml: 1,\n            mt: 0.5,\n            children: [t('fileReady'), \": \", newFile.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this), tab === 4 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mb: 2,\n            children: t('feedbackFormateur')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FeedbackFormateur, {\n            seanceId: seanceId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 13\n        }, this), showFeedback && tab === 5 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            mb: 2,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"\\uD83D\\uDCDD \", t('sessionFeedback')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AddSeanceFeedback, {\n            seanceId: seanceId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 13\n        }, this), tab === 6 && /*#__PURE__*/_jsxDEV(SeanceFeedbackList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), zoomedImage && /*#__PURE__*/_jsxDEV(Box, {\n      onClick: () => setZoomedImage(null),\n      sx: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        zIndex: 2000,\n        width: \"100vw\",\n        height: \"100vh\",\n        background: \"rgba(0,0,0,0.88)\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        cursor: \"zoom-out\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: zoomedImage,\n        alt: \"\",\n        style: {\n          maxWidth: \"92vw\",\n          maxHeight: \"92vh\",\n          borderRadius: 12,\n          boxShadow: \"0 2px 24px #111\"\n        },\n        onClick: e => e.stopPropagation()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n};\n_s(AnimerSeanceView, \"aMd0bjy+zFTcs8+4gksVAgzokCU=\", false, function () {\n  return [useTranslation, useParams, useNavigate];\n});\n_c = AnimerSeanceView;\nexport default AnimerSeanceView;\nvar _c;\n$RefreshReg$(_c, \"AnimerSeanceView\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useParams", "Box", "Typography", "Tabs", "Tab", "Paper", "Chip", "<PERSON><PERSON>", "Collapse", "<PERSON><PERSON>", "TextField", "IconButton", "Divider", "useTranslation", "axios", "ReactPlayer", "Description", "DescriptionIcon", "Quiz", "QuizIcon", "Cha<PERSON>", "ChatIcon", "InsertDriveFile", "InsertDriveFileIcon", "AddPhotoAlternate", "AddPhotoAlternateIcon", "Movie", "MovieIcon", "Save", "SaveIcon", "ZoomInMap", "ZoomInMapIcon", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "v4", "uuidv4", "io", "EmojiPicker", "Avatar", "DeleteIcon", "useNavigate", "AddSeanceFeedback", "FeedbackFormateur", "SeanceFeedbackList", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "_programDetails$progr", "t", "id", "seanceId", "seance", "setSeance", "programDetails", "setProgramDetails", "tab", "setTab", "showContenus", "setShowContenus", "sessionImages", "setSessionImages", "sessionVideos", "setSessionVideos", "zoomedImage", "setZoomedImage", "expandedCourses", "setExpandedCourses", "sessionNotes", "setSessionNotes", "saving", "setSaving", "showFeedback", "setShowFeedback", "chatMessages", "setChatMessages", "newMsg", "setNewMsg", "showE<PERSON>ji", "setShowEmoji", "newFile", "setNewFile", "chatBottomRef", "socket", "setSocket", "user", "JSON", "parse", "localStorage", "getItem", "navigate", "s", "emit", "Number", "on", "msg", "prev", "payload", "filter", "m", "disconnect", "get", "then", "res", "data", "catch", "fetchSeance", "_base$session", "base", "session2", "detailRes", "err", "console", "error", "type", "uploadMedia", "file", "formData", "FormData", "append", "post", "headers", "toggleCourseVisibility", "courseId", "handleTabChange", "e", "newValue", "setPrevTab", "handleAddImage", "target", "files", "media", "alert", "handleAddVideo", "handleSaveSession", "setTimeout", "handlePublishContenu", "contenuId", "patch", "published", "handleEmoji", "emoji", "handleChatSend", "content", "fileUrl", "fileType", "senderId", "trim", "handleDeleteMsg", "msgId", "delete", "userId", "renderProgramHierarchy", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mb", "program", "title", "ml", "mt", "session2Modules", "map", "mod", "fontWeight", "sx", "color", "module", "courses", "course", "direction", "alignItems", "spacing", "size", "onClick", "in", "contenus", "ct", "_ct$contenu$id", "_ct$contenu", "_ct$contenu3", "_ct$contenu4", "_ct$contenu6", "display", "gap", "flexWrap", "icon", "fontSize", "contenu", "label", "_ct$contenu2", "window", "open", "cursor", "borderColor", "min<PERSON><PERSON><PERSON>", "justifyContent", "_ct$contenu5", "p", "background", "minHeight", "border", "overflow", "src", "allow", "style", "width", "height", "startIcon", "my", "orientation", "value", "onChange", "borderRight", "iconPosition", "flex", "pl", "component", "accept", "hidden", "img", "alt", "maxHeight", "margin", "borderRadius", "boxShadow", "vid", "url", "controls", "fullWidth", "multiline", "minRows", "placeholder", "disabled", "overflowY", "i", "_msg$sender", "_msg$sender2", "_msg$sender2$profileP", "_msg$sender3", "_msg$sender4", "_msg$sender5", "_msg$sender5$name", "_msg$sender5$name$", "_msg$sender6", "_msg$sender7", "_msg$sender8", "sender", "profilePic", "startsWith", "name", "marginRight", "toUpperCase", "role", "marginLeft", "createdAt", "Date", "toLocaleTimeString", "hour", "minute", "max<PERSON><PERSON><PERSON>", "marginTop", "href", "rel", "split", "pop", "ref", "onKeyDown", "key", "v", "position", "zIndex", "onEmojiClick", "autoFocusSearch", "top", "left", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/AnimerSeanceView.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Paper,\r\n  Chip,\r\n  Button,\r\n  Collapse,\r\n  Stack,\r\n  TextField,\r\n  IconButton,\r\n  Divider,\r\n} from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport ReactPlayer from \"react-player\";\r\nimport {\r\n  Description as DescriptionIcon,\r\n  Quiz as QuizIcon,\r\n  Chat as ChatIcon,\r\n  InsertDriveFile as InsertDriveFileIcon,\r\n  AddPhotoAlternate as AddPhotoAlternateIcon,\r\n  Movie as MovieIcon,\r\n  Save as SaveIcon,\r\n  ZoomInMap as ZoomInMapIcon,\r\n  Feedback as FeedbackIcon,\r\n} from \"@mui/icons-material\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport io from \"socket.io-client\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport { Avatar } from \"@mui/material\";\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AddSeanceFeedback from './AddSeanceFeedback';\r\nimport FeedbackFormateur from '../../../components/FeedbackFormateur';\r\nimport SeanceFeedbackList from './seancefeedbacklist';\r\n\r\nconst AnimerSeanceView = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [seance, setSeance] = useState(null);\r\n  const [programDetails, setProgramDetails] = useState(null);\r\n  const [tab, setTab] = useState(0);\r\n  const [showContenus, setShowContenus] = useState(true);\r\n  const [sessionImages, setSessionImages] = useState([]);\r\n  const [sessionVideos, setSessionVideos] = useState([]);\r\n  const [zoomedImage, setZoomedImage] = useState(null);\r\n  const [expandedCourses, setExpandedCourses] = useState({});\r\n  const [sessionNotes, setSessionNotes] = useState(\"\");\r\n  const [saving, setSaving] = useState(false);\r\n  const [showFeedback, setShowFeedback] = useState(false);\r\n  const [chatMessages, setChatMessages] = useState([]);\r\n  const [newMsg, setNewMsg] = useState(\"\");\r\n  const [showEmoji, setShowEmoji] = useState(false);\r\n  const [newFile, setNewFile] = useState(null);\r\n  const chatBottomRef = useRef();\r\n  const [socket, setSocket] = useState(null);\r\n  const user = JSON.parse(localStorage.getItem(\"user\"));\r\n  const navigate = useNavigate();\r\n\r\n  // Init socket.io\r\n  useEffect(() => {\r\n    const s = io(\"http://localhost:8000\");\r\n    setSocket(s);\r\n    s.emit(\"joinRoom\", { seanceId: Number(seanceId) });\r\n\r\n    s.on(\"newMessage\", (msg) => {\r\n      setChatMessages((prev) => [...prev, msg]);\r\n    });\r\n\r\n    s.on(\"deleteMessage\", (payload) => {\r\n      setChatMessages((prev) => prev.filter((m) => m.id !== payload.id));\r\n    });\r\n\r\n    return () => {\r\n      s.disconnect();\r\n    };\r\n  }, [seanceId]);\r\n\r\n  // Load old messages\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    axios.get(`http://localhost:8000/chat-messages/${seanceId}`)\r\n      .then((res) => setChatMessages(res.data))\r\n      .catch(() => setChatMessages([]));\r\n  }, [seanceId]);\r\n\r\n  // Load seance data\r\n  useEffect(() => {\r\n    const fetchSeance = async () => {\r\n      try {\r\n        const res = await axios.get(`http://localhost:8000/seance-formateur/${seanceId}`);\r\n        const base = res.data;\r\n        setSeance(base);\r\n\r\n        if (base?.session2?.id) {\r\n          const detailRes = await axios.get(\r\n            `http://localhost:8000/seance-formateur/details/${base.session2.id}`\r\n          );\r\n          setProgramDetails(detailRes.data);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"❌ Erreur chargement séance :\", err);\r\n      }\r\n    };\r\n    fetchSeance();\r\n  }, [seanceId]);\r\n\r\n  // Load media\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    axios.get(`http://localhost:8000/seance-formateur/${seanceId}/media`)\r\n      .then(res => {\r\n        setSessionImages(res.data.filter(m => m.type === \"IMAGE\"));\r\n        setSessionVideos(res.data.filter(m => m.type === \"VIDEO\"));\r\n      })\r\n      .catch(err => {\r\n        console.error(\"Erreur chargement médias:\", err);\r\n      });\r\n  }, [seanceId]);\r\n\r\n  const uploadMedia = async (file, type) => {\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n    formData.append(\"type\", type);\r\n    const res = await axios.post(\r\n      `http://localhost:8000/seance-formateur/${seanceId}/upload-media`,\r\n      formData,\r\n      { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n    );\r\n    return res.data;\r\n  };\r\n\r\n  const toggleCourseVisibility = (courseId) => {\r\n    setExpandedCourses((prev) => ({\r\n      ...prev,\r\n      [courseId]: !prev[courseId],\r\n    }));\r\n  };\r\n\r\n  const handleTabChange = (e, newValue) => {\r\n    if (newValue !== 4 && newValue !== 5) setPrevTab(tab);\r\n    setTab(newValue);\r\n  };\r\n\r\n  const handleAddImage = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    try {\r\n      const media = await uploadMedia(file, \"IMAGE\");\r\n      setSessionImages((prev) => [...prev, media]);\r\n    } catch (err) {\r\n      alert(t('uploadImageError'));\r\n    }\r\n  };\r\n\r\n  const handleAddVideo = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    try {\r\n      const media = await uploadMedia(file, \"VIDEO\");\r\n      setSessionVideos((prev) => [...prev, media]);\r\n    } catch (err) {\r\n      alert(t('uploadVideoError'));\r\n    }\r\n  };\r\n\r\n  const handleSaveSession = async () => {\r\n    setSaving(true);\r\n    setTimeout(() => setSaving(false), 1000);\r\n    alert(t('seance.saveSuccess'));\r\n  };\r\n\r\n  const handlePublishContenu = async (contenuId) => {\r\n    if (!contenuId) return;\r\n    try {\r\n      await axios.patch(`http://localhost:8000/contenus/${contenuId}/publish`, {\r\n        published: true,\r\n      });\r\n      const detailRes = await axios.get(\r\n        `http://localhost:8000/seance-formateur/details/${seance.session2.id}`\r\n      );\r\n      setProgramDetails(detailRes.data);\r\n    } catch {\r\n      alert(t('statusChangeError'));\r\n    }\r\n  };\r\n\r\n  const handleEmoji = (e) => {\r\n    setNewMsg((prev) => prev + e.emoji);\r\n    setShowEmoji(false);\r\n  };\r\n\r\n  const handleChatSend = async () => {\r\n    if (!socket) return;\r\n    if (newFile) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", newFile);\r\n      formData.append(\"seanceId\", seanceId);\r\n      try {\r\n        const res = await axios.post(\"http://localhost:8000/chat-messages/upload-chat\", formData, {\r\n          headers: { \"Content-Type\": \"multipart/form-data\" },\r\n        });\r\n\r\n        socket.emit(\"sendMessage\", {\r\n          content: res.data.fileUrl,\r\n          type: res.data.fileType || \"file\",\r\n          seanceId: Number(seanceId),\r\n          senderId: user.id,\r\n        });\r\n\r\n        setNewFile(null);\r\n      } catch {\r\n        alert(t('fileUploadError'));\r\n      }\r\n    } else if (newMsg.trim()) {\r\n      socket.emit(\"sendMessage\", {\r\n        content: newMsg,\r\n        type: \"text\",\r\n        seanceId: Number(seanceId),\r\n        senderId: user.id,\r\n      });\r\n\r\n      setNewMsg(\"\");\r\n    }\r\n  };\r\n\r\n  const handleDeleteMsg = async (msgId) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/chat-messages/${msgId}`, {\r\n        data: { userId: user.id },\r\n      });\r\n      setChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } catch (err) {\r\n      alert(t('seances.deleteMessageError'));\r\n    }\r\n  };\r\n\r\n  const renderProgramHierarchy = () => {\r\n    if (!programDetails) return <Typography>{t('loadingProgram')}</Typography>;\r\n\r\n    return (\r\n      <Box>\r\n        <Typography variant=\"h6\" mb={1}>📘 <strong>{t('program')} : {programDetails.program.title}</strong></Typography>\r\n\r\n        <Box ml={2} mt={2}>\r\n          {programDetails.session2Modules.map((mod) => (\r\n            <Box key={mod.id} mt={2}>\r\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" sx={{ color: \"#1976d2\" }}>\r\n                📦 {mod.module.title}\r\n              </Typography>\r\n\r\n              <Box ml={3}>\r\n                {mod.courses.map((course) => (\r\n                  <Box key={course.id} mt={1}>\r\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\r\n                      <Typography variant=\"body1\" fontWeight=\"bold\" sx={{ color: \"#1e88e5\" }}>\r\n                        📘 {course.course.title}\r\n                      </Typography>\r\n                      <Button\r\n                        size=\"small\"\r\n                        variant=\"outlined\"\r\n                        onClick={() => toggleCourseVisibility(course.id)}\r\n                      >\r\n                        {expandedCourses[course.id] ? t('hide') : t('show')}\r\n                      </Button>\r\n                    </Stack>\r\n\r\n                    <Collapse in={expandedCourses[course.id]}>\r\n                      {course.contenus.map((ct) => (\r\n                        <Box key={ct.contenu?.id ?? uuidv4()} display=\"flex\" alignItems=\"center\" gap={1} flexWrap=\"wrap\" mt={1}>\r\n                          <Chip\r\n                            icon={<InsertDriveFileIcon sx={{ fontSize: 22, color: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\" }} />}\r\n                            label={ct.contenu.title}\r\n                            variant=\"outlined\"\r\n                            onClick={() => ct.contenu?.fileUrl && window.open(ct.contenu.fileUrl, \"_blank\")}\r\n                            sx={{\r\n                              cursor: ct.contenu?.fileUrl ? \"pointer\" : \"default\",\r\n                              borderColor: ct.contenu.published ? \"#4caf50\" : \"#b0bec5\",\r\n                              color: ct.contenu.published ? \"#2e7d32\" : \"#546e7a\",\r\n                              fontWeight: \"bold\",\r\n                              minWidth: 140,\r\n                              justifyContent: \"flex-start\",\r\n                            }}\r\n                          />\r\n                          <Button\r\n                            size=\"small\"\r\n                            variant=\"outlined\"\r\n                            color={ct.contenu?.published ? \"success\" : \"warning\"}\r\n                            onClick={() => handlePublishContenu(ct.contenu?.id)}\r\n                          >\r\n                            {ct.contenu?.published ? t('unpublish') : t('publish')}\r\n                          </Button>\r\n                        </Box>\r\n                      ))}\r\n                    </Collapse>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Box>\r\n          ))}\r\n        </Box>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  if (!seance) return <Typography>{t('loadingSession')}</Typography>;\r\n\r\n  return (\r\n    <Box p={2}>\r\n      {/* Meet */}\r\n      <Paper sx={{\r\n        mb: 3, p: 0, background: \"#f8fafc\", minHeight: \"70vh\",\r\n        display: \"flex\", alignItems: \"center\", justifyContent: \"center\",\r\n        border: \"2px solid #bcbcbc\", overflow: \"hidden\",\r\n      }}>\r\n        <iframe\r\n          src={`https://localhost:8443/${seance.title || \"default-room\"}`}\r\n          allow=\"camera; microphone; fullscreen; display-capture\"\r\n          style={{ width: \"100%\", height: \"70vh\", border: \"none\" }}\r\n          title={t('jitsiMeeting')}\r\n        />\r\n      </Paper>\r\n\r\n      {/* Programme */}\r\n      <Paper sx={{ p: 2, mb: 2 }}>\r\n        <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n          <Chip label={`${t('program')} : ${programDetails?.program?.title || \"\"}`} color=\"info\" />\r\n          <Button\r\n            startIcon={<ZoomInMapIcon />}\r\n            onClick={() => setShowContenus(!showContenus)}\r\n            variant=\"outlined\"\r\n            size=\"small\"\r\n          >\r\n            {showContenus ? t('hideHierarchy') : t('showHierarchy')}\r\n          </Button>\r\n          <Button\r\n            startIcon={<FeedbackIcon />}\r\n            onClick={() => setShowFeedback(!showFeedback)}\r\n            variant={showFeedback ? \"outlined\" : \"contained\"}\r\n            color=\"secondary\"\r\n            size=\"small\"\r\n          >\r\n            {showFeedback ? t('hideFeedback') : t('showFeedback')}\r\n          </Button>\r\n        </Stack>\r\n        <Collapse in={showContenus}>\r\n          <Divider sx={{ my: 2 }} />\r\n          {renderProgramHierarchy()}\r\n        </Collapse>\r\n      </Paper>\r\n\r\n      {/* Tabs */}\r\n      <Box display=\"flex\" mt={2}>\r\n        <Tabs orientation=\"vertical\" value={tab} onChange={handleTabChange} sx={{ borderRight: 1, borderColor: \"divider\", minWidth: 180 }}>\r\n          <Tab icon={<DescriptionIcon />} iconPosition=\"start\" label={t('sessionAdditions')} />\r\n          <Tab icon={<QuizIcon />} iconPosition=\"start\" label={t('quizComing')} />\r\n          <Tab icon={<ChatIcon />} iconPosition=\"start\" label={t('notesChat')} />\r\n          <Tab icon={<InsertDriveFileIcon />} iconPosition=\"start\" label={t('whiteboard')} onClick={() => navigate(`/whiteboard/${seanceId}`)} />\r\n          <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedbackFormateur')} />\r\n          {showFeedback && (\r\n            <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedback')} />\r\n          )}\r\n          <Tab icon={<FeedbackIcon />} iconPosition=\"start\" label={t('feedbackList')} />\r\n        </Tabs>\r\n\r\n        <Box flex={1} pl={3}>\r\n          {/* Onglet 1 - Session Additions */}\r\n          {tab === 0 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mt={1}>\r\n                {t('sessionImages')}\r\n                <IconButton color=\"primary\" component=\"label\">\r\n                  <AddPhotoAlternateIcon />\r\n                  <input type=\"file\" accept=\"image/*\" hidden onChange={handleAddImage} />\r\n                </IconButton>\r\n              </Typography>\r\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\r\n                {sessionImages.map((img) => (\r\n                  <img\r\n                    key={img.id}\r\n                    src={img.fileUrl}\r\n                    alt=\"\"\r\n                    style={{ maxHeight: 100, margin: 2, cursor: \"pointer\", borderRadius: 8, boxShadow: \"0 1px 6px #bbb\" }}\r\n                    onClick={() => setZoomedImage(img.fileUrl)}\r\n                  />\r\n                ))}\r\n              </Stack>\r\n\r\n              <Typography variant=\"h6\" mt={2}>\r\n                {t('sessionVideos')}\r\n                <IconButton color=\"primary\" component=\"label\">\r\n                  <MovieIcon />\r\n                  <input type=\"file\" accept=\"video/*\" hidden onChange={handleAddVideo} />\r\n                </IconButton>\r\n              </Typography>\r\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\r\n                {sessionVideos.map((vid) => (\r\n                  <Box key={vid.id} sx={{ width: 180 }}>\r\n                    <ReactPlayer url={vid.fileUrl} controls width=\"100%\" height={100} />\r\n                  </Box>\r\n                ))}\r\n              </Stack>\r\n\r\n              <Typography variant=\"h6\" mt={2}>{t('sessionNotes')}</Typography>\r\n              <TextField\r\n                fullWidth multiline minRows={3}\r\n                placeholder={t('notesPlaceholder')}\r\n                value={sessionNotes}\r\n                onChange={(e) => setSessionNotes(e.target.value)}\r\n                sx={{ my: 1 }}\r\n              />\r\n              <Button startIcon={<SaveIcon />} variant=\"contained\" onClick={handleSaveSession} disabled={saving}>\r\n                {saving ? t('saving') : t('saveSession')}\r\n              </Button>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 2 - Quiz */}\r\n          {tab === 1 && (\r\n            <Typography color=\"text.secondary\">🧪 {t('quizFeature')}</Typography>\r\n          )}\r\n\r\n          {/* Onglet 3 - Chat */}\r\n          {tab === 2 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mb={1}>💬 {t('sessionChat')}</Typography>\r\n              <Paper sx={{\r\n                p: 2, mb: 2, maxHeight: 320, minHeight: 150, overflowY: \"auto\",\r\n                border: \"1px solid #ccc\", borderRadius: 2, background: \"#f9f9f9\"\r\n              }}>\r\n                <Stack spacing={1}>\r\n                  {chatMessages.map((msg, i) => (\r\n                    <Paper\r\n                      key={i}\r\n                      sx={{\r\n                        p: 1,\r\n                        background: \"#fff\",\r\n                        display: \"flex\",\r\n                        alignItems: \"flex-start\",\r\n                        mb: 1,\r\n                        gap: 1,\r\n                      }}\r\n                    >\r\n                      {msg.sender?.profilePic\r\n                        ? (\r\n                          <img\r\n                            src={\r\n                              msg.sender?.profilePic?.startsWith('http')\r\n                                ? msg.sender.profilePic\r\n                                : `http://localhost:8000${msg.sender?.profilePic || '/profile-pics/default.png'}`\r\n                            }\r\n                            alt={msg.sender?.name}\r\n                            style={{ width: 32, height: 32, borderRadius: \"50%\", marginRight: 8 }}\r\n                          />\r\n                        ) : (\r\n                          <Avatar sx={{ width: 32, height: 32, marginRight: 1 }}>\r\n                            {msg.sender?.name?.[0]?.toUpperCase() || \"?\"}\r\n                          </Avatar>\r\n                        )\r\n                      }\r\n\r\n                      <Box>\r\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\" color=\"primary\">\r\n                          {msg.sender?.name || t('anonymous')}\r\n                          {msg.sender?.role && (\r\n                            <span style={{ color: \"#888\", fontWeight: 400, marginLeft: 8, fontSize: 13 }}>\r\n                              · {msg.sender.role}\r\n                            </span>\r\n                          )}\r\n                          {msg.createdAt && (\r\n                            <span style={{ color: \"#888\", fontSize: 11, marginLeft: 8 }}>\r\n                              {new Date(msg.createdAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" })}\r\n                            </span>\r\n                          )}{msg.sender?.id === user.id && (\r\n                            <IconButton size=\"small\" onClick={() => handleDeleteMsg(msg.id)} color=\"error\">\r\n                              <DeleteIcon fontSize=\"small\" />\r\n                            </IconButton>\r\n                          )}\r\n                        </Typography>\r\n\r\n                        {/* Message Content */}\r\n                        {msg.type === \"text\" && <span>{msg.content}</span>}\r\n                        {msg.type === \"image\" && (\r\n                          <img src={msg.content} alt=\"img\" style={{ maxWidth: 180, borderRadius: 6, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"audio\" && (\r\n                          <audio controls src={msg.content} style={{ maxWidth: 180, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"video\" && (\r\n                          <video controls src={msg.content} style={{ maxWidth: 180, borderRadius: 6, marginTop: 4 }} />\r\n                        )}\r\n                        {msg.type === \"file\" && (\r\n                          <a href={msg.content} target=\"_blank\" rel=\"noopener noreferrer\" style={{ display: \"block\", marginTop: 4 }}>\r\n                            📎 {msg.content.split(\"/\").pop()}\r\n                          </a>\r\n                        )}\r\n                      </Box>\r\n                    </Paper>\r\n                  ))}\r\n                  <div ref={chatBottomRef} />\r\n                </Stack>\r\n              </Paper>\r\n              <Stack direction=\"row\" spacing={1} alignItems=\"center\">\r\n                <TextField\r\n                  fullWidth\r\n                  value={newMsg}\r\n                  size=\"small\"\r\n                  placeholder={t('writeMessage')}\r\n                  onChange={(e) => setNewMsg(e.target.value)}\r\n                  onKeyDown={(e) => e.key === \"Enter\" && handleChatSend()}\r\n                  sx={{ background: \"#fff\", borderRadius: 1 }}\r\n                />\r\n                <IconButton onClick={() => setShowEmoji((v) => !v)}>\r\n                  <span role=\"img\" aria-label=\"emoji\">😀</span>\r\n                </IconButton>\r\n                <IconButton component=\"label\" color={newFile ? \"success\" : \"primary\"}>\r\n                  <AddPhotoAlternateIcon />\r\n                  <input\r\n                    hidden\r\n                    type=\"file\"\r\n                    accept=\"image/*,video/*,audio/*,application/pdf\"\r\n                    onChange={(e) => setNewFile(e.target.files[0])}\r\n                  />\r\n                </IconButton>\r\n                <Button onClick={handleChatSend} variant=\"contained\" disabled={!newMsg.trim() && !newFile}>\r\n                  {t('send')}\r\n                </Button>\r\n              </Stack>\r\n              {showEmoji && (\r\n                <Box sx={{ position: \"absolute\", zIndex: 11 }}>\r\n                  <EmojiPicker onEmojiClick={handleEmoji} autoFocusSearch={false} />\r\n                </Box>\r\n              )}\r\n              {newFile && (\r\n                <Typography color=\"primary\" fontSize={12} ml={1} mt={0.5}>\r\n                  {t('fileReady')}: {newFile.name}\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 4 - Feedback Formateur */}\r\n          {tab === 4 && (\r\n            <Box>\r\n              <Typography variant=\"h6\" mb={2}>{t('feedbackFormateur')}</Typography>\r\n              <FeedbackFormateur seanceId={seanceId} />\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 5 - Feedback (dynamique) */}\r\n          {showFeedback && tab === 5 && (\r\n            <Box>\r\n              <Stack direction=\"row\" alignItems=\"center\" spacing={2} mb={2}>\r\n                <Typography variant=\"h6\">📝 {t('sessionFeedback')}</Typography>\r\n              </Stack>\r\n              <AddSeanceFeedback seanceId={seanceId} />\r\n            </Box>\r\n          )}\r\n\r\n          {/* Onglet 6 - Feedback List */}\r\n          {tab === 6 && (\r\n            <SeanceFeedbackList />\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Image zoom */}\r\n      {zoomedImage && (\r\n        <Box\r\n          onClick={() => setZoomedImage(null)}\r\n          sx={{\r\n            position: \"fixed\", top: 0, left: 0, zIndex: 2000, width: \"100vw\", height: \"100vh\",\r\n            background: \"rgba(0,0,0,0.88)\", display: \"flex\", alignItems: \"center\",\r\n            justifyContent: \"center\", cursor: \"zoom-out\",\r\n          }}\r\n        >\r\n          <img\r\n            src={zoomedImage}\r\n            alt=\"\"\r\n            style={{ maxWidth: \"92vw\", maxHeight: \"92vh\", borderRadius: 12, boxShadow: \"0 2px 24px #111\" }}\r\n            onClick={(e) => e.stopPropagation()}\r\n          />\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AnimerSeanceView;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,cAAc;AACtC,SACEC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,eAAe,IAAIC,mBAAmB,EACtCC,iBAAiB,IAAIC,qBAAqB,EAC1CC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC7B,MAAM;IAAEC;EAAE,CAAC,GAAGpC,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEqC,EAAE,EAAEC;EAAS,CAAC,GAAGnD,SAAS,CAAC,CAAC;EACpC,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0D,GAAG,EAAEC,MAAM,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,MAAM,EAAEC,SAAS,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8E,MAAM,EAAEC,SAAS,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkF,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoF,aAAa,GAAGnF,MAAM,CAAC,CAAC;EAC9B,MAAM,CAACoF,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAMuF,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACrD,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;;EAE9B;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM8F,CAAC,GAAGvD,EAAE,CAAC,uBAAuB,CAAC;IACrCgD,SAAS,CAACO,CAAC,CAAC;IACZA,CAAC,CAACC,IAAI,CAAC,UAAU,EAAE;MAAEzC,QAAQ,EAAE0C,MAAM,CAAC1C,QAAQ;IAAE,CAAC,CAAC;IAElDwC,CAAC,CAACG,EAAE,CAAC,YAAY,EAAGC,GAAG,IAAK;MAC1BpB,eAAe,CAAEqB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEFJ,CAAC,CAACG,EAAE,CAAC,eAAe,EAAGG,OAAO,IAAK;MACjCtB,eAAe,CAAEqB,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjD,EAAE,KAAK+C,OAAO,CAAC/C,EAAE,CAAC,CAAC;IACpE,CAAC,CAAC;IAEF,OAAO,MAAM;MACXyC,CAAC,CAACS,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACjD,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,QAAQ,EAAE;IACfrC,KAAK,CAACuF,GAAG,CAAC,uCAAuClD,QAAQ,EAAE,CAAC,CACzDmD,IAAI,CAAEC,GAAG,IAAK5B,eAAe,CAAC4B,GAAG,CAACC,IAAI,CAAC,CAAC,CACxCC,KAAK,CAAC,MAAM9B,eAAe,CAAC,EAAE,CAAC,CAAC;EACrC,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,MAAM6G,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QAAA,IAAAC,aAAA;QACF,MAAMJ,GAAG,GAAG,MAAMzF,KAAK,CAACuF,GAAG,CAAC,0CAA0ClD,QAAQ,EAAE,CAAC;QACjF,MAAMyD,IAAI,GAAGL,GAAG,CAACC,IAAI;QACrBnD,SAAS,CAACuD,IAAI,CAAC;QAEf,IAAIA,IAAI,aAAJA,IAAI,gBAAAD,aAAA,GAAJC,IAAI,CAAEC,QAAQ,cAAAF,aAAA,eAAdA,aAAA,CAAgBzD,EAAE,EAAE;UACtB,MAAM4D,SAAS,GAAG,MAAMhG,KAAK,CAACuF,GAAG,CAC/B,kDAAkDO,IAAI,CAACC,QAAQ,CAAC3D,EAAE,EACpE,CAAC;UACDK,iBAAiB,CAACuD,SAAS,CAACN,IAAI,CAAC;QACnC;MACF,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEF,GAAG,CAAC;MACpD;IACF,CAAC;IACDL,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACvD,QAAQ,CAAC,CAAC;;EAEd;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsD,QAAQ,EAAE;IACfrC,KAAK,CAACuF,GAAG,CAAC,0CAA0ClD,QAAQ,QAAQ,CAAC,CAClEmD,IAAI,CAACC,GAAG,IAAI;MACX1C,gBAAgB,CAAC0C,GAAG,CAACC,IAAI,CAACN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACe,IAAI,KAAK,OAAO,CAAC,CAAC;MAC1DnD,gBAAgB,CAACwC,GAAG,CAACC,IAAI,CAACN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACe,IAAI,KAAK,OAAO,CAAC,CAAC;IAC5D,CAAC,CAAC,CACDT,KAAK,CAACM,GAAG,IAAI;MACZC,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEF,GAAG,CAAC;IACjD,CAAC,CAAC;EACN,CAAC,EAAE,CAAC5D,QAAQ,CAAC,CAAC;EAEd,MAAMgE,WAAW,GAAG,MAAAA,CAAOC,IAAI,EAAEF,IAAI,KAAK;IACxC,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7BC,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;IAC7B,MAAMX,GAAG,GAAG,MAAMzF,KAAK,CAAC0G,IAAI,CAC1B,0CAA0CrE,QAAQ,eAAe,EACjEkE,QAAQ,EACR;MAAEI,OAAO,EAAE;QAAE,cAAc,EAAE;MAAsB;IAAE,CACvD,CAAC;IACD,OAAOlB,GAAG,CAACC,IAAI;EACjB,CAAC;EAED,MAAMkB,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CxD,kBAAkB,CAAE6B,IAAI,KAAM;MAC5B,GAAGA,IAAI;MACP,CAAC2B,QAAQ,GAAG,CAAC3B,IAAI,CAAC2B,QAAQ;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvC,IAAIA,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,EAAEC,UAAU,CAACvE,GAAG,CAAC;IACrDC,MAAM,CAACqE,QAAQ,CAAC;EAClB,CAAC;EAED,MAAME,cAAc,GAAG,MAAOH,CAAC,IAAK;IAClC,MAAMT,IAAI,GAAGS,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACd,IAAI,EAAE;IACX,IAAI;MACF,MAAMe,KAAK,GAAG,MAAMhB,WAAW,CAACC,IAAI,EAAE,OAAO,CAAC;MAC9CvD,gBAAgB,CAAEmC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEmC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOpB,GAAG,EAAE;MACZqB,KAAK,CAACnF,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoF,cAAc,GAAG,MAAOR,CAAC,IAAK;IAClC,MAAMT,IAAI,GAAGS,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACd,IAAI,EAAE;IACX,IAAI;MACF,MAAMe,KAAK,GAAG,MAAMhB,WAAW,CAACC,IAAI,EAAE,OAAO,CAAC;MAC9CrD,gBAAgB,CAAEiC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEmC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAOpB,GAAG,EAAE;MACZqB,KAAK,CAACnF,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC/D,SAAS,CAAC,IAAI,CAAC;IACfgE,UAAU,CAAC,MAAMhE,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACxC6D,KAAK,CAACnF,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAChC,CAAC;EAED,MAAMuF,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI;MACF,MAAM3H,KAAK,CAAC4H,KAAK,CAAC,kCAAkCD,SAAS,UAAU,EAAE;QACvEE,SAAS,EAAE;MACb,CAAC,CAAC;MACF,MAAM7B,SAAS,GAAG,MAAMhG,KAAK,CAACuF,GAAG,CAC/B,kDAAkDjD,MAAM,CAACyD,QAAQ,CAAC3D,EAAE,EACtE,CAAC;MACDK,iBAAiB,CAACuD,SAAS,CAACN,IAAI,CAAC;IACnC,CAAC,CAAC,MAAM;MACN4B,KAAK,CAACnF,CAAC,CAAC,mBAAmB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2F,WAAW,GAAIf,CAAC,IAAK;IACzBhD,SAAS,CAAEmB,IAAI,IAAKA,IAAI,GAAG6B,CAAC,CAACgB,KAAK,CAAC;IACnC9D,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM+D,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAC3D,MAAM,EAAE;IACb,IAAIH,OAAO,EAAE;MACX,MAAMqC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvC,OAAO,CAAC;MAChCqC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEpE,QAAQ,CAAC;MACrC,IAAI;QACF,MAAMoD,GAAG,GAAG,MAAMzF,KAAK,CAAC0G,IAAI,CAAC,iDAAiD,EAAEH,QAAQ,EAAE;UACxFI,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QACnD,CAAC,CAAC;QAEFtC,MAAM,CAACS,IAAI,CAAC,aAAa,EAAE;UACzBmD,OAAO,EAAExC,GAAG,CAACC,IAAI,CAACwC,OAAO;UACzB9B,IAAI,EAAEX,GAAG,CAACC,IAAI,CAACyC,QAAQ,IAAI,MAAM;UACjC9F,QAAQ,EAAE0C,MAAM,CAAC1C,QAAQ,CAAC;UAC1B+F,QAAQ,EAAE7D,IAAI,CAACnC;QACjB,CAAC,CAAC;QAEF+B,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,MAAM;QACNmD,KAAK,CAACnF,CAAC,CAAC,iBAAiB,CAAC,CAAC;MAC7B;IACF,CAAC,MAAM,IAAI2B,MAAM,CAACuE,IAAI,CAAC,CAAC,EAAE;MACxBhE,MAAM,CAACS,IAAI,CAAC,aAAa,EAAE;QACzBmD,OAAO,EAAEnE,MAAM;QACfsC,IAAI,EAAE,MAAM;QACZ/D,QAAQ,EAAE0C,MAAM,CAAC1C,QAAQ,CAAC;QAC1B+F,QAAQ,EAAE7D,IAAI,CAACnC;MACjB,CAAC,CAAC;MAEF2B,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMuE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI;MACF,MAAMvI,KAAK,CAACwI,MAAM,CAAC,uCAAuCD,KAAK,EAAE,EAAE;QACjE7C,IAAI,EAAE;UAAE+C,MAAM,EAAElE,IAAI,CAACnC;QAAG;MAC1B,CAAC,CAAC;MACFyB,eAAe,CAAEqB,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjD,EAAE,KAAKmG,KAAK,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZqB,KAAK,CAACnF,CAAC,CAAC,4BAA4B,CAAC,CAAC;IACxC;EACF,CAAC;EAED,MAAMuG,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAClG,cAAc,EAAE,oBAAOT,OAAA,CAAC3C,UAAU;MAAAuJ,QAAA,EAAExG,CAAC,CAAC,gBAAgB;IAAC;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;IAE1E,oBACEhH,OAAA,CAAC5C,GAAG;MAAAwJ,QAAA,gBACF5G,OAAA,CAAC3C,UAAU;QAAC4J,OAAO,EAAC,IAAI;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,GAAC,eAAG,eAAA5G,OAAA;UAAA4G,QAAA,GAASxG,CAAC,CAAC,SAAS,CAAC,EAAC,KAAG,EAACK,cAAc,CAAC0G,OAAO,CAACC,KAAK;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEhHhH,OAAA,CAAC5C,GAAG;QAACiK,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,EACfnG,cAAc,CAAC8G,eAAe,CAACC,GAAG,CAAEC,GAAG,iBACtCzH,OAAA,CAAC5C,GAAG;UAAckK,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACtB5G,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAACS,UAAU,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAhB,QAAA,GAAC,eACvE,EAACa,GAAG,CAACI,MAAM,CAACT,KAAK;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEbhH,OAAA,CAAC5C,GAAG;YAACiK,EAAE,EAAE,CAAE;YAAAT,QAAA,EACRa,GAAG,CAACK,OAAO,CAACN,GAAG,CAAEO,MAAM,iBACtB/H,OAAA,CAAC5C,GAAG;cAAiBkK,EAAE,EAAE,CAAE;cAAAV,QAAA,gBACzB5G,OAAA,CAACpC,KAAK;gBAACoK,SAAS,EAAC,KAAK;gBAACC,UAAU,EAAC,QAAQ;gBAACC,OAAO,EAAE,CAAE;gBAAAtB,QAAA,gBACpD5G,OAAA,CAAC3C,UAAU;kBAAC4J,OAAO,EAAC,OAAO;kBAACS,UAAU,EAAC,MAAM;kBAACC,EAAE,EAAE;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAhB,QAAA,GAAC,eACnE,EAACmB,MAAM,CAACA,MAAM,CAACX,KAAK;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACbhH,OAAA,CAACtC,MAAM;kBACLyK,IAAI,EAAC,OAAO;kBACZlB,OAAO,EAAC,UAAU;kBAClBmB,OAAO,EAAEA,CAAA,KAAMvD,sBAAsB,CAACkD,MAAM,CAAC1H,EAAE,CAAE;kBAAAuG,QAAA,EAEhDvF,eAAe,CAAC0G,MAAM,CAAC1H,EAAE,CAAC,GAAGD,CAAC,CAAC,MAAM,CAAC,GAAGA,CAAC,CAAC,MAAM;gBAAC;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAERhH,OAAA,CAACrC,QAAQ;gBAAC0K,EAAE,EAAEhH,eAAe,CAAC0G,MAAM,CAAC1H,EAAE,CAAE;gBAAAuG,QAAA,EACtCmB,MAAM,CAACO,QAAQ,CAACd,GAAG,CAAEe,EAAE;kBAAA,IAAAC,cAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;kBAAA,oBACtB5I,OAAA,CAAC5C,GAAG;oBAAkCyL,OAAO,EAAC,MAAM;oBAACZ,UAAU,EAAC,QAAQ;oBAACa,GAAG,EAAE,CAAE;oBAACC,QAAQ,EAAC,MAAM;oBAACzB,EAAE,EAAE,CAAE;oBAAAV,QAAA,gBACrG5G,OAAA,CAACvC,IAAI;sBACHuL,IAAI,eAAEhJ,OAAA,CAACtB,mBAAmB;wBAACiJ,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,EAAE;0BAAErB,KAAK,EAAEW,EAAE,CAACW,OAAO,CAACpD,SAAS,GAAG,SAAS,GAAG;wBAAU;sBAAE;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzGmC,KAAK,EAAEZ,EAAE,CAACW,OAAO,CAAC9B,KAAM;sBACxBH,OAAO,EAAC,UAAU;sBAClBmB,OAAO,EAAEA,CAAA;wBAAA,IAAAgB,YAAA;wBAAA,OAAM,EAAAA,YAAA,GAAAb,EAAE,CAACW,OAAO,cAAAE,YAAA,uBAAVA,YAAA,CAAYjD,OAAO,KAAIkD,MAAM,CAACC,IAAI,CAACf,EAAE,CAACW,OAAO,CAAC/C,OAAO,EAAE,QAAQ,CAAC;sBAAA,CAAC;sBAChFwB,EAAE,EAAE;wBACF4B,MAAM,EAAE,CAAAb,YAAA,GAAAH,EAAE,CAACW,OAAO,cAAAR,YAAA,eAAVA,YAAA,CAAYvC,OAAO,GAAG,SAAS,GAAG,SAAS;wBACnDqD,WAAW,EAAEjB,EAAE,CAACW,OAAO,CAACpD,SAAS,GAAG,SAAS,GAAG,SAAS;wBACzD8B,KAAK,EAAEW,EAAE,CAACW,OAAO,CAACpD,SAAS,GAAG,SAAS,GAAG,SAAS;wBACnD4B,UAAU,EAAE,MAAM;wBAClB+B,QAAQ,EAAE,GAAG;wBACbC,cAAc,EAAE;sBAClB;oBAAE;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFhH,OAAA,CAACtC,MAAM;sBACLyK,IAAI,EAAC,OAAO;sBACZlB,OAAO,EAAC,UAAU;sBAClBW,KAAK,EAAE,CAAAe,YAAA,GAAAJ,EAAE,CAACW,OAAO,cAAAP,YAAA,eAAVA,YAAA,CAAY7C,SAAS,GAAG,SAAS,GAAG,SAAU;sBACrDsC,OAAO,EAAEA,CAAA;wBAAA,IAAAuB,YAAA;wBAAA,OAAMhE,oBAAoB,EAAAgE,YAAA,GAACpB,EAAE,CAACW,OAAO,cAAAS,YAAA,uBAAVA,YAAA,CAAYtJ,EAAE,CAAC;sBAAA,CAAC;sBAAAuG,QAAA,EAEnD,CAAAgC,YAAA,GAAAL,EAAE,CAACW,OAAO,cAAAN,YAAA,eAAVA,YAAA,CAAY9C,SAAS,GAAG1F,CAAC,CAAC,WAAW,CAAC,GAAGA,CAAC,CAAC,SAAS;oBAAC;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA,IAAAwB,cAAA,IAAAC,WAAA,GAtBDF,EAAE,CAACW,OAAO,cAAAT,WAAA,uBAAVA,WAAA,CAAYpI,EAAE,cAAAmI,cAAA,cAAAA,cAAA,GAAIlJ,MAAM,CAAC,CAAC;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuB/B,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,GAzCHe,MAAM,CAAC1H,EAAE;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Cd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAnDES,GAAG,CAACpH,EAAE;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAI,CAACzG,MAAM,EAAE,oBAAOP,OAAA,CAAC3C,UAAU;IAAAuJ,QAAA,EAAExG,CAAC,CAAC,gBAAgB;EAAC;IAAAyG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAa,CAAC;EAElE,oBACEhH,OAAA,CAAC5C,GAAG;IAACwM,CAAC,EAAE,CAAE;IAAAhD,QAAA,gBAER5G,OAAA,CAACxC,KAAK;MAACmK,EAAE,EAAE;QACTT,EAAE,EAAE,CAAC;QAAE0C,CAAC,EAAE,CAAC;QAAEC,UAAU,EAAE,SAAS;QAAEC,SAAS,EAAE,MAAM;QACrDjB,OAAO,EAAE,MAAM;QAAEZ,UAAU,EAAE,QAAQ;QAAEyB,cAAc,EAAE,QAAQ;QAC/DK,MAAM,EAAE,mBAAmB;QAAEC,QAAQ,EAAE;MACzC,CAAE;MAAApD,QAAA,eACA5G,OAAA;QACEiK,GAAG,EAAE,0BAA0B1J,MAAM,CAAC6G,KAAK,IAAI,cAAc,EAAG;QAChE8C,KAAK,EAAC,iDAAiD;QACvDC,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEN,MAAM,EAAE;QAAO,CAAE;QACzD3C,KAAK,EAAEhH,CAAC,CAAC,cAAc;MAAE;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRhH,OAAA,CAACxC,KAAK;MAACmK,EAAE,EAAE;QAAEiC,CAAC,EAAE,CAAC;QAAE1C,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzB5G,OAAA,CAACpC,KAAK;QAACoK,SAAS,EAAC,KAAK;QAACC,UAAU,EAAC,QAAQ;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,gBACpD5G,OAAA,CAACvC,IAAI;UAAC0L,KAAK,EAAE,GAAG/I,CAAC,CAAC,SAAS,CAAC,MAAM,CAAAK,cAAc,aAAdA,cAAc,wBAAAN,qBAAA,GAAdM,cAAc,CAAE0G,OAAO,cAAAhH,qBAAA,uBAAvBA,qBAAA,CAAyBiH,KAAK,KAAI,EAAE,EAAG;UAACQ,KAAK,EAAC;QAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzFhH,OAAA,CAACtC,MAAM;UACL4M,SAAS,eAAEtK,OAAA,CAACd,aAAa;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BoB,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CoG,OAAO,EAAC,UAAU;UAClBkB,IAAI,EAAC,OAAO;UAAAvB,QAAA,EAEX/F,YAAY,GAAGT,CAAC,CAAC,eAAe,CAAC,GAAGA,CAAC,CAAC,eAAe;QAAC;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACThH,OAAA,CAACtC,MAAM;UACL4M,SAAS,eAAEtK,OAAA,CAACZ,YAAY;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BoB,OAAO,EAAEA,CAAA,KAAMxG,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CsF,OAAO,EAAEtF,YAAY,GAAG,UAAU,GAAG,WAAY;UACjDiG,KAAK,EAAC,WAAW;UACjBO,IAAI,EAAC,OAAO;UAAAvB,QAAA,EAEXjF,YAAY,GAAGvB,CAAC,CAAC,cAAc,CAAC,GAAGA,CAAC,CAAC,cAAc;QAAC;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRhH,OAAA,CAACrC,QAAQ;QAAC0K,EAAE,EAAExH,YAAa;QAAA+F,QAAA,gBACzB5G,OAAA,CAACjC,OAAO;UAAC4J,EAAE,EAAE;YAAE4C,EAAE,EAAE;UAAE;QAAE;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzBL,sBAAsB,CAAC,CAAC;MAAA;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGRhH,OAAA,CAAC5C,GAAG;MAACyL,OAAO,EAAC,MAAM;MAACvB,EAAE,EAAE,CAAE;MAAAV,QAAA,gBACxB5G,OAAA,CAAC1C,IAAI;QAACkN,WAAW,EAAC,UAAU;QAACC,KAAK,EAAE9J,GAAI;QAAC+J,QAAQ,EAAE3F,eAAgB;QAAC4C,EAAE,EAAE;UAAEgD,WAAW,EAAE,CAAC;UAAEnB,WAAW,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAA7C,QAAA,gBAChI5G,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAAC5B,eAAe;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,kBAAkB;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrFhH,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAAC1B,QAAQ;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,YAAY;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxEhH,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAACxB,QAAQ;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,WAAW;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEhH,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAACtB,mBAAmB;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,YAAY,CAAE;UAACgI,OAAO,EAAEA,CAAA,KAAMvF,QAAQ,CAAC,eAAevC,QAAQ,EAAE;QAAE;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvIhH,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAACZ,YAAY;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,mBAAmB;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClFrF,YAAY,iBACX3B,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAACZ,YAAY;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,UAAU;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC1E,eACDhH,OAAA,CAACzC,GAAG;UAACyL,IAAI,eAAEhJ,OAAA,CAACZ,YAAY;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC4D,YAAY,EAAC,OAAO;UAACzB,KAAK,EAAE/I,CAAC,CAAC,cAAc;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eAEPhH,OAAA,CAAC5C,GAAG;QAACyN,IAAI,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlE,QAAA,GAEjBjG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAwJ,QAAA,gBACF5G,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,GAC5BxG,CAAC,CAAC,eAAe,CAAC,eACnBJ,OAAA,CAAClC,UAAU;cAAC8J,KAAK,EAAC,SAAS;cAACmD,SAAS,EAAC,OAAO;cAAAnE,QAAA,gBAC3C5G,OAAA,CAACpB,qBAAqB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBhH,OAAA;gBAAOqE,IAAI,EAAC,MAAM;gBAAC2G,MAAM,EAAC,SAAS;gBAACC,MAAM;gBAACP,QAAQ,EAAEvF;cAAe;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACbhH,OAAA,CAACpC,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACa,QAAQ,EAAC,MAAM;YAAAnC,QAAA,EAC/C7F,aAAa,CAACyG,GAAG,CAAE0D,GAAG,iBACrBlL,OAAA;cAEEiK,GAAG,EAAEiB,GAAG,CAAC/E,OAAQ;cACjBgF,GAAG,EAAC,EAAE;cACNhB,KAAK,EAAE;gBAAEiB,SAAS,EAAE,GAAG;gBAAEC,MAAM,EAAE,CAAC;gBAAE9B,MAAM,EAAE,SAAS;gBAAE+B,YAAY,EAAE,CAAC;gBAAEC,SAAS,EAAE;cAAiB,CAAE;cACtGnD,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC8J,GAAG,CAAC/E,OAAO;YAAE,GAJtC+E,GAAG,CAAC7K,EAAE;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKZ,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERhH,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,GAC5BxG,CAAC,CAAC,eAAe,CAAC,eACnBJ,OAAA,CAAClC,UAAU;cAAC8J,KAAK,EAAC,SAAS;cAACmD,SAAS,EAAC,OAAO;cAAAnE,QAAA,gBAC3C5G,OAAA,CAAClB,SAAS;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACbhH,OAAA;gBAAOqE,IAAI,EAAC,MAAM;gBAAC2G,MAAM,EAAC,SAAS;gBAACC,MAAM;gBAACP,QAAQ,EAAElF;cAAe;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACbhH,OAAA,CAACpC,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACa,QAAQ,EAAC,MAAM;YAAAnC,QAAA,EAC/C3F,aAAa,CAACuG,GAAG,CAAEgE,GAAG,iBACrBxL,OAAA,CAAC5C,GAAG;cAAcuK,EAAE,EAAE;gBAAEyC,KAAK,EAAE;cAAI,CAAE;cAAAxD,QAAA,eACnC5G,OAAA,CAAC9B,WAAW;gBAACuN,GAAG,EAAED,GAAG,CAACrF,OAAQ;gBAACuF,QAAQ;gBAACtB,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE;cAAI;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAD5DwE,GAAG,CAACnL,EAAE;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAERhH,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACK,EAAE,EAAE,CAAE;YAAAV,QAAA,EAAExG,CAAC,CAAC,cAAc;UAAC;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChEhH,OAAA,CAACnC,SAAS;YACR8N,SAAS;YAACC,SAAS;YAACC,OAAO,EAAE,CAAE;YAC/BC,WAAW,EAAE1L,CAAC,CAAC,kBAAkB,CAAE;YACnCqK,KAAK,EAAElJ,YAAa;YACpBmJ,QAAQ,EAAG1F,CAAC,IAAKxD,eAAe,CAACwD,CAAC,CAACI,MAAM,CAACqF,KAAK,CAAE;YACjD9C,EAAE,EAAE;cAAE4C,EAAE,EAAE;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFhH,OAAA,CAACtC,MAAM;YAAC4M,SAAS,eAAEtK,OAAA,CAAChB,QAAQ;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAACmB,OAAO,EAAE3C,iBAAkB;YAACsG,QAAQ,EAAEtK,MAAO;YAAAmF,QAAA,EAC/FnF,MAAM,GAAGrB,CAAC,CAAC,QAAQ,CAAC,GAAGA,CAAC,CAAC,aAAa;UAAC;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGArG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC3C,UAAU;UAACuK,KAAK,EAAC,gBAAgB;UAAAhB,QAAA,GAAC,eAAG,EAACxG,CAAC,CAAC,aAAa,CAAC;QAAA;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CACrE,EAGArG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAwJ,QAAA,gBACF5G,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,GAAC,eAAG,EAACxG,CAAC,CAAC,aAAa,CAAC;UAAA;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClEhH,OAAA,CAACxC,KAAK;YAACmK,EAAE,EAAE;cACTiC,CAAC,EAAE,CAAC;cAAE1C,EAAE,EAAE,CAAC;cAAEkE,SAAS,EAAE,GAAG;cAAEtB,SAAS,EAAE,GAAG;cAAEkC,SAAS,EAAE,MAAM;cAC9DjC,MAAM,EAAE,gBAAgB;cAAEuB,YAAY,EAAE,CAAC;cAAEzB,UAAU,EAAE;YACzD,CAAE;YAAAjD,QAAA,eACA5G,OAAA,CAACpC,KAAK;cAACsK,OAAO,EAAE,CAAE;cAAAtB,QAAA,GACf/E,YAAY,CAAC2F,GAAG,CAAC,CAACtE,GAAG,EAAE+I,CAAC;gBAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;gBAAA,oBACvB5M,OAAA,CAACxC,KAAK;kBAEJmK,EAAE,EAAE;oBACFiC,CAAC,EAAE,CAAC;oBACJC,UAAU,EAAE,MAAM;oBAClBhB,OAAO,EAAE,MAAM;oBACfZ,UAAU,EAAE,YAAY;oBACxBf,EAAE,EAAE,CAAC;oBACL4B,GAAG,EAAE;kBACP,CAAE;kBAAAlC,QAAA,GAED,CAAAsF,WAAA,GAAAhJ,GAAG,CAAC2J,MAAM,cAAAX,WAAA,eAAVA,WAAA,CAAYY,UAAU,gBAEnB9M,OAAA;oBACEiK,GAAG,EACD,CAAAkC,YAAA,GAAAjJ,GAAG,CAAC2J,MAAM,cAAAV,YAAA,gBAAAC,qBAAA,GAAVD,YAAA,CAAYW,UAAU,cAAAV,qBAAA,eAAtBA,qBAAA,CAAwBW,UAAU,CAAC,MAAM,CAAC,GACtC7J,GAAG,CAAC2J,MAAM,CAACC,UAAU,GACrB,wBAAwB,EAAAT,YAAA,GAAAnJ,GAAG,CAAC2J,MAAM,cAAAR,YAAA,uBAAVA,YAAA,CAAYS,UAAU,KAAI,2BAA2B,EAClF;oBACD3B,GAAG,GAAAmB,YAAA,GAAEpJ,GAAG,CAAC2J,MAAM,cAAAP,YAAA,uBAAVA,YAAA,CAAYU,IAAK;oBACtB7C,KAAK,EAAE;sBAAEC,KAAK,EAAE,EAAE;sBAAEC,MAAM,EAAE,EAAE;sBAAEiB,YAAY,EAAE,KAAK;sBAAE2B,WAAW,EAAE;oBAAE;kBAAE;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,gBAEFhH,OAAA,CAACP,MAAM;oBAACkI,EAAE,EAAE;sBAAEyC,KAAK,EAAE,EAAE;sBAAEC,MAAM,EAAE,EAAE;sBAAE4C,WAAW,EAAE;oBAAE,CAAE;oBAAArG,QAAA,EACnD,EAAA2F,YAAA,GAAArJ,GAAG,CAAC2J,MAAM,cAAAN,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYS,IAAI,cAAAR,iBAAA,wBAAAC,kBAAA,GAAhBD,iBAAA,CAAmB,CAAC,CAAC,cAAAC,kBAAA,uBAArBA,kBAAA,CAAuBS,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CACT,eAGHhH,OAAA,CAAC5C,GAAG;oBAAAwJ,QAAA,gBACF5G,OAAA,CAAC3C,UAAU;sBAAC4J,OAAO,EAAC,WAAW;sBAACS,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAhB,QAAA,GAC9D,EAAA8F,YAAA,GAAAxJ,GAAG,CAAC2J,MAAM,cAAAH,YAAA,uBAAVA,YAAA,CAAYM,IAAI,KAAI5M,CAAC,CAAC,WAAW,CAAC,EAClC,EAAAuM,YAAA,GAAAzJ,GAAG,CAAC2J,MAAM,cAAAF,YAAA,uBAAVA,YAAA,CAAYQ,IAAI,kBACfnN,OAAA;wBAAMmK,KAAK,EAAE;0BAAEvC,KAAK,EAAE,MAAM;0BAAEF,UAAU,EAAE,GAAG;0BAAE0F,UAAU,EAAE,CAAC;0BAAEnE,QAAQ,EAAE;wBAAG,CAAE;wBAAArC,QAAA,GAAC,OAC1E,EAAC1D,GAAG,CAAC2J,MAAM,CAACM,IAAI;sBAAA;wBAAAtG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CACP,EACA9D,GAAG,CAACmK,SAAS,iBACZrN,OAAA;wBAAMmK,KAAK,EAAE;0BAAEvC,KAAK,EAAE,MAAM;0BAAEqB,QAAQ,EAAE,EAAE;0BAAEmE,UAAU,EAAE;wBAAE,CAAE;wBAAAxG,QAAA,EACzD,IAAI0G,IAAI,CAACpK,GAAG,CAACmK,SAAS,CAAC,CAACE,kBAAkB,CAAC,EAAE,EAAE;0BAAEC,IAAI,EAAE,SAAS;0BAAEC,MAAM,EAAE;wBAAU,CAAC;sBAAC;wBAAA5G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CACP,EAAE,EAAA4F,YAAA,GAAA1J,GAAG,CAAC2J,MAAM,cAAAD,YAAA,uBAAVA,YAAA,CAAYvM,EAAE,MAAKmC,IAAI,CAACnC,EAAE,iBAC3BL,OAAA,CAAClC,UAAU;wBAACqK,IAAI,EAAC,OAAO;wBAACC,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAACrD,GAAG,CAAC7C,EAAE,CAAE;wBAACuH,KAAK,EAAC,OAAO;wBAAAhB,QAAA,eAC5E5G,OAAA,CAACN,UAAU;0BAACuJ,QAAQ,EAAC;wBAAO;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC,EAGZ9D,GAAG,CAACmB,IAAI,KAAK,MAAM,iBAAIrE,OAAA;sBAAA4G,QAAA,EAAO1D,GAAG,CAACgD;oBAAO;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACjD9D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnBrE,OAAA;sBAAKiK,GAAG,EAAE/G,GAAG,CAACgD,OAAQ;sBAACiF,GAAG,EAAC,KAAK;sBAAChB,KAAK,EAAE;wBAAEuD,QAAQ,EAAE,GAAG;wBAAEpC,YAAY,EAAE,CAAC;wBAAEqC,SAAS,EAAE;sBAAE;oBAAE;sBAAA9G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC5F,EACA9D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnBrE,OAAA;sBAAO0L,QAAQ;sBAACzB,GAAG,EAAE/G,GAAG,CAACgD,OAAQ;sBAACiE,KAAK,EAAE;wBAAEuD,QAAQ,EAAE,GAAG;wBAAEC,SAAS,EAAE;sBAAE;oBAAE;sBAAA9G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC5E,EACA9D,GAAG,CAACmB,IAAI,KAAK,OAAO,iBACnBrE,OAAA;sBAAO0L,QAAQ;sBAACzB,GAAG,EAAE/G,GAAG,CAACgD,OAAQ;sBAACiE,KAAK,EAAE;wBAAEuD,QAAQ,EAAE,GAAG;wBAAEpC,YAAY,EAAE,CAAC;wBAAEqC,SAAS,EAAE;sBAAE;oBAAE;sBAAA9G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC7F,EACA9D,GAAG,CAACmB,IAAI,KAAK,MAAM,iBAClBrE,OAAA;sBAAG4N,IAAI,EAAE1K,GAAG,CAACgD,OAAQ;sBAACd,MAAM,EAAC,QAAQ;sBAACyI,GAAG,EAAC,qBAAqB;sBAAC1D,KAAK,EAAE;wBAAEtB,OAAO,EAAE,OAAO;wBAAE8E,SAAS,EAAE;sBAAE,CAAE;sBAAA/G,QAAA,GAAC,eACtG,EAAC1D,GAAG,CAACgD,OAAO,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;oBAAA;sBAAAlH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA/DDiF,CAAC;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgED,CAAC;cAAA,CACT,CAAC,eACFhH,OAAA;gBAAKgO,GAAG,EAAE3L;cAAc;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRhH,OAAA,CAACpC,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACE,OAAO,EAAE,CAAE;YAACD,UAAU,EAAC,QAAQ;YAAArB,QAAA,gBACpD5G,OAAA,CAACnC,SAAS;cACR8N,SAAS;cACTlB,KAAK,EAAE1I,MAAO;cACdoG,IAAI,EAAC,OAAO;cACZ2D,WAAW,EAAE1L,CAAC,CAAC,cAAc,CAAE;cAC/BsK,QAAQ,EAAG1F,CAAC,IAAKhD,SAAS,CAACgD,CAAC,CAACI,MAAM,CAACqF,KAAK,CAAE;cAC3CwD,SAAS,EAAGjJ,CAAC,IAAKA,CAAC,CAACkJ,GAAG,KAAK,OAAO,IAAIjI,cAAc,CAAC,CAAE;cACxD0B,EAAE,EAAE;gBAAEkC,UAAU,EAAE,MAAM;gBAAEyB,YAAY,EAAE;cAAE;YAAE;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACFhH,OAAA,CAAClC,UAAU;cAACsK,OAAO,EAAEA,CAAA,KAAMlG,YAAY,CAAEiM,CAAC,IAAK,CAACA,CAAC,CAAE;cAAAvH,QAAA,eACjD5G,OAAA;gBAAMmN,IAAI,EAAC,KAAK;gBAAC,cAAW,OAAO;gBAAAvG,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACbhH,OAAA,CAAClC,UAAU;cAACiN,SAAS,EAAC,OAAO;cAACnD,KAAK,EAAEzF,OAAO,GAAG,SAAS,GAAG,SAAU;cAAAyE,QAAA,gBACnE5G,OAAA,CAACpB,qBAAqB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBhH,OAAA;gBACEiL,MAAM;gBACN5G,IAAI,EAAC,MAAM;gBACX2G,MAAM,EAAC,yCAAyC;gBAChDN,QAAQ,EAAG1F,CAAC,IAAK5C,UAAU,CAAC4C,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;cAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACbhH,OAAA,CAACtC,MAAM;cAAC0K,OAAO,EAAEnC,cAAe;cAACgB,OAAO,EAAC,WAAW;cAAC8E,QAAQ,EAAE,CAAChK,MAAM,CAACuE,IAAI,CAAC,CAAC,IAAI,CAACnE,OAAQ;cAAAyE,QAAA,EACvFxG,CAAC,CAAC,MAAM;YAAC;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACP/E,SAAS,iBACRjC,OAAA,CAAC5C,GAAG;YAACuK,EAAE,EAAE;cAAEyG,QAAQ,EAAE,UAAU;cAAEC,MAAM,EAAE;YAAG,CAAE;YAAAzH,QAAA,eAC5C5G,OAAA,CAACR,WAAW;cAAC8O,YAAY,EAAEvI,WAAY;cAACwI,eAAe,EAAE;YAAM;cAAA1H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACN,EACA7E,OAAO,iBACNnC,OAAA,CAAC3C,UAAU;YAACuK,KAAK,EAAC,SAAS;YAACqB,QAAQ,EAAE,EAAG;YAAC5B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,GAAI;YAAAV,QAAA,GACtDxG,CAAC,CAAC,WAAW,CAAC,EAAC,IAAE,EAAC+B,OAAO,CAAC6K,IAAI;UAAA;YAAAnG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGArG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAAC5C,GAAG;UAAAwJ,QAAA,gBACF5G,OAAA,CAAC3C,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,EAAExG,CAAC,CAAC,mBAAmB;UAAC;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrEhH,OAAA,CAACH,iBAAiB;YAACS,QAAQ,EAAEA;UAAS;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACN,EAGArF,YAAY,IAAIhB,GAAG,KAAK,CAAC,iBACxBX,OAAA,CAAC5C,GAAG;UAAAwJ,QAAA,gBACF5G,OAAA,CAACpC,KAAK;YAACoK,SAAS,EAAC,KAAK;YAACC,UAAU,EAAC,QAAQ;YAACC,OAAO,EAAE,CAAE;YAAChB,EAAE,EAAE,CAAE;YAAAN,QAAA,eAC3D5G,OAAA,CAAC3C,UAAU;cAAC4J,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,eAAG,EAACxG,CAAC,CAAC,iBAAiB,CAAC;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACRhH,OAAA,CAACJ,iBAAiB;YAACU,QAAQ,EAAEA;UAAS;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACN,EAGArG,GAAG,KAAK,CAAC,iBACRX,OAAA,CAACF,kBAAkB;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACtB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7F,WAAW,iBACVnB,OAAA,CAAC5C,GAAG;MACFgL,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,IAAI,CAAE;MACpCuG,EAAE,EAAE;QACFyG,QAAQ,EAAE,OAAO;QAAEI,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEJ,MAAM,EAAE,IAAI;QAAEjE,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE,OAAO;QACjFR,UAAU,EAAE,kBAAkB;QAAEhB,OAAO,EAAE,MAAM;QAAEZ,UAAU,EAAE,QAAQ;QACrEyB,cAAc,EAAE,QAAQ;QAAEH,MAAM,EAAE;MACpC,CAAE;MAAA3C,QAAA,eAEF5G,OAAA;QACEiK,GAAG,EAAE9I,WAAY;QACjBgK,GAAG,EAAC,EAAE;QACNhB,KAAK,EAAE;UAAEuD,QAAQ,EAAE,MAAM;UAAEtC,SAAS,EAAE,MAAM;UAAEE,YAAY,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAkB,CAAE;QAC/FnD,OAAO,EAAGpD,CAAC,IAAKA,CAAC,CAAC0J,eAAe,CAAC;MAAE;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAtiBID,gBAAgB;EAAA,QACNjC,cAAc,EACHb,SAAS,EAmBjBwC,WAAW;AAAA;AAAAgP,EAAA,GArBxB1O,gBAAgB;AAwiBtB,eAAeA,gBAAgB;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}